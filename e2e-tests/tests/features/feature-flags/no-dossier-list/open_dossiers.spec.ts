import { test, expect } from '../../../../src/lib/base_Test';
import testData from '../../../../src/lib/testData/testData.json'

test(
  'Verify no dossier list appear',
  { tag: '@noDossierList' },
  async ({ page, DashboardWithToken, account }) => {
    const dashboard = await DashboardWithToken(account.token);
    await dashboard.goto();
    await expect(page).toHaveTitle(testData.dossierTitle);
    // await expect(
    //   page.getByText('Dossiers werden geladen. Bitte warten...')
    // ).toBeVisible();

    /*await dashboard.clickDossier(dossier1.name!);
  await dossierDocumentsPage.clickDocument('Pass CH Andreas Dominik»');
  await expect(
    page.getByText('<PERSON>»', { exact: true })
  ).toBeVisible();*/
  }
);
