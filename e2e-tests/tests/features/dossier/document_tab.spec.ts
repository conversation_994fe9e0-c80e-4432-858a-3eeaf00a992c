import { test } from '../../../src/lib/base_Test';
import testData from '../../../src/lib/testData/testData.json'

const user = process.env.E2E_USER;
const password = process.env.E2E_PASSWORD;

test.describe('Processing Detail Modal Tests', () => {
  test.beforeEach(async ({ page, LoginPage, DashboardPage }) => {
    await page.goto('/');
    await LoginPage.login(`${user}`, `${password}`);
    await DashboardPage.goto();
    await DashboardPage.clickDossier(testData.validDossierName);
  });

  test(
    'Test clicking Detail links opens Processing Detail modal',
    { tag: '@openProcessingDetailModal' },
    async ({ DocumentTab }) => {
      await DocumentTab.openDetailModalAndVerify();
    }
  );

  test(
    'Test Download Processing Detail File',
    { tag: '@downloadProcessingDetailModal' },
    async ({ DocumentTab }) => {
      await DocumentTab.openDetailModalAndVerify();
      await DocumentTab.downloadProcessingDetailFileAndVerify();
    }
  );
});
