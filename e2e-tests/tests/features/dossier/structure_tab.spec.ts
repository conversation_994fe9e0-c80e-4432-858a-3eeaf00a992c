
import {test} from '../../../src/lib/base_Test';
import { StructureTab} from '../../../src/page_factory/structure_tab'
import testData from '../../../src/lib/testData/testData.json'
const user = process.env.E2E_USER
const password = process.env.E2E_PASSWORD;
test(
  'Verify user can navigate to Structure tab',
  { tag: '@navigateToStructureTab' },
  async ({
    page , LoginPage, DashboardPage // this is needed to get a dossier
  }) => {
    await page.goto('/');
    await LoginPage.login(`${user}`, `${password}`);
    await DashboardPage.goto();
    await DashboardPage.clickDossier(testData.validDossierName)
    const structurePage = new StructureTab(page);

  await structurePage.openStructureTab();
  await structurePage.verifyCategoriesCount(7);
  await structurePage.verifyDocumentRowsVisible(12);
});