import { uploadFile } from '../../../src/helper/common';
import { test, expect } from '../../../src/lib/base_Test';
import testData from '../../../src/lib/testData/testData.json'
import path from 'path';
const user = process.env.E2E_USER
const password = process.env.E2E_PASSWORD;
test.describe('Document Categorization & Actions', () => {
  //In-Progress
  test("Verify that uploaded documents are auto-categorized ", { tag: "@docAutoCategorization" }, async ({ page, DashboardWithToken,DocumentTab,account,DossierPage }) => {
    const dashboard = await DashboardWithToken(account.token);
        await dashboard.goto();
        const dossierName = testData.DossierName;
        await dashboard.createNewDossier(dossierName);
        await dashboard.clickDossier(dossierName);
        await uploadFile(page, DossierPage.selectors.dossierFileUploadInput, 'batch_05.pdf');
        await DocumentTab.expectCategoryVisible('person');
        await DocumentTab.expectAllElementsVisible()
  
  
  })


  test("Test behavior when the document doesn't match any category. ", { tag: "@notMatchAnyCat" }, async ({ page, DashboardWithToken,DocumentTab,account,DossierPage },) => {

  const dashboard = await DashboardWithToken(account.token);
  await dashboard.goto();
  const dossierName = testData.DossierName;
  await dashboard.createNewDossier(dossierName);
  await dashboard.clickDossier(dossierName);
  await uploadFile(page, DossierPage.selectors.dossierFileUploadInput, 'images.jpg');
  await DocumentTab.assertUnmatchedCategory()
  })



  // });
  // Precondition: Multiple document types (e.g., Passport, Salary Statement) are uploaded
  // Step 1: Upload various document types
  // Step 2: Allow system auto-classification
  // Step 3: Check displayed categories (Person, Finances, Property)
  // Expected Result: Documents are correctly classified with clear category labels.
  // TODO: Implement test logic here
}
);


//   test('Verify Manual and Automated Document Splitting', async ({    page }) => {
//     // Precondition: A merged document containing multiple sections is uploaded
//     // Step 1: Automated - Enable auto-splitting and wait
//     // Step 2: Manual - Open document, drag & drop pages to split
//     // Step 3: Confirm classifications
//     // Expected Result: Document is split correctly (both manually and automatically) with proper classification.
//     // TODO: Implement test logic here
//   });

// test(
//   'open one dossierVerify Manual and Automated Document Splitting',
//   { tag: '@manualSplitting' },
//   async ({
//     page,
//     account,
//     DashboardWithToken,
//     dossier1 // this is needed to get a dossier
//   }) => {
//     const dashboard = await DashboardWithToken(account.token);
//     await dashboard.goto();
//     await expect(page).toHaveTitle('Dossier Manager');
//     await dashboard.clickDossier(dossier1.name!);
//     const source = await page.locator('[data-handler-id="T0"]'); // replace with your source locator
//   const target = await page.locator('[data-handler-id="T3"]'); // replace with your target locator

//   const sourceBox = await source.boundingBox();
//   const targetBox = await target.boundingBox();
//   if(sourceBox && targetBox){
//     await source.hover()
//   await page.mouse.down();
//   await page.waitForTimeout(2000);
//   await page.mouse.move(targetBox.x + targetBox.width / 2, targetBox.y + targetBox.height / 2);
//   await page.mouse.up();
//   }
  
// });


    
  
//   test('Verify Merging of Document Pages', async ({ page }) => {
//     // Precondition: Multiple related pages exist as individual documents
//     // Step 1: Select multiple pages from the same category
//     // Step 2: Click the 'Merge' button
//     // Expected Result: Selected pages merge into a single document with correct classification and order.
//     // TODO: Implement test logic here
//   });



test('Verify Deletion of Irrelevant Documents', {tag:"@deleteIrreleventData"},async ({page ,DossierPage,DashboardWithToken,account, DossierPageTabsNavigation}) => {
    
  //   test('Verify Deletion of Irrelevant Documents', async ({ page }) => {
//     // Precondition: Duplicate or irrelevant documents exist
//     // Step 1: Identify a duplicate/irrelevant document
//     // Step 2: Click 'Delete' and confirm
//     // Step 3: Refresh the view
//     // Expected Result: The document is removed from the dossier without errors.
//     // TODO: Implement test logic here
//   });
        const dashboard = await DashboardWithToken(account.token);
          await dashboard.goto();
      
          const dossierName = testData.DossierName;
          await dashboard.createNewDossier(dossierName);
          await dashboard.clickDossier(dossierName);
      
      
          await test.step('Upload a document into dossier', async () => {
            await uploadFile(page, DossierPage.selectors.dossierFileUploadInput, 'bankbekb.pdf');
          });
      
          await test.step('Assert file is being processed (check document container)', async () => {
            const docContainer = page.locator(DossierPage.selectors.dossierTotalImages);
            await page.waitForSelector(DossierPage.selectors.dossierTotalImages)
            await expect(docContainer, testData.dashboard.documentUploadAndProcessing.documentContainerVisibleTxt).toBeVisible();
          });
      
          await test.step('Assert uploaded document appears in the document list', async () => {
            const list = page.locator(DossierPage.selectors.dossierTotalImages);
            await expect(list, testData.dashboard.documentUploadAndProcessing.documentList).toBeVisible();
            const count = await list.count();
            expect(count).toBeGreaterThan(0); 
          });

          
          await DossierPageTabsNavigation.deletePersonRow();
          // ")
        
    
});
// test('Verify the image catergorization is done properly', {tag:"@PropertySectionPopulation"},async ({page ,DossierPage,DashboardWithToken,account, DossierPageTabsNavigation}) => {

// const testImages = [
//   { file: 'surveyMap.jpg', expectedCategory: 'PROPERTY' },
//   { file: 'propertyPhotos.jpg', expectedCategory: 'PROPERTY' },
// ];

//   // Upload images
//   const fileChooser = await page.waitForEvent('filechooser');
//   await page.click("input[type='file']"); // or whatever triggers upload
//   await fileChooser.setFiles(testImages.map(img => path.join(__dirname, '../../src/lib/files', img.file)));

//   // Wait for categorization to complete
//   await page.waitForTimeout(10000)
//   //await page.waitForSelector('.category-container', { timeout: 10000 });

  
//   for (const img of testImages) {
//     const categorySelector = `div.category[data-name="${img.expectedCategory}"]`;
//     const imageSelector = `${categorySelector} img[src*="${img.file}"]`;

//     const image = await page.$(imageSelector);
//     expect(image, `Expected ${img.file} to appear under ${img.expectedCategory}`).not.toBeNull();
//   }
// });




