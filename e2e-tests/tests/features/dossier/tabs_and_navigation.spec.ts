import { uploadFile } from '../../../src/helper/common';
import { test, expect } from '../../../src/lib/base_Test';
import testData from '../../../src/lib/testData/testData.json'
const user = process.env.E2E_USER
const password = process.env.E2E_PASSWORD;

test.describe('Tabs & Navigation', () => {

  test('Verify Navigation Between Tabs', {tag:"@navigateTabs"},async ({ page, LoginPage, DashboardPage, DossierPageTabsNavigation}) => {
    
    await page.goto('/');
    await LoginPage.login(`${user}`, `${password}`);
    await DashboardPage.goto();
    await DashboardPage.clickDossier(testData.validDossierName);
   

    await DossierPageTabsNavigation.verifyOnDossierPage();
    await DossierPageTabsNavigation.goThroughAllSections();
  });


  test('Verify Data Persistence Across Tabs', {tag:"@ModifyData"},async ({page ,DossierPage,DashboardWithToken,account, DossierPageTabsNavigation}) => {
    
        const dashboard = await DashboardWithToken(account.token);
          await dashboard.goto();
      
          const dossierName = testData.DossierName
          await dashboard.createNewDossier(dossierName);
          await dashboard.clickDossier(dossierName);
      
          
      
          await test.step('Upload a document into dossier', async () => {
            await uploadFile(page, DossierPage.selectors.dossierFileUploadInput, 'bankbekb.pdf');
          });
      
          await test.step('Assert file is being processed (check document container)', async () => {
            const docContainer = page.locator(DossierPage.selectors.dossierTotalImages);
            await page.waitForSelector(DossierPage.selectors.dossierTotalImages)
            await expect(docContainer, testData.dashboard.documentUploadAndProcessing.documentContainerVisibleTxt).toBeVisible();
          });
      
          await test.step('Assert uploaded document appears in the document list', async () => {
            const list = page.locator(DossierPage.selectors.dossierTotalImages);
            await expect(list, testData.dashboard.documentUploadAndProcessing.documentList).toBeVisible();
            const count = await list.count();
            expect(count).toBeGreaterThan(0); 
          });

          
          await DossierPageTabsNavigation.deletePersonRow();
          // ")
        
    // Precondition: User has made changes in one tab (e.g., renaming a document)
    // Step 1: Switch to a different tab and then return
    // Step 2: Verify that changes persist
    // Expected Result: User modifications remain visible; no unsaved data is lost.
    // TODO: Implement test logic here
});

});