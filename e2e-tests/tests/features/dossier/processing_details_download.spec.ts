import { test } from '../../../src/lib/base_Test';
import testData from '../../../src/lib/testData/testData.json'



const user = process.env.E2E_USER
const password = process.env.E2E_PASSWORD;

test(
  'Test clicking Detail links opens Processing Detial modal',
  { tag: '@openProcessingDetialModal' },
  async ({
    page, LoginPage, DashboardPage,DocumentTab // this is needed to get a dossier
  }) => {
    await page.goto('/');
    await LoginPage.login(`${user}`, `${password}`);
    await DashboardPage.goto();
    await DashboardPage.clickDossier(testData.validDossierName)
    await DocumentTab.openDetailModalAndVerify()
    


  });

 
  