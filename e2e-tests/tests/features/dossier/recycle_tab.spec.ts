import { test, expect } from '../../../src/lib/base_Test';
import { RecycleBinTab} from '../../../src/page_factory/recycle_tab';
import testData from '../../../src/lib/testData/testData.json'
const user = process.env.E2E_USER
const password = process.env.E2E_PASSWORD;
test(
    'Verify user can navigate to Recycle tab',
    { tag: '@navigateToRecycleTab' },
    async ({
        page, LoginPage, DashboardPage // this is needed to get a dossier
    }) => {
        const recycleBin = new RecycleBinTab(page);
        await page.goto('/');
        await LoginPage.login(`${user}`, `${password}`);
        await DashboardPage.goto();
        await DashboardPage.clickDossier(testData.validDossierName)
        await recycleBin.verifyRecycleBinMessage()
    });