import {test, expect} from '../../../src/lib/base_Test';
import testData from '../../../src/lib/testData/testData.json'

test.describe('Dossier Deletion Flow', () => {
  let dashboard: any;

  test.beforeEach(async ({ page, DashboardWithToken,account }) => {
    dashboard = await DashboardWithToken(account.token);
    await dashboard.goto();

  });

  test(
    'Test delete button triggers a confirmation modal.',
    { tag: '@openConfirmationModal' },
    async ({ DossierPage }) => {
      await dashboard.createNewDossier(testData.DossierName);
      await DossierPage.handleDeleteModal(testData.dashboard.triggerDeletionModel); // Trigger modal
    }
  );
  
  test(
    'Test clicking Cancel button closes the delete confirmation box',
    { tag: '@cancelConfirmationDialog' },
    async ({ DossierPage }) => {
      await dashboard.createNewDossier(testData.DossierName);
      await DossierPage.handleDeleteModal(testData.dashboard.triggerDeletionModel); // Trigger modal
      await DossierPage.handleDeleteModal(testData.dashboard.cancelDeletionModel); // Cancel the deletion
    }
  );
  
  test(
    'Verify deleted dossier no longer appears in the list.',
    { tag: '@deleteDossier' },
    async ({ DossierPage }) => {
      await dashboard.createNewDossier(testData.DossierName);
      await DossierPage.handleDeleteModal(testData.dashboard.triggerDeletionModel); // Trigger modal
      await DossierPage.handleDeleteModal(testData.dashboard.cancelDeletionModel); // Cancel the deletion
      await DossierPage.expectDossierNotInList(); // Verify the dossier isn't in the list
    }
  );
  // test(
  //   'Verify Deletion of Irrelevant Documents',
  //   { tag: '@deleteSignleDocumentWithinDocument' },
  //   async ({page, DashboardWithToken, account, DossierPage,dossier1}) => {
  //     const dashboard = await DashboardWithToken(account.token);
  //     await dashboard.goto();
  //     await dashboard.clickDossier(dossier1.name!);
 
  //   }
    
  // );
});
