import { test, expect } from '../../../src/lib/base_Test';
import testData from '../../../src/lib/testData/testData.json'

test(
  'Verify user can open one dossier',
  { tag: '@openDossier' },
  async ({
    page,
    account,
    DashboardWithToken,
    DossierDocumentsPage,
    dossier1 // this is needed to get a dossier
  }) => {
    const dashboard = await DashboardWithToken(account.token);
    await dashboard.goto();
    await expect(page).toHaveTitle(testData.dossierTitle);
    // await expect(
    //   page.getByText('Dossiers werden geladen. Bitte warten...')
    // ).toBeVisible();

    await dashboard.clickDossier(dossier1.name!);
    await DossierDocumentsPage.clickDocument(testData.dashboard.openDossier.documentName);
    await DossierDocumentsPage.verifyDocumentVisible();
  }
);

// currently there is an issue with using the account with more than 1 dossier
// should be fixed and used again
/*
test('open 2 dossiers', async ({ page, account, dossier1, dossier2 }) => {
  const dashboard = new DashboardPage(page, account.token);
  const dossierDocumentsPage = new DossierDocumentsPage(page);
  await dashboard.goto();
  await expect(page).toHaveTitle('Dossier Manager');
  await expect(
    page.getByText('Dossiers werden geladen. Bitte warten...')
  ).toBeVisible();

  await dashboard.clickDossier(dossier1.name!);
  await dossierDocumentsPage.clickDocument('Pass CH Andreas Dominik»');
  await expect(
    page.getByText('Andreas Dominik»', { exact: true })
  ).toBeVisible();
  await dashboard.goto();
  await dashboard.clickDossier(dossier2.name!);
});

test('open 3 dossiers', async ({
  page,
  account,
  dossier1,
  dossier2,
  dossier3
}) => {
  const dashboard = new DashboardPage(page, account.token);
  const dossierDocumentsPage = new DossierDocumentsPage(page);
  await dashboard.goto();
  await expect(page).toHaveTitle('Dossier Manager');
  await expect(
    page.getByText('Dossiers werden geladen. Bitte warten...')
  ).toBeVisible();

  // dossier 1
  await dashboard.clickDossier(dossier1.name!);
  await dossierDocumentsPage.clickDocument('Pass CH Andreas Dominik»');
  await expect(
    page.getByText('Andreas Dominik»', { exact: true })
  ).toBeVisible();

  // dossier 2
  await dashboard.goto();
  await dashboard.clickDossier(dossier2.name!);

  // dossier 3
  await dashboard.goto();
  await dashboard.clickDossier(dossier3.name!);
});
*/
