import { test, expect } from '../../../../src/lib/base_Test';
import testData from '../../../../src/lib/testData/testData.json'
test(
  'open one dossier',
  { tag: '@DashboardDownload' },
  async ({
    page,
    account,
    DossierPage,
    DashboardWithToken,
    dossier1 // this is needed to get a dossier
  }) => {
    const dashboard = await DashboardWithToken(account.token);
    await dashboard.goto();
    await expect(page).toHaveTitle(testData.dossierTitle);
    await dashboard.clickDossier(dossier1.name!);
    await DossierPage.download();
  }
);
