import { test } from '../../src/lib/base_Test';
const user = process.env.E2E_USER
const password = process.env.E2E_PASSWORD;

test(
  'Test changing settings (languages)',
  { tag: '@SettingPage' },
  async ({ page, LoginPage, SettingsPage }) => {
    // Navigation and authentication
    await page.goto('/');
    await LoginPage.login(`${user}`, `${password}`);

    // Test all language options
    await SettingsPage.testAllLanguagesVisual(['FR', 'EN', 'DE', 'IT']);
  }
);
