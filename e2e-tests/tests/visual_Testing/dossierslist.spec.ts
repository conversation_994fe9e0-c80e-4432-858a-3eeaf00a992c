import { test } from '../../src/lib/base_Test';
import testData from '../../src/lib/testData/testData.json'
const DEFAULT_DOSSIERS_QUANTITY = 3;
const user = process.env.E2E_USER
const password = process.env.E2E_PASSWORD;

test(
  'Authenticate test user, add and remove 1 dossier to the list of dossiers',
  { tag: '@addRemoveDossier' },
  async ({ page, LoginPage, DashboardPage, waitForLoad }) => {
    // Navigation and authentication
    await DashboardPage.goto();
    await LoginPage.login(`${user}`, `${password}`);
    await DashboardPage.verifyPageTitle();

    // Verify initial state
    await DashboardPage.verifyDossierCount(DEFAULT_DOSSIERS_QUANTITY);

    // Create new dossier
    await DashboardPage.createNewDossier(testData.DossierName);
    await DashboardPage.verifyDossierCount(DEFAULT_DOSSIERS_QUANTITY + 1);

    // Delete dossier
    await DashboardPage.deleteFirstDossier();
    await DashboardPage.verifyDossierCount(DEFAULT_DOSSIERS_QUANTITY);
    await waitForLoad(page);
    await DashboardPage.verifyPageAppearance();
  }
);
