import { expect, test } from '../../src/lib/base_Test';
const DEFAULT_DOSSIERS_QUANTITY = 3;
const user = process.env.E2E_USER
const password = process.env.E2E_PASSWORD;

test(
  'Authenticate test user, check different tabs of dossiers',
  { tag: '@checkDifferentTabs' },
  async ({ page, LoginPage, DossierPage, DashboardPage, waitForLoad }) => {
    // Navigation and authentication
    await page.goto('/');
    await LoginPage.login(`${user}`, `${password}`);
    await expect(page).toHaveTitle(/Dossier Manager/);

    // // Verify initial dossier list
    // await DossierPage.verifyDossierListCount(DEFAULT_DOSSIERS_QUANTITY);

    // Open specific dossier
    await DashboardPage.clickDossier('sales pitch mix with errors');
    // Verify and check all tabs
    await page.waitForTimeout(2000);
    await DossierPage.verifyTabCount(7);
    await waitForLoad(page);
    await DossierPage.verifyAllTabs();
  }
);
