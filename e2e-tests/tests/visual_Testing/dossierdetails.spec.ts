import { test, expect } from '../../src/lib/base_Test';
import testData from '../../src/lib/testData/testData.json'
const user = process.env.E2E_USER
const password = process.env.E2E_PASSWORD;

test(
  'Test dossier details',
  { tag: '@dossierList' },
  async ({ page, LoginPage, DashboardPage, DossierPage, waitForLoad, DocumentTab }) => {
    // Navigation
    await page.goto('/');
    await LoginPage.login(`${user}`, `${password}`);
    await DashboardPage.clickDossier(testData.validDossierName);

    // Dossier Operations
    await DossierPage.navigateToManager();
    await DossierPage.selectImageExportSample();
    await waitForLoad(page);
    await page.waitForSelector(DocumentTab.locators.notificationBubble)
    await expect(page.locator(DocumentTab.locators.imageContainer)).toBeVisible();
    
    // Assertion
    await DossierPage.verifyDetailsPageAppearance();
  }
);
