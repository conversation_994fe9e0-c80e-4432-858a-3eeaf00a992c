import { defineConfig } from '@playwright/test';
import { Options } from '../e2e-tests/src/lib/users';
import { NavigationStrategy } from '../src/gen/dms';
import os from 'os';

const dotenv = require('dotenv');
import path from 'path';
const currentOS = os.platform();
if (!process.env.CI) {
  const envFile = `.env.${process.env.ENV ?? 'local'}`;
  dotenv.config({ path: envFile });
}
export default defineConfig<Options>({
  globalTeardown: path.resolve(__dirname,'./src/lib/global-teardown.ts'),
  globalSetup:  path.resolve(__dirname,'./src/lib/global-setup.ts'),
  snapshotPathTemplate: path.resolve(
    __dirname,
    `./src/lib/files/screenshots/${currentOS}/{arg}{ext}`
  ),
  timeout: 200 * 1000, 
  outputDir: 'test-results/playwright-report/',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 4 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['dot'], // Use the dot reporter or any other reporter for console output
    ['junit', { outputFile: 'test-results/playwright-report/junit.xml' }], // JUnit for GitLab CI
    ['html', { outputFolder: 'test-results/html-report' }] // HTML for more detailed insights
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: process.env.CI
      ? `https://${
          process.env.DMF_VERSION && process.env.DMF_VERSION.trim() !== ''
            ? process.env.DMF_VERSION
            : process.env.CI_ENVIRONMENT_SLUG
        }.e2e-test.hypodossier.ch`
      : process.env.BASEURL,

    headless: true,

    browserName: 'chromium',
    screenshot: 'on',
    video: 'on',
    actionTimeout: 80 * 1000,
    navigationTimeout: 60 * 1000,

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    // viewport: { width: 1280, height: 720 }, 
  },
  /* Configure projects for major browsers */
  projects: [
    {
      name: 'test',
      testIgnore: [
        '/tests/features/feature-flags/**/*.spec.ts',
        '/tests/visual_Testing/**/*.spec.ts'
      ]
    },
    {
      name: 'no-dossier-list',
      testMatch:/tests\/features\/feature-flags\/no-dossier-list\/.*\.spec\.ts/,
      use: {
        accountOptions: {
          enable_uploading_files: false,
          navigation_strategy: NavigationStrategy.NO_DOSSIER_LIST_BRANDED
        },
        userRoles: []
      }
    },
    {
      name: 'no-download',
      testMatch:/tests\/features\/feature-flags\/no-download\/.*\.spec\.ts/,
      use: {
        accountOptions: {
          enable_button_download: false,
        }
      }
    },
    {
      name: 'visual_Testing',
      testMatch:/tests\/visual_Testing\/.*\.spec\.ts/,
      use: {
        accountOptions: {
          enable_form_tab:true
        }
      }
    }
  ],

  /* Run your local dev server before starting the tests */
  /*it loads the FE locally and you can connect the Backend and Database to run test*/
  // webServer: {
  //   command: 'yarn start',
  //   url: process.env.CI
  //     ? `https://${
  //         process.env.DMF_VERSION?.trim()
  //           ? process.env.DMF_VERSION
  //           : process.env.CI_ENVIRONMENT_SLUG
  //       }.e2e-test.hypodossier.ch`
  //     : 'https://service.hypo.duckdns.org/',
  //   reuseExistingServer: true,
  //   timeout: 300 * 1000,
  //   stdout: 'ignore',
  //   stderr: 'pipe'
  // }
  
});
