import { Page, expect } from '@playwright/test';
import testData from '../lib/testData/testData.json'
export class DashboardPage {
  private static readonly expectedDossierName = 'sales pitch mix with errors dossier';
 
  constructor(
    private readonly page: Page,
    private readonly token?: string
  ) {}

 
  // Locators
  public locators = {
    pageTitle: 'Dossier Manager',
    dossierList: `[data-testid="table-item-container"] `,
    title: "div.hd-ant-modal-title",
    dossierLink: (index: number) =>
      `tbody[data-testid='dossier-list-table-body'] > tr:nth-child(${index}) a > span`,
    noteButton: '#black-notes-full-surface1 path:nth-child(2)',
    createDossierHeader: '[data-testid="create-dossier-top-header"]',
    createDossierButton: '[data-testid="CREATE_DOSSIER_BUTTON"]',
    dossierModal: '[class="sc-fFoeYl fmhdul"]',
    confirmCreateButton: 'button.save-button',
    uploadPrompt: 'span:nth-child(1) p:nth-child(1)',
    dossierName: (name: string) => `//span[normalize-space()='${name}']`,
    dossierItems: '[data-testid="table-item-container"]',
    firstDossier: '[data-testid="table-item-container"]:first-child',
    trashIcon: '[data-testid="trash-icon"]',
    deleteConfirmButton: 'button:has-text("Ja, Dossier löschen")',
    createButton: '[data-testid="CREATE_DOSSIER_BUTTON"]',
    nameInput: `input[type='text']`,
    sampleDossierLink: 'role=link[name="sales pitch mix with errors"]',
    propertiesButton: '[data-testid="dossier-item-properties-button"]',
    crossIcon: 'button[aria-label="Close"]',
    dossierNameField: '.hd-ant-input-outlined:nth-child(2) ',
    cancelButton: 'button.cancel-button',
    searchInput: '[data-testid="searchbox-input"]',
   searchResultText: 'tr[data-testid="table-item-container"] td a > span',
   noDossierErrorMessage: '[data-testid="no-search-results-line-1"]',
   resetAllFilter: '[data-testid="no-search-results-reset-button"]',
   resetFilter: '[data-testid="btn-reset-all"]',
   propertiesButtons: '[data-testid="dossier-item-properties-button"]',
   nameInputs: 'input.hd-ant-input.hd-ant-input-outlined',
   saveButton: 'button.hd-ant-btn-primary.save-button',
   dossierNameCell: 'tbody[data-testid="dossier-list-table-body"] tr:nth-child(1) td:nth-child(1) span',
   wrapperDiv: 'div.wrapper',
    datePicker: 'div.hd-ant-picker',
    pastDate: '[title="${targetDateTitle}"]',
    previousMonth:'.hd-ant-picker-header-prev-btn',
    nextMonthButton: '[class="hd-ant-picker-header-next-btn"]',
    dateInputField: '[placeholder="Datum auswählen"]',
    expiredDateText: '[data-testid="expired-date-text-wrapper"] div:nth-child(2) > span',
    prevMonthButton: '.hd-ant-picker-header-prev-btn',
    targetDate: '2025-04-27',
    scrollContainer: 'div[id="hd-application-root-div"] > div > div > div > div:nth-child(2)',
    paginationLocator:'li[title="2"]',
    pastDateByTitle: (date: string) => `[title="${date}"]`,
  sortByOldestOption: '[data-testid="dossier-list-sorting-creation-date-oldest"]',
    sortingValue: '[data-testid="sorting-value"]',
  firstRowDossierName: 'tbody[data-testid="dossier-list-table-body"] tr:nth-child(1) td:nth-child(1) span'
   
  };


  // Navigation
  async goto() {
    const url = `/dashboard?lang=de${this.token ? `&token=${this.token}` : ''}`;
    await this.page.goto(url);
  }

  // Dossier Actions
  async clickDossier(dossierName: string) {
    await this.page.waitForLoadState('networkidle')
    await this.page.waitForSelector(this.locators.dossierList);
    const rows = this.page.locator(this.locators.dossierList);
    const count = await rows.count();
    for (let i = 0; i < count; i++) {
      const rowText = await rows.nth(i).textContent();
      if (rowText?.trim().includes(dossierName)) {
        Promise.all([
          await this.page.locator(this.locators.dossierLink(i + 1)).click(),
          await this.page.waitForLoadState('domcontentloaded')
        ]);

        break;
      }
    }
  }

  async verifyDossierIsVisible(dossierName: string) {
    await expect(
      this.page.locator(this.locators.dossierName(dossierName))
    ).toBeVisible();
  }

  // Note Actions
  async openNote() {
    await this.page.locator(this.locators.noteButton).click();
  }

  // Dossier Creation Flow
  async createDossier() {
    await expect(this.page.locator(this.locators.createDossierHeader)).toBeVisible({ timeout: 10000 });
    await this.page.locator(this.locators.createDossierButton).click();
    await expect(this.page.locator(this.locators.dossierModal)).toBeVisible();
    await this.page.locator(this.locators.confirmCreateButton).click();
  }

  async verifyDocumentUploadPrompt() {
    await expect(this.page.locator(this.locators.uploadPrompt)).toBeVisible();
  }
  async verifyDossierCount(expectedCount: number) {
    Promise.all([
      await this.page.waitForLoadState('networkidle'),
      await this.page.waitForSelector(this.locators.dossierItems),
      await expect(this.page.locator(this.locators.dossierItems)).toHaveCount(
        expectedCount
      )
    ]);
  }

  async createNewDossier(name: string) {
    await this.page.locator(this.locators.createButton).click();
    await expect(this.page.locator(this.locators.title)).toBeVisible();
    await this.page.waitForLoadState('domcontentloaded'),
    await this.page.locator(this.locators.nameInput).fill(name);
    Promise.all([
    await this.page.locator(this.locators.confirmCreateButton).click(),
    await this.page.waitForLoadState('networkidle'),
   await this.verifyDocumentUploadPrompt()

    ])
    Promise.all([
      await this.goto(),
      await this.page.waitForLoadState('networkidle')
    ]);
  }

  async deleteFirstDossier() {
    await this.page
      .locator(this.locators.firstDossier)
      .locator(this.locators.trashIcon)
      .click();
    // await expect(this.page.locator(this.locators.sampleDossierLink))
    //   .toMatchSnapshot();
    await this.page.locator(this.locators.deleteConfirmButton).click();
  }

  async verifyPageAppearance() {
    await expect.soft(this.page).toHaveScreenshot('list-of-dossiers-page.png', {
      maxDiffPixelRatio: 0.03,
      threshold: 0.3,
    });
  }
  async verifyPageTitle() {
    await expect(this.page).toHaveTitle(this.locators.pageTitle);
  }

//  Handle Properties
  async handlePropertiesModal(action = 'open') {
    if (action === 'open') {
      // Open the properties modal by clicking the properties button
      await this.page.locator(this.locators.propertiesButton).first().click();
      await expect(this.page.locator(this.locators.crossIcon)).toBeVisible();
    } else if (action === 'verify') {
      // Verify if the dossier name field is visible
      const dossierName = this.page.locator(this.locators.dossierNameField);
      await expect(dossierName).toBeVisible();
    } else if (action === 'cancel') {
      // Cancel the properties modal by clicking the cancel button
      await this.page.locator(this.locators.cancelButton).click();
    }
  }

 async createAndOpenDossier(DashboardWithToken, account, page, dossierName) {
    const dashboard = await DashboardWithToken(account.token);
    await dashboard.goto();
    await dashboard.createNewDossier(dossierName);
    await dashboard.clickDossier(dossierName);
    await page.waitForLoadState('networkidle');
  }


  // search dossier test 

  async searchDossierByName(name: string): Promise<void> {
    await this.page.locator(this.locators.searchInput).click();
    await this.page.locator(this.locators.searchInput).fill(name);
    const result = await this.page.locator(this.locators.searchResultText).nth(0).textContent();
    expect(result?.trim()).toBe(DashboardPage.expectedDossierName);
  }

  async noDossierFound(name: string): Promise<void> {
    await this.page.locator(this.locators.searchInput).click();
    await this.page.locator(this.locators.searchInput).fill(name);
    await expect(this.page.locator(this.locators.noDossierErrorMessage)).toBeVisible()
  }
  

  async resetAllFilter() {
    await this.page.locator(this.locators.resetAllFilter).click()
    await expect(this.page.locator(this.locators.resetFilter)).toBeHidden()
  }



  // dossier properties test cases 

  async renameDossier(newName: string) {
    await this.page.locator(this.locators.propertiesButton).click();
    const nameInput = this.page.locator(this.locators.nameInput).first();
    await nameInput.clear();
    await nameInput.fill(newName);
    await this.page.waitForSelector(this.locators.saveButton)
    await this.page.locator(this.locators.saveButton).click({force:true});
    
  }

  async getWrapperText() {
    return await this.page.locator(this.locators.wrapperDiv).textContent();
  }

  async getRenamedDossierText() {
    const updatedName=  await this.page.locator(this.locators.dossierNameCell).textContent();
    await expect(updatedName).toBe('NewDossier1');
  }


  // Future date test 

  async clickDossierPropertiesButton(page: any) {
    await page.locator(this.locators.propertiesButton).click();
  }

  // Combined method to open the date picker, navigate to the next month (if needed), and select the date
  async selectFutureDate(page: any, futureDate: Date) {
    const today = new Date();
    const year = futureDate.getFullYear();
    const month = String(futureDate.getMonth() + 1).padStart(2, '0');
    const day = String(futureDate.getDate()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}`;

    // Open the date picker and handle month navigation if needed
    await page.locator(this.locators.datePicker).click();
    if (futureDate.getMonth() !== today.getMonth()) {
      await page.locator(this.locators.nextMonthButton).click();
    }
    await page.locator(`[title="${formattedDate}"]`).click({ force: true });
  }

  async getSelectedDateInputValue(page: any) {
    return page.locator(this.locators.dateInputField).inputValue();
  }

  async clickSaveButton(page: any) {
    await expect(page.locator(this.locators.saveButton)).toBeVisible({ timeout: 5000 });
    await expect(page.locator(this.locators.saveButton)).toBeEnabled();
    await page.locator(this.locators.saveButton).click();
  }

  async getExpiredDateText(page: any) {
    return await page.locator(this.locators.expiredDateText).textContent();
  }

  // get past Date testCase 

  async validateDateIsDisabled(dateTitle: string) {
    await this.page.locator(this.locators.datePicker).click();
  
    const dateElement = this.getPastDateElement(dateTitle);
  
    for (let i = 0; i < 3; i++) {
      if (await dateElement.isVisible()) break;
      await this.page.locator(this.locators.prevMonthButton).click();
      await this.page.waitForTimeout(300);
    }
  
    const classAttribute = await dateElement.getAttribute('class');
    expect(classAttribute).toContain('disabled');
  }

  getPastDateElement(date: string) {
    return this.page.locator(this.locators.pastDateByTitle(date));
  }

  // Test scrolling and pagination 

  async checkScrollbar(shouldBeVisible: boolean) {
    const isVisible = await this.page.locator(this.locators.scrollContainer).evaluate(el => el.scrollHeight > el.clientHeight);
    expect(isVisible).toBe(shouldBeVisible);
  }

  // Sorting Dossier 

  async sortDossierByCreationDateNewest() {
    const sortingText = await this.page.locator(this.locators.sortingValue).textContent();
    expect(sortingText).toBe(testData.dashboard.dossierSorting.sortByCreationDateNewest);
  
    const firstRowText = await this.page.locator(this.locators.firstRowDossierName).textContent();
    expect(firstRowText).toContain(testData.DossierName); // Make sure `testData` is in scope
  }

  async sortDossierByCreationDateOldest() {
    await this.page.locator(this.locators.sortingValue).click();
    await this.page.locator(this.locators.sortByOldestOption).click();
  
    const sortingText = await this.page.locator(this.locators.sortingValue).textContent();
    expect(sortingText).toBe(testData.dashboard.dossierSorting.sortByCreationDateOldest);
  
    const firstRowText = await this.page.locator(this.locators.firstRowDossierName).textContent();
    expect(firstRowText).toContain(testData.dashboard.dossierSorting.FirstDossierName);
  }
}
