// recycleBinPage.ts
import { Page, expect } from '@playwright/test';
import testData from '../../src/lib/testData/testData.json'

// Locators and test data
const recycleBinLocators = {
  recycleBinButton: '[data-node-key="recycle-bin"]',
  recycleBinText: '[id="dnd-scroll-container"] div',
};


export class RecycleBinTab {
  private page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async verifyRecycleBinMessage() {
    await this.page.locator(recycleBinLocators.recycleBinButton).click();
    const actualText = await this.page.locator(recycleBinLocators.recycleBinText).textContent();
    expect(actualText?.trim()).toBe(testData.dossier.txtMessage);
  }
}
