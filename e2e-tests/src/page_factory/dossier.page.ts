import { Page, expect } from '@playwright/test';
import testData from '../../src/lib/testData/testData.json'
export class DossierPage {
  constructor(private readonly page: Page) { }

  public selectors = {
    // Existing controls
    downloadBtn1: 'button:has-text("Herunterladen")',
    downloadBtn2: 'button:has-text("Dossier")',
    writeNoteBtn: `(//button[@type='button'])[2]`,
    saveNoteBtn: '.hd-ant-modal-footer button:nth-child(2)',
    textBox: 'textarea, input[type="text"]',
    closePopup: '[aria-label="Close"]',
    managerLink: 'text=HypoDossier Manager',
    imageExportCell: 'role=cell[name="image export samples"]',
    dossierCell: (name: string) => `role=cell[name="${name}"]`,
    dossierList: '[data-testid="table-item-container"]',
    notesButton: `[data-testid="dossier-item-notes-button"]`,
    noteText: 'textarea.hd-ant-input-outlined',
    dossierFileUploadInput: "input[type='file']",
    dossierValidationPopup: '.hd-ant-notice-success div:nth-child(2)',
    dossierTotalImages: '.hd-override-document-images-container',
    dossierName: '#hd-application-root-div',
    deleteIcon: '[data-icon="trash-can"]',
    confirmationModal: '.hd-ant-modal-content',
    cancelButton: '.hd-ant-btn-default.cancel-button',
    confirmButton: '.hd-ant-btn-primary.save-button',

    docContainer: '.hd-override-card-text',
    ellipsisIcon: '[data-icon="circle-ellipsis-vertical"]',
    editIcon: '[data-icon="pen"]',
    saveButton: 'button.save-button',
    successNotification: '[class="hd-ant-notice-message"]',

    errorIcon: '[data-icon="circle-x"]',
    errorMessageCell: 'table tbody td:nth-child(5)',
    tabLocator: 'div[role="tab"]',
    combinePagesNotification: '.hd-override-combine-pages-notification-bubble',
    dossierHandler: '[data-handler-id="T70"]',
    plusIconButton: '[data-icon="plus"]',
    tabIcons: 'span.hd-ant-btn-icon',
    allTabs: 'role=tab',
    allTab: '[data-tab="allTabs"]',
    documentImageWrapper: '.hd-override-document-image-wrapper',
    documentImagesContainer: "(//div[@class='hd-override-document-images-container']//div)[2]",
    scrollContainer: '#dnd-scroll-container',
    errorText : "div.rah-static--height-specific div:nth-child(3).jrzQum",
    errorBlock : 'div.rah-static--height-specific div:nth-child(3)',
    processedFiles :'div.rah-static--height-specific div:nth-child(2) div:nth-child(2)',
    multipleFiles :'div.rah-static--height-specific div:nth-child(2) div div:nth-child(2)',


  };
  public assertions = {
    //-----------Assertion-Messages-----------------//
    UploadDocSucess: 'Datei hochgeladen',
    nameOfDoc: 'namOfDoc',
    updatedDocTitle: 'Maria Mustermann Müller BEKB 2020-09-04',
    originalDocValue: 'Maria Mustermann Müller BEKB 2020-09-02',
    updatedDocValue: 'Maria Mustermann Müller BEKB 2020-09-04',
    invalidDocErrorMessage: "Dateityp '.sh' nicht unterstützt.",
    errorMessage: "Dateityp '.sh' nicht unterstützt. Aktuell unterstützte Dateitypen: ['.7z', '.avif', '.bmp', '.dcx', '.doc', '.docx', '.eml', '.gif', '.heic', '.heif', '.j2k', '.jb2', '.jfif', '.jp2', '.jpc', '.jpeg', '.jpg', '.msg', '.odp', '.ods', '.odt', '.pcx', '.pdf', '.png', '.ppt', '.pptx', '.rar', '.rtf', '.tar', '.tif', '.tiff', '.txt', '.xls', '.xlsm', '.xlsx', '.zip']."
  }

  async goToTab(nameOfTab: string) {
    // Locate all divs with role="tab"
    await this.page.waitForSelector(this.selectors.tabLocator);
    const tabs = await this.page.locator(this.selectors.tabLocator);

    // Iterate through each tab and check the text content of the span inside it
    const tabCount = await tabs.count();
    for (let i = 0; i < tabCount; i++) {
      const spanText = await tabs.nth(i).textContent();

      // Check if the text content matches the provided nameOfTab
      if (spanText === nameOfTab) {
        // Click on the div if the text matches
        await tabs.nth(i).click();
        break;
      }
    }
  }

  async writeNote(note: string) {
    await this.page.waitForSelector(this.selectors.writeNoteBtn);
    await this.page.locator(this.selectors.writeNoteBtn).click();
    await this.page.locator(this.selectors.textBox).click();
    await this.page.locator(this.selectors.textBox).fill(note);
    await this.page.locator(this.selectors.saveNoteBtn).click();
  }

  async updateNote(note: string) {
    await this.page.waitForSelector(this.selectors.writeNoteBtn);
    await this.page.locator(this.selectors.writeNoteBtn).click()
    await this.page.locator(this.selectors.textBox).click();
    await this.page.keyboard.press('Control+A'); // Or 'Meta+A' on macOS
    await this.page.keyboard.press('Backspace')
    await this.page.locator(this.selectors.textBox).fill(note);
    await this.page.locator(this.selectors.saveNoteBtn).click();
  }

  async deleteNote() {
    await this.page.waitForSelector(this.selectors.writeNoteBtn);
    await this.page.locator(this.selectors.writeNoteBtn).click()
    await this.page.locator(this.selectors.textBox).click();
    await this.page.keyboard.press('Control+A'); // Or 'Meta+A' on macOS
    await this.page.keyboard.press('Backspace')
    await this.page.locator(this.selectors.saveNoteBtn).click();
  }

  async download() {
    await this.page.waitForSelector(this.selectors.downloadBtn1);
    await this.page.locator(this.selectors.downloadBtn1).click();
    await this.page.locator(this.selectors.downloadBtn2).click();
    await this.page.locator(this.selectors.closePopup).click();
  }

  // New methods for the test
  async navigateToManager() {
    await this.page.locator(this.selectors.managerLink).click();
  }

  async selectImageExportSample() {
    await this.page.locator(this.selectors.imageExportCell).click();
  }

  async verifyDetailsPageAppearance() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForSelector(this.selectors.combinePagesNotification);
    await this.page.waitForSelector(this.selectors.dossierHandler);
    expect(this.page.locator(this.selectors.dossierHandler)).toBeVisible();
    await expect.soft(this.page).toHaveScreenshot('dossierlist.png', {
      maxDiffPixels: 3000,
      timeout: 10000,
    });
  }
  
  async verifyTabCount(expectedCount: number) {
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.waitForSelector(this.selectors.plusIconButton);
    const iconButton = await this.page.locator(this.selectors.tabIcons).nth(4);
    await expect(this.page.locator(this.selectors.allTabs)).toHaveCount(expectedCount);
  }
  
  async verifyAllTabs() {
    const tabs = await this.page.locator(this.selectors.allTabs).all();
    const firstTab = tabs[0];
    await firstTab.click();
    await this.page.waitForSelector(this.selectors.documentImageWrapper);
    await expect(this.page.locator(this.selectors.documentImagesContainer)).toBeVisible();
    await expect(this.page.locator(this.selectors.scrollContainer)).toBeVisible();
    await this.page.waitForTimeout(5000);
  
    const firstName = await firstTab.textContent();
    await expect.soft(this.page).toHaveScreenshot(`dossier-tab-${firstName}.png`, {
      maxDiffPixels: 8000,
      maxDiffPixelRatio: 0.05,
      threshold: 0.4,
    });
  
    for (const tab of tabs) {
      await tab.click();
      await expect(this.page.locator(this.selectors.scrollContainer)).toBeVisible();
      const tabName = await tab.textContent();
  
      await expect.soft(this.page).toHaveScreenshot(`dossier-tab-${tabName}.png`, {
        maxDiffPixels: 8000,
        maxDiffPixelRatio: 0.05,
        threshold: 0.3,
        timeout: 10000,
      });
    }
  }

  async verifyDossierListCount(expectedCount: number) {
  await this.page.waitForSelector(this.selectors.dossierList);
  await expect(this.page.locator(this.selectors.dossierList)).toHaveCount(
    expectedCount
  );
}

async openDossierNotes(clickFirst: boolean = false) {
  const notesButton = this.page.locator(this.selectors.notesButton);
  const buttonToClick = clickFirst ? notesButton.first() : notesButton;
  await buttonToClick.click();
}
  async verifyNoteVisible() {
  await expect(this.page.locator(this.selectors.noteText)).toBeVisible();
}

  async verifyNoteCreation() {
  await this.openDossierNotes();
  await this.verifyNoteVisible();
}


  async handleDeleteModal(action = 'open', index ?: number) {
  if (action === 'open') {
    // Trigger the modal by clicking the delete icon
    const deleteIconLocator = this.page.locator(this.selectors.deleteIcon);
    if (typeof index === 'number') {
      await deleteIconLocator.nth(index).click();
    } else {
      await deleteIconLocator.first().click();
    }
    await expect(this.page.locator(this.selectors.confirmationModal)).toBeVisible();
  } else if (action === 'confirm') {
    // Confirm the deletion by clicking the confirm button
    await this.page.locator(this.selectors.confirmButton).click();
    await expect(this.page.locator(this.selectors.confirmationModal)).toBeHidden();
  } else if (action === 'cancel') {
    // Cancel the deletion by clicking the cancel button
    await this.page.locator(this.selectors.cancelButton).click();
    await expect(this.page.locator(this.selectors.confirmationModal)).toBeHidden();
  }
}

  async expectDossierNotInList() {
  await expect(this.page.locator(this.selectors.dossierList)).toHaveCount(0);
}


  //  Renaming a Document code start here 

  async renameingDoc(value: string, original: string, updated: string) {
  await this.page.waitForSelector(this.selectors.allTabs)
  await expect(this.page.locator(this.selectors.docContainer).nth(2), 'Document container not visible after upload').toBeVisible();
  await this.page.locator(this.selectors.docContainer).nth(2).hover();
  await this.page.locator(this.selectors.ellipsisIcon).hover();
  await this.page.locator(this.selectors.editIcon).click();
  await this.page.locator(`[value="${value}"]`).click();
  await this.page.locator(`[value="${original}"]`).fill(updated);
  await this.page.locator(this.selectors.saveButton).click();
  await expect(this.page.locator(this.selectors.successNotification)).toBeVisible();
  await expect(this.page.locator(this.selectors.docContainer).nth(2)).toHaveText(this.assertions.updatedDocTitle);
}

// document upload 

async assertSuccessToastShown(page: Page) {
  await page.waitForSelector(this.selectors.dossierValidationPopup);
  const toast = page.locator(this.selectors.dossierValidationPopup);
  await expect(toast, testData.dashboard.documentUploadAndProcessing.successToastShown)
    .toHaveText(this.assertions.UploadDocSucess);
}

async assertDocumentContainerVisible(page: Page) {
  const docContainer = page.locator(this.selectors.dossierTotalImages);
  await page.waitForSelector(this.selectors.dossierTotalImages);
  await expect(docContainer, testData.dashboard.documentUploadAndProcessing.documentContainerVisibleTxt)
    .toBeVisible();
}

async assertDocumentAppearsInList(page: Page) {
  const list = page.locator(this.selectors.dossierTotalImages);
  await expect(list, testData.dashboard.documentUploadAndProcessing.documentList).toBeVisible();
  const count = await list.count();
  expect(count).toBeGreaterThan(0);
}

async clickUploadedDocument(page: Page) {
  const firstDoc = page.locator(`${this.selectors.dossierTotalImages} > div:nth-child(1)`);
  await expect(firstDoc, testData.dashboard.documentUploadAndProcessing.imageFound).toBeVisible();
  await firstDoc.click();
  // Optionally assert on the opened document:
  // await expect(page.locator(this.selectors.dossierName)).toContainText(this.assertions.nameOfDoc);
}

async verifyErrorMessage(page: Page) {
  await page.waitForLoadState('networkidle')
  await page.waitForSelector(this.selectors.errorText, { state: 'visible'});
  await page.click(this.selectors.errorBlock);
  await expect(page.locator(this.selectors.errorIcon)).toBeVisible();

  const errorMessage = await page.locator(this.selectors.errorMessageCell).textContent();
  expect(errorMessage).toContain(this.assertions.invalidDocErrorMessage);
}

async verifyErrorMessageText(page: Page) {
  await page.waitForLoadState('networkidle')
  await this.page.waitForSelector(this.selectors.errorText, { state: 'visible' });
  await page.click(this.selectors.errorBlock);
  await expect(page.locator(this.selectors.errorIcon)).toBeVisible();

  const actualMessage = await page.locator(this.selectors.errorMessageCell).textContent();
  expect(actualMessage).toBe(this.assertions.errorMessage);
}


}