import { FullConfig } from '@playwright/test';
import os from 'os';
const currentOS = os.platform();
async function globalSetup(config: FullConfig) {
  console.log(">> Global setup starting...");
 
  
  const isGitLabCI = process.env.GITLAB_CI === 'true';
  const environmentInfo = {
    BASE_URL: process.env.BASEURL || 'Not defined',
    ENV: process.env.ENV || 'Not defined',
    OS: currentOS,
    IS_GITLAB_CI: isGitLabCI,
  };
  console.table(environmentInfo);
  if (isGitLabCI) {
    console.log(">> Running in GitLab CI environment.");
  } else {
    console.log(">> Running in local/development environment.");
  }
}
export default globalSetup;