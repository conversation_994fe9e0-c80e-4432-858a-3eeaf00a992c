# 📘 Dossier Manager E2E Tests (Playwright)

This yarn workspace contains end-to-end (E2E) tests for the Dossier Manager frontend using [<PERSON><PERSON>](https://playwright.dev/). It is structured for scalability and environment-based configurations.

---

## ✨ Overview

- **Test Framework**: [Playwright](https://playwright.dev/)
- **Language**: TypeScript
- **Architecture**: Page Object Model (POM)
- **Environment Support**: via `.env` files

---

## 🗂 Folder Structure

```plaintext
e2e-tests/

├── .env-example         # Template for environment configuration
├── src/
│   ├── helper/             # Utility/helper functions used across tests
│   ├── lib/                # Shared base classes or logic (e.g., BaseTest)
│   └── page_factory/       # Page Object Model implementations
├── tests/
│   └── features/           # Test specs organized by feature
│       ├── dashboard/
│       ├── feature-flags/
│       ├── login/
│       └── ...
├── test-results/           # Stores output like traces, screenshots
├── playwright-report/      # HTML reports generated by Playwright
├── tsconfig.json           # TypeScript config specific to tests
└── playwright.config.ts    # Playwright test runner configuration

package.json                # Scripts and dependencies
```

---

## ⚙️ Environment & Running

### 🔧 Environment Setup

This project supports multiple environments using `.env` files.

1. Duplicate `.env-example` and rename it to match your environment:
   - Example: `.env.local`, `.env.docker`
2. Replace placeholder values in the new file with actual credentials or URLs.
3. Create a .env folder and add the new environment file inside it.       

├── .env/
│   ├── .env.NameOfTheEnv 

> These variables are accessed via the `env` CLI flag during test execution.

---

### 🚀 Running the Tests

#### Install dependencies

```bash
yarn install
```
---

### Running Locally with Docker

To run the tests locally, follow these steps:

1. Start all the necessary services:

   ```bash
   docker-compose -f docker-compose.e2e.yml up -d
   ```

2. If you want to run the dmf locally rather then using the version pinned in `docker-compose.e2e.yml`:
   - Change the config in `conf/proxy/Caddyfile` to point Caddy to the locally running dmf instance
   - Run `docker-compose -f docker-compose.e2e.yml up -d` again to make sure Caddy loads the latest config
   - Make sure dmf is running locally
   - Optionally you can now stop the pinned dmf version.
   ```bash
      docker stop dossier-manager-frontend-dmf-1 
   ```
   This is not strictly necessary as by the above change to the 
   Caddy config you just bypass it


3. Run the tests through Docker
```bash
 docker compose -f docker-compose.playwright.yml -p e2e-tests up --build --force-recreate
   ```
    

Alternatively just run the `run_e2e.sh` script after you made sure you have the Caddy config set the way you need it.


#### Run with a specific environment CommandLine

```bash
env=.env.NameOfEnv npm run test
```

#### Run with default environment

```bash
yarn test
```
Defualt env you can see in playwright.config.ts file line #10

---
