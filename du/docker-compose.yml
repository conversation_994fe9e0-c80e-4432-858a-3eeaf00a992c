version: '3.8'
services:
  caddy:
    image: registry.gitlab.com/hypodossier/caddy:${TAG-latest}
    command: "caddy run --watch --config /etc/caddy/Caddyfile"
    ports:
      - 80:80
      - 443:443
    environment:
      - BASE_DOMAIN=hypo.duckdns.org
      - DUCKDNS_API_TOKEN=63daf4d5-9c7e-4c02-9702-5842664a5077
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      caddy:
        aliases:
          - auth.hypo.duckdns.org
      core-services:
        aliases:
          - minio.hypo.duckdns.org
          - s3.hypo.duckdns.org
          - dms.hypo.duckdns.org

    volumes:
      - caddy_data:/data
      - caddy_config:/config
      - ./conf/proxy/Caddyfile:/etc/caddy/Caddyfile

  stepca1:
    image: smallstep/step-ca
    environment:
      DOCKER_STEPCA_INIT_NAME: My CA
      DOCKER_STEPCA_INIT_DNS_NAMES: localhost,ca1.hypo.duckdns.org
      DOCKER_STEPCA_INIT_REMOTE_MANAGEMENT: true
      DOCKER_STEPCA_INIT_PROVISIONER_NAME: admin
      DOCKER_STEPCA_INIT_PASSWORD: admin
    networks:
      caddy:
      core-services:
    volumes:
      - step:/home/<USER>
    ports:
      - 8443:9000

  whoami:
    image: containous/whoami
    networks:
      - caddy
    labels:
      caddy_0: whoami.hypo.duckdns.org
      caddy_0.reverse_proxy: "{{upstreams 80}}"
      caddy_0.import: tls

  rabbitmq:
    image: rabbitmq:3.8-management
    hostname: rabbitmq1
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq/mnesia
      - ./conf/rabbitmq/rabbitmq-definitions.json:/etc/rabbitmq/definitions.json

    ports:
      - 5672:5672
      - 15672:15672
    secrets:
      - source: RABBITMQ_CONFIG
        target: /etc/rabbitmq/rabbitmq.conf
    networks:
      core-services:
        aliases:
          - rabbitmq.hypo.duckdns.org
      caddy:

  rabbitmq_definitions_backup:
    profiles: [ rabbitmq ]
    init: true
    image: registry.gitlab.com/hypodossier/infrastructure/rabbitmq-definitions-backup:v0.9.0
    secrets:
      - source: RABBITMQ_DEFINITIONS_BACKUP_CONFIG
        target: /app/.env
    networks:
      core-services:

  pgcluster1:
    image: postgres:12
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin
    networks:
      core-services:
        aliases:
          - pgcluster1.hypo.duckdns.org
    ports:
      - 5432:5432
    volumes:
      - ./conf/pgcluster1/init.sql:/docker-entrypoint-initdb.d/init.sql
      - pg-data:/var/lib/postgresql/data

  pgadmin:
    image: dpage/pgadmin4:6
    environment:
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DISABLE_POSTFIX=0
    networks:
      core-services:
      caddy:
    secrets:
      - source: PGADMIN4_SERVER_CONFIG
        target: /pgadmin4/servers.json

  minio:
    image: quay.io/minio/minio
    environment:
      - MINIO_BROWSER_REDIRECT_URL=https://minioc.hypo.duckdns.org
      - MINIO_ROOT_USER=S3_ACCESS_KEY
      - MINIO_ROOT_PASSWORD=S3_SECRET_KEY
    networks:
      caddy:
    entrypoint: sh
    command: -c 'mkdir -p /data/dossier && mkdir -p /data/dms-default-bucket && mkdir -p /data/dp-processing && mkdir -p /data/dp-cache && mkdir -p /data/hyintapi && mkdir -p /data/hypodossier-models && minio server /data --console-address ":9001"'
    volumes:
      - minio_data:/data
    ports:
      - 9000:9000
      - 9001:9001
    healthcheck:
      test: [ "CMD", "mc", "ready", "local" ]
      interval: 5s
      timeout: 15s
      retries: 5

  keycloak:
    image: registry.gitlab.com/hypodossier/keycloak:v1.12.10
    ports:
      - 8080:8080
    command:
      - "--import-realm"
      - "--optimized"
    networks:
      core-services:
      caddy:
    environment:
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
    secrets:
      - source: KEYCLOAK_REALM_CONFIG_V5
        target: /opt/keycloak/data/import/main-realm.json
      - source: KEYCLOAK_CONF_V4
        target: /opt/keycloak/conf/keycloak.conf

    healthcheck:
      test: "{ printf >&3 'GET /health/ready HTTP/1.0\r\nHost: localhost\r\n\r\n'; cat <&3; } 3<>/dev/tcp/localhost/8080 | head -1 | grep 200"
      start_period: 20s
      interval: 5s
      timeout: 5s
      retries: 30

  dmf:
    image: registry.gitlab.com/hypodossier/dossier-manager-frontend:v1.117.0
    secrets:
      - source: DMF_KEYCLOAK_CONFIG
        target: /srv/build/keycloak.json
      - source: DMF_MANAGER_CONFIG_V2
        target: /srv/build/config/config.json
    networks:
      caddy:
    healthcheck:
      test: wget -O /dev/null -q 127.0.0.1:2019/config
      start_period: 5s
      interval: 5s
      timeout: 5s
      retries: 20
    stop_grace_period: 0s

  # Dossier Event Consumer (DEC)
  dec:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.157.4
    command: python manage.py dossier_event_consumer_v2
    secrets:
      - source: DMS_ENV_V5
        target: /app/.env
    restart: on-failure
    networks:
      core-services:
      caddy:
    stop_grace_period: 0s

  # Dossier Image Exporter Worker (DIEW)
  diew:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.157.4
    command: python manage.py image_exporter_worker
    networks:
      core-services:
      caddy:
    restart: on-failure
    secrets:
      - source: DMS_ENV_V5
        target: /app/.env
    stop_grace_period: 0s

  # Generic Worker responsible for Dossier Zipper Worker (DZW) and Single Semantic PDF generation, sends message to Rabbit M
  worker:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.157.4
    command: python manage.py worker
    restart: always
    networks:
      core-services:
      caddy:
    secrets:
      - source: DMS_ENV_V5
        target: /app/.env
    stop_grace_period: 0s

  # Document Manager Server
  dms:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.157.4
    command: bash ./start.sh
    # command: python -m uvicorn --lifespan auto projectconfig.asgi:application --host 0.0.0.0 --reload
    # command: python manage.py runserver 0.0.0.0:8000
    #command: sh -c "chpasswd && echo 'root:root' | chpasswd && mkdir -p /run/sshd && /usr/sbin/sshd -D -o PermitRootLogin=yes"
    #command: sh -c "mkdir -p /run/sshd && /usr/sbin/sshd -D -o PermitRootLogin=yes"
    networks:
      core-services:
      caddy:
    restart: on-failure
    secrets:
      - source: DMS_ENV_V5
        target: /app/.env
    stop_grace_period: 0s

  s3proxy:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.157.4
    command: sanic s3proxy.proxy -H 0.0.0.0
    networks:
      core-services:
      caddy:
    secrets:
      - source: DMS_ENV_V5
        target: /app/.env

volumes:
  step:
  pg-data:
  minio_data:
  rabbitmq-data:
  caddy_data:
  caddy_config:

secrets:
  RABBITMQ_CONFIG:
    file: conf/rabbitmq/rabbitmq.conf
  RABBITMQ_DEFINITIONS_BACKUP_CONFIG:
    file: conf/rabbitmq/.backup.env
  PGADMIN4_SERVER_CONFIG:
    file: conf/pgadmin/servers.json
  DMF_KEYCLOAK_CONFIG:
    file: conf/dmf/keycloak.json
  DMF_MANAGER_CONFIG_V2:
    file: conf/dmf/config.json
  DMS_ENV_V5:
    file: conf/dms/env
  KEYCLOAK_REALM_CONFIG_V5:
    file: conf/keycloak/realm-config.json
  KEYCLOAK_CONF_V4:
    file: conf/keycloak/keycloak.conf
networks:
  core-services:
    external: true
  caddy:
    external: true
