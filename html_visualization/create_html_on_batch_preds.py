import argparse
import ast
import pathlib

import pandas as pd
import numpy as np

"""

run: 
python -m html_visualization.create_html_on_batch_preds -f output/TABME_vision/dainty-sound-79/predictions/prediction_dainty-sound-79_test.csv
python -m html_visualization.create_html_on_batch_preds -f output/TABME_vision/dainty-sound-79/predictions/prediction_dainty-sound-79_test.csv -foi output/TABME_vision/dainty-sound-79/low_score_folder_list_val_split_df.csv

"""

A4_WIDTH = 210
A4_HEIGHT = 297
RATIO = 1.0
BLACK_COLOR = "#000000"
WHITE_COLOR = "#FFFFFF"
RED_COLOR = "#FF0000"
GREEN_COLOR = "#02BD21"
ORANGE_COLOR = "#E59200"
FOI_COLOR = "#5eebb7"


def create_index_page(save_folder, sorted_f1, f1_scores, foi_batch=None):
    index_html = "<!DOCTYPE html>\n<html>\n<body>\n"

    # create list of all batch pages
    index_html += "<ul>\n"
    for b, batch_sorted in enumerate(sorted_f1):
        text_color = BLACK_COLOR
        if foi_batch:
            if b in foi_batch:
                text_color = FOI_COLOR

        index_html += f'<li style="color:{text_color}"><a href="batch{b}.html"> Batch {b} </a>(F1: {f1_scores[batch_sorted]:.3f})</li>\n'
    index_html += "</ul>\n"

    # add closing html body
    index_html += "</body>\n</html>"

    with open(save_folder / f"index.html", "w") as f:
        f.write(index_html)


def create_batch_pages(n_batch, save_folder, foi_list=None):
    # for b in range(n_batch):
    all_f1, all_acc, all_preds, all_y_preds, all_labels, all_pdf_paths = (
        [],
        [],
        [],
        [],
        [],
        [],
    )  # probably a dict might start to make sense here :p
    for b in range(n_batch):
        # get batch
        batch = data[data["batch"] == b]

        # get value form batch
        loss = batch["loss"].values[0]
        f1 = batch["f1"].values[0]
        acc = batch["acc"].values[0]
        print(f"batch loss: {loss:.3f}, acc: {acc*100:.2f}%, f1: {f1:.3f}")
        all_f1.append(f1)
        all_acc.append(acc)

        # get batch values stored in list
        preds = [
            float(val)
            for val in batch["preds"]
            .values[0]
            .replace("[", "")
            .replace("]", "")
            .split()
        ]
        all_preds.append(preds)
        y_preds = [
            int(char)
            for char in batch["y_preds"]
            .values[0]
            .replace("[", "")
            .replace("]", "")
            .split()
        ]
        all_y_preds.append(y_preds)
        labels = [
            int(char)
            for char in batch["labels"]
            .values[0]
            .replace("[", "")
            .replace("]", "")
            .split()
        ]
        all_labels.append(labels)
        pdf_paths = ast.literal_eval(batch["pdf_paths"].values[0])
        all_pdf_paths.append(pdf_paths)

    # sort f1
    sorted_f1_index = np.argsort(all_f1)

    # Create HTML page
    foi_batch = set()
    for b, batch_sorted in enumerate(sorted_f1_index):
        # start page
        batch_html = "<!DOCTYPE html>\n<html>\n<body>\n"

        # add style
        batch_html += "<style>\n .flex-container {display: flex; flex-wrap: wrap;}\n .link {padding: 5px 5px 5px 5px}\n .center {display: flex; justify-content: center; align-items: center;}\n</style>\n"

        # Add html text for batch
        batch_html += f"<h1>Batch {b}</h1>\n"

        # add batch scores
        batch_html += f"<h2>F1: {all_f1[batch_sorted]:.3f} - Accuracy: {all_acc[batch_sorted]*100:.2f}%</h2>\n"

        # print value for each entry
        n_extra_cut, n_missing_cut = 0, 0
        content_text = ""

        warning_printed = False
        for i, (label, y_pred, pred, path) in enumerate(
            zip(
                all_labels[batch_sorted],
                all_y_preds[batch_sorted],
                all_preds[batch_sorted],
                all_pdf_paths[batch_sorted],
            )
        ):
            path = path.replace("feyn_doc_split_20240430", "Vigilant-Fox")
            if not warning_printed:
                print(
                    f"[WARNING] batch {b}, path editing in line 94 should be removed!"
                )
                warning_printed = True

            if i == 0:
                content_text += (
                    f'<div id="cut{i}" class="flex-container">'  # create first div
                )
            elif y_pred == 1:
                content_text += f'</div>\n <hr> \n <div id="cut{i}" class="flex-container">'  # add a new div with a line in between
            # print(f'{label} - {y_pred} ({pred:.3f}): {path}')

            # set colors to wrong cuts
            bckg_color = WHITE_COLOR
            text_color = BLACK_COLOR
            bold_text = ["", ""]  # add a bold balise around text to see better
            class_text = ""
            # if cut is done but should not be done (extra cut) -> red
            if y_pred == 1 and label == 0:
                n_extra_cut += 1
                bckg_color = RED_COLOR
                text_color = WHITE_COLOR
                bold_text = ["<b>", "</b>"]
            # if we should have cut but didn't (missing cut) -> orange
            if y_pred == 0 and label == 1:
                n_missing_cut += 1
                bckg_color = ORANGE_COLOR
                text_color = WHITE_COLOR
                bold_text = ["<b>", "</b>"]

            # check if the image is from a folder of interest
            folder_name = path.split("/")[-3]
            class_text = f"<br>{folder_name}"

            # add border around the image if found in the foi_list
            if folder_name in foi_list:
                border_text = f'style="border-width: 5px; border-style:solid; border-color: {FOI_COLOR}"'
                # get class dificulty (ranking or order of the list)
                difficulty = foi_list.index(folder_name)  # type: ignore
                class_text = f"<br>{folder_name} ({difficulty})"
                foi_batch.add(b)
            else:
                border_text = ""
                class_text = f"<br>{folder_name}"

            # add image
            content_text += f'<div style="color: {text_color}; background-color:{bckg_color}; padding: 5px 5px 5px 5px"> <p style="text-align: center;"> {bold_text[0]}{pred*100:.2f}%{class_text}{bold_text[1]} </p> <embed src="{path}" {border_text} width="{RATIO * A4_WIDTH}px" height="{RATIO * A4_WIDTH}px" ></embed> </div>'
        content_text += "</div>"
        # add text to page
        batch_html += (
            f"<h2>Extra cut: {n_extra_cut} - Missing cut: {n_missing_cut}</h2>\n"
        )
        batch_html += content_text + "\n"

        # add link to prev and next pages
        batch_html += f'<hr>\n<div class="center">\n<div class="flex-container" style="height:100px;">\n<a class="link" href="index.html">index</a>\n'
        if b == 0 and n_batch > 0:
            batch_html += f'<a href="batch{b+1}.html" class="link">next ></a>\n'
        elif b == n_batch - 1:
            batch_html += f'<a href="batch{b-1}.html" class="link">< prev</a>\n'
        else:
            batch_html += f'<a href="batch{b-1}.html" class="link">< prev</a><a href="batch{b+1}.html" class="link">next ></a>\n'
        batch_html += f"</div>\n</div>\n"

        # end div and page body
        batch_html += "</body>\n</html>"
        with open(save_folder / f"batch{b}.html", "w") as f:
            f.write(batch_html)

    return sorted_f1_index, all_f1, foi_batch


def create_html(data: pd.DataFrame, foi_list=None):
    n_batch = len(data["batch"].unique())
    print("num_batch: ", n_batch)

    # create save directory
    save_folder = pathlib.Path("html_visualization") / "pages"
    save_folder.mkdir(parents=True, exist_ok=True)

    # create batch pages
    sorted_f1, f1_scores, foi_batch = create_batch_pages(
        n_batch, save_folder, foi_list=foi_list
    )

    # create index page
    create_index_page(save_folder, sorted_f1, f1_scores, foi_batch)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train the model")
    parser.add_argument(
        "-f", "--file_name", type=str, required=True, help="file name path"
    )
    parser.add_argument(
        "-foi",
        "--foi",
        type=str,
        required=False,
        default=None,
        help="file name of the list of folder of interest to highlight",
    )
    args = parser.parse_args()

    # load data
    data = pd.read_csv(args.file_name, index_col=0)

    # look for the File list Of Interest (FOI)
    foi_list = None
    if args.foi:
        foi_path = pathlib.Path(args.foi)
        if foi_path.is_file():
            foi_list = pd.read_csv(foi_path, index_col=0)
            foi_list = [row["folder_name"] for _, row in foi_list.iterrows()]
        else:
            print(f"{foi_path} does not exists!")

    # create_html
    create_html(data, foi_list)
