from pathlib import Path
import typer
from rich.console import Console
import tqdm
import json
from multiprocessing import Pool, cpu_count
from functools import partial
from PIL import Image


from pdf2image import convert_from_path

"""
This script takes into an input folder with multiple sausages of the following form:

input_path/
├── pdfs/
│   ├── folder1_CU.pdf    (i.e. 3 pages pdf)
│   ├── folder1_DI.pdf    (i.e. 5 pages pdf)
│   ├── folder1_KA.pdf    
│   ...    
├── labels.json  

and create a new folder containing each individual page of the pdfs, such as: 

input_path/
├── images/
│   ├── folder1_CU_0.png     (single image)
│   ├── folder1_CU_1.png
│   ├── folder1_CU_2.png
│   ├── folder1_DI_0.png    
│   ├── folder1_DI_1.png    
│   ├── folder1_DI_2.png    
│   ├── folder1_DI_3.png    
│   ├── folder1_DI_4.png    
│   ├── folder1_KA_0.png    
│   ├── folder1_KA_1.png    
...

python -m prepare_labelled_pdf_to_png /home/<USER>/ml/data/2024_10_28_PSS_VZ_2
python -m prepare_labelled_pdf_to_png sample/inference/sample_sausage --extension jpg
python -m prepare_labelled_pdf_to_png /home/<USER>/ml/data/2025_02_14_PSS_VZ3 --extension png

"""

app = typer.Typer()


def process_pdf(pdf, input_path, output_path, config):
    pdf_name = pdf["pdf_name"]
    pdf_path = input_path / pdf_name

    images = convert_from_path(pdf_path)
    for i, image in enumerate(images):
        # Save img to folder
        image.save(
            output_path / f"{Path(pdf_name).with_suffix('')}_{i}.{config['extension']}"
        )


def test_image(image_path):
    try:
        img = Image.open(image_path)
        img.verify()  # Verify the integrity of the image
        img = Image.open(image_path)  # Reopen it after verifying
        img.load()
    except (IOError, OSError) as e:
        print("Error loading image:", e)


# Main function that sets up paths, loads data, and starts multiprocessing
def create_images(config):
    # Set up paths
    input_path = Path(config["input_path"])
    output_path = Path(config["output_path"])

    # Create output folder
    output_path.mkdir(parents=True, exist_ok=True)

    # check if we have a label file
    if (input_path / "labels.json").is_file():

        # Load labels
        with open(input_path / "labels.json") as f:
            json_data = json.load(f)

        input_path = input_path / "pdfs"

        # count number of files/pages
        n_files = len(json_data["pdfs"])
        n_pages = sum([len(pdf["labels"]) for pdf in json_data["pdfs"]])
        console.print(f"Found {n_files} files with {n_pages} pages in total")
    else:
        console.print("No labels.json file found in input folder")
        console.print("Load all pdfs in input folder")

        # Load all pdfs in input folder
        pdfs = list(input_path.glob("*.pdf"))

        # create a json dict to mimic labels.json
        json_data = {"pdfs": [{"pdf_name": pdf.name} for pdf in pdfs]}

        n_pages = len(pdfs)

    # count files in image folder
    n_files_in_image_folder = len(list(output_path.glob(f"*.{config['extension']}")))
    console.print(f"Found {n_files_in_image_folder} images in {output_path}")

    # Prepare multiprocessing pool
    num_workers = min(min(cpu_count(), len(json_data["pdfs"])), 4)
    with Pool(processes=num_workers) as pool:
        # Use partial to fix input_path and output_path for each process
        process_func = partial(
            process_pdf, input_path=input_path, output_path=output_path, config=config
        )

        # Map over each pdf
        list(
            tqdm.tqdm(
                pool.imap_unordered(process_func, json_data["pdfs"]),
                total=len(json_data["pdfs"]),
            )
        )

    # Print results
    # count files in image folder
    n_files_in_image_folder = len(list(output_path.glob(f"*.{config['extension']}")))
    console.print(f"Created {n_files_in_image_folder} images in {output_path}")
    assert (
        n_files_in_image_folder >= n_pages
    ), "Number of images created does not match number of pages"

    # control images
    image_list = list(output_path.glob(f"*.{config['extension']}"))
    with Pool(processes=num_workers) as pool:
        # Map over each pdf
        list(
            tqdm.tqdm(
                pool.imap_unordered(test_image, image_list),
                total=len(image_list),
            )
        )


@app.command()
def run(
    input_path: str,
    output_path: str = "output/converted_images",
    extension: str = "png",
):

    # declare a config dict
    config = {}
    config["input_path"] = input_path
    config["output_path"] = output_path
    config["extension"] = extension

    # count word in dataset
    create_images(config)


if __name__ == "__main__":
    console = Console(record=True, quiet=False)
    try:
        app()
    except SystemExit as e:
        if e.code == 0:
            pass  # Suppress the traceback for successful exits
        else:
            raise  # Re-raise the exception for non-zero exit codes to show the traceback
    except:
        console.print_exception(show_locals=True)
    print("Saved logs to console.log")
    console.save_text("console.log")
