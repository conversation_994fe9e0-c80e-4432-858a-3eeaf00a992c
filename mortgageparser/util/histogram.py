from collections import OrderedDict
from dataclasses import dataclass, field


@dataclass
class Histogram:
    data: dict = field(default_factory=dict)

    def add(self, key, val: float = 1):
        if not self.data:
            self.data = {}
        if key not in self.data:
            self.data[key] = 0
        self.data[key] = self.data[key] + val

    def get_sorted_by_key(self, reverse=False):
        return OrderedDict(sorted(self.data.items(), reverse=reverse))

    def get_sorted_by_value(self, reverse=False):
        return OrderedDict(
            sorted(self.data.items(), key=lambda item: item[1], reverse=reverse)
        )

    def get_sum_values(self):
        v = 0
        for x in self.data.values():
            v += x
        return v


@dataclass
class HistogramEntry:
    count: int = 0
    sum: float = 0


@dataclass
class HistogramWithCount:
    data: dict = field(default_factory=dict)

    def add(self, key, val: float):
        if not self.data:
            self.data = {}
        if key not in self.data:
            self.data[key] = HistogramEntry()
        self.data[key].count += 1
        self.data[key].sum += val

    def get_sorted_by_key(self, reverse=False):
        return OrderedDict(sorted(self.data.items(), reverse=reverse))

    def get_sorted_by_value(self, reverse=False):
        return OrderedDict(
            sorted(self.data.items(), key=lambda item: item[1].sum, reverse=reverse)
        )

    def get_sum_values(self):
        v = 0
        for entry in self.data.values():
            v += entry.sum
        return v

    def get_sum_counts(self):
        v = 0
        for entry in self.data.values():
            v += entry.count
        return v
