from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.bank_account.bank_account_util import (
    GenericBankDocumentPageParser,
)
from mortgageparser.documents.parser.pageparsers.payslip.PayslipPageParser import (
    PayslipPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.CommonParsersDE import (
    ClassifierPensionCertificatePageTwoPageParser,
    ClassifierPensionCertificatePageThreePageParser,
    CLASSIFIER_PENSION_CERTIFICATE_PAGE_ONE_PARSER,
)
from mortgageparser.documents.parser.pageparsers.property_insurance.GenericLegacyPropertyInsurancePageParser import (
    GenericLegacyPropertyInsurancePageParser,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_util import (
    PageCfg,
)

page_configurations_en = [
    PageCfg(
        prefix="EN/322/Bank",
        confidence_threshold=0.7,
        parser=GenericBankDocumentPageParser(),
    ),
    PageCfg(
        prefix="EN/340/Salärabrechnung",
        confidence_threshold=0.8,
        parser=PayslipPageParser(),
    ),
    # strict because quality very good
    # 350 is legacy, new number is 345
    PageCfg(
        prefix="EN/345/Bonus",
        confidence_threshold=0.75,
        doc_cat=DocumentCat.SALARY_BONUS,
    ),
    PageCfg(
        prefix="EN/350/Bonus",
        confidence_threshold=0.75,
        doc_cat=DocumentCat.SALARY_BONUS,
    ),  # legacy_doc_cat_id
    # Copied from DE, could need adjustment
    PageCfg(
        prefix="EN/410/Pensionskassenausweis/Erste Seite",
        confidence_threshold=0.8,
        min_chars_alpha_per_page=500,
        min_num_lines=10,
        parser=CLASSIFIER_PENSION_CERTIFICATE_PAGE_ONE_PARSER,
    ),
    PageCfg(
        prefix="EN/410/Pensionskassenausweis/Zweite Seite",
        confidence_threshold=0.75,
        min_chars_alpha_per_page=200,
        min_num_lines=10,
        parser=ClassifierPensionCertificatePageTwoPageParser(),
    ),
    PageCfg(
        prefix="EN/410/Pensionskassenausweis/Dritte Seite",
        confidence_threshold=0.75,
        min_chars_alpha_per_page=200,
        min_num_lines=10,
        parser=ClassifierPensionCertificatePageThreePageParser(None, None),
    ),
    PageCfg(
        prefix="EN/410/Pensionskassenausweis/Brief",
        confidence_threshold=0.7,
        page_cat=PageCat.PENSION2_CERTIFICATE_LETTER,
    ),
    PageCfg(
        prefix="EN/410/Pensionskassenausweis/Info-Seite",
        confidence_threshold=0.7,
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
    ),
    PageCfg(
        prefix="EN/424/Freizügigkeitskonto",
        confidence_threshold=0.9,
        doc_cat=DocumentCat.VESTED_BENEFITS_ACCOUNT,
    ),
    PageCfg(
        prefix="EN/530/Jahresrechnung Firma Inhalt",
        doc_cat=DocumentCat.FINANCIAL_STATEMENT_COMPANY,
        page_cat=PageCat.FINANCIAL_STATEMENT_COMPANY_CONTENT,
    ),
    PageCfg(
        prefix="EN/617",
        confidence_threshold=0.5,
        parser=GenericLegacyPropertyInsurancePageParser(),
    ),
    # Min Threshold overall for EN if not defined less strict above
    PageCfg(prefix="EN/", confidence_threshold=0.8),
]
