version: '3.8'
services:
  dossier-frontend:
    image: registry.gitlab.com/hypodossier/dossier-manager-frontend:${TAG-latest}
    configs:
      - source: KEYCLOAK_CONFIG
        target: /srv/build/keycloak.json
      - source: MANAGER_CONFIG_V6
        target: /srv/build/config/config.json
    networks:
      caddy:
    deploy:
      labels:
        caddy: service.${BASE_DOMAIN-hypo.duckdns.org}
        caddy.reverse_proxy: "/* {{upstreams 80}}"
        caddy.import: tls
    healthcheck:
      test: wget -O /dev/null -q 127.0.0.1:2019/config
      start_period: 5s
      interval: 5s
      timeout: 5s
      retries: 20

configs:
  KEYCLOAK_CONFIG:
    file: deployment/dev/keycloak.json
  MANAGER_CONFIG_V6:
    file: deployment/dev/config.json

networks:
  caddy:
    external: true
