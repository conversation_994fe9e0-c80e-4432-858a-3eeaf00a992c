import logging
import os
from pathlib import Path

import structlog
from pydantic import BaseSettings, HttpUrl, AmqpDsn

logger = structlog.get_logger()
class Settings(BaseSettings):
    classifier_api_base_url: HttpUrl = "https://classifier.swarm-v2.hypodossier.ch/api"
    labeling_api_base_url: HttpUrl = "https://classifier.hypo.duckdns.org/labeling/api"
    rabbitmq_url: AmqpDsn = "amqp://admin:<EMAIL>/dossier-processor"
    token_url: str = "https://auth.hypodossier.ch/auth/realms/Hypodossier/protocol/openid-connect/token"
    auth_url: str = "https://auth.hypodossier.ch/auth/realms/Hypodossier/protocol/openid-connect/auth"
    client_id: str = "hylayout"
    client_secret: str = "cf7ed8c5-f3e7-477f-9579-e2b9b15ac673"

    # This config is used to load the models, only needs read-only access
    s3_host: str = "minio.hypo.duckdns.org"
    s3_access_key: str = "S3_ACCESS_KEY"
    s3_secret_key: str = "S3_SECRET_KEY"
    s3_secure: bool = True
    s3_region: str = "ch-dk-2"

    # This config is used to upload test input documents, needs read/write/create bucket/...
    # Does not need to be configured for production
    test_s3_host: str = "minio.hypo.duckdns.org"
    test_s3_access_key: str = "S3_ACCESS_KEY"
    test_s3_secret_key: str = "S3_SECRET_KEY"
    test_s3_secure: bool = True
    test_s3_region: str = "ch-dk-2"


    debug_log_page_word_classifications: bool = True

    debug_log_memory_consumption: bool = False
    release_memory_aggressively: bool = False

    PRETTY_PRINT_LOG: bool
    ENABLE_GPU: bool = False
    SET_GPU_MEMORY_LIMIT: bool = True
    LOGICAL_GPU_MEMORY_LIMIT_GB: int = 4
    log_timing_to_file: bool = False
    timing_log_file_suffix: str = "model_key_model_loading_time_prediction_time.csv"
    gpu_model_queue_length: int = 1
    cpu_model_queue_length: int = 1
    model_cache_timeout_seconds: float = 60 * 60 # 1 hour

    class Config:
        env_file = '.env'

def configure_logger():
    nice = settings.PRETTY_PRINT_LOG
    shared_processors = [
        # Processors that have nothing to do with output,
        # e.g., add timestamps or log level names.
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
        structlog.processors.TimeStamper(fmt="iso"),
    ]

    if nice:
        # Pretty printing when we run in a terminal session.
        # Automatically prints pretty tracebacks when "rich" is installed
        processors = shared_processors + [
            structlog.processors.TimeStamper(fmt="iso", utc=False),
            structlog.dev.ConsoleRenderer(),
        ]
    else:
        # Print JSON when we run, e.g., in a Docker container.
        # Also print structured tracebacks.
        processors = shared_processors + [
            structlog.processors.dict_tracebacks,
            structlog.processors.JSONRenderer(),
        ]

    # noinspection PyTypeChecker
    structlog.configure(
        processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def load_secret_from_file(key: str) -> str:
    if os.getenv(key):
        p = Path(os.getenv(key))
        if p.exists():
            logger.info(f"Load secret {key} from file: {p}")
            return p.read_text()
        else:
            logger.error(
                f"Tried to load secret {key} from file but it did not exist: {p}"
            )


logging.basicConfig(level=logging.INFO)

logger.info("Now initializing settings...")
logger.info("Settings file path : ", settings_file_path=Path(__file__).parent / "../../.env")
settings = Settings(Path(__file__).parent / "../../.env")

logger.info(
    "Settings initialized, now checking if secrets need to be loaded from files..."
)

logger.info(
    "Settings fully initialized... settings.PRETTY_PRINT_LOG: ",
    settings_PRETTY_PRINT_LOG=settings.PRETTY_PRINT_LOG,
)
logger.info("settings.ENABLE_GPU: ", settings_ENABLE_GPU=settings.ENABLE_GPU)
logger.info(
    "settings.LOGICAL_GPU_MEMORY_LIMIT_GB: ",
    settings_LOGICAL_GPU_MEMORY_LIMIT_GB=settings.LOGICAL_GPU_MEMORY_LIMIT_GB,
)
logger.info("settings.LOG_TIMING: ", settings_log_timing_to_file=settings.log_timing_to_file)
logger.info(
    "settings.timing_log_file_suffix: ",
    settings_timing_log_file_suffix=settings.timing_log_file_suffix,
)
logger.info(
    "settings.gpu_model_queue_length: ",
    settings_gpu_model_queue_length=settings.gpu_model_queue_length,
)
logger.info(
    "settings.cpu_model_queue_length: ",
    settings_cpu_model_queue_length=settings.cpu_model_queue_length,
)
logger.info(
    "settings.model_cache_timeout_seconds: ",
    settings_model_cache_timeout_seconds=settings.model_cache_timeout_seconds,
)




