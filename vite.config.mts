import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import alias from '@rollup/plugin-alias';
import path from 'path';
import { resolve } from "path";

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@components': path.resolve(__dirname, 'src/components'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@hooks': path.resolve(__dirname, 'src/utils/hooks')
    }
  },
  // activate this line for sfex web component deployment
  // remove this line for normal SPA deployment
  base: './',
  build: {
    outDir: path.resolve(__dirname, 'build'),

    rollupOptions: {
      input: {
        index: resolve(__dirname, "index.html"),
        "webcomponents-bundle": resolve(__dirname, "src/SwissFex/webcomponents.tsx")
      },
      output: {
        entryFileNames: 'entrypoints/[name]-[hash].js'
      }
    }
  },
  server: {
    allowedHosts: [
      'service.hypo.duckdns.org'
    ]
  }
});
