import sys
from dataclasses import replace, dataclass
from pathlib import Path
from typing import List

from abbyyplumber.api import Page, get_bounding_box, find_characters_as_search_result
from abbyyplumber.plumberstudio.Char import Char
from abbyyplumber.plumberstudio.char_util import (
    clean_and_format_chars,
    find_chars_by_baseline,
)
from hypodossier.core.domain.TextContentStats import TextContentStats
from hypodossier.util.language_detector import get_lang_code_for_swiss_text

import structlog

logger = structlog.getLogger(__name__)

# Language that will be set if no language can be found. Either 'de' or None
DEFAULT_LANG_FOR_PAGE = "de"


class DocumentLoaderException(Exception):
    pass


# The doc is no valid PDF document
class DocumentLoaderInvalidPdfException(Exception):
    pass


# This means that we should try another type of OCR to read the doc and not rely on the
# existing font
class DocumentLoaderCidFontException(DocumentLoaderException):
    pass


# It can be empty because it really contains no text or because the text has not been detected
# Try to make it searchable again with the Engine just to be sure
class DocumentLoaderEmptyException(DocumentLoaderException):
    pass


@dataclass
class DocumentLoader:
    valid_empty_pages: List[str] = None
    do_add_spacing_on_line: bool = True
    do_validate_page: bool = False
    coord_multiple: int = -1
    line_height_tolerance: int = -1

    path_pdf_for_image: Path = None

    # Line height in proportion to the effective height of the character is very different between
    # XML parsing (narrow box) and PDF parsing (high box)
    # This is used to manage the difference for find operations
    line_height_factor: int = 1

    def create_page(
        self,
        chars,
        page_source,
        bbox_page,
        add_text_border=False,
        default_lang=DEFAULT_LANG_FOR_PAGE,
        page_source_original=None,
    ):
        chars = clean_and_format_chars(
            chars,
            self.line_height_tolerance,
            do_add_spacing_on_line=self.do_add_spacing_on_line,
        )
        bbox_text = get_bounding_box(chars)

        if add_text_border:
            # bbox_text.left -= min(self.line_height_tolerance, 0)
            bbox_text.right += self.line_height_tolerance
            bbox_text.bottom += self.line_height_tolerance

        page = Page(
            chars,
            page_source.page_index,
            bbox_page,
            bbox_text=bbox_text,
            page_source=page_source,
            coord_multiple=self.coord_multiple,
            line_height_factor=self.line_height_factor,
            path_pdf_for_image=self.path_pdf_for_image,
            page_source_original=page_source_original,
        )

        text = page.get_text()
        if chars:
            self.add_search_result_by_baseline(page, chars[0], "page_first_line", True)
            self.add_search_result_by_baseline(page, chars[-1], "page_last_line", True)

            page.lang = get_lang_code_for_swiss_text(text, default_lang=default_lang)
        else:
            page.lang = default_lang
        page.text_content_stats = TextContentStats.create(text)

        return page

    def add_search_result_by_baseline(
        self, page: Page, char: Char, name: str, extract: bool
    ):
        chars_first = find_chars_by_baseline(char.baseline, page.chars)
        sr = find_characters_as_search_result(
            chars_first, page.bbox_text, name, extract=extract
        )
        if sr:
            sr.extract = False
            page.search_results.append(sr)

    def validate_page(self, page: Page, min_chars=100):
        if not self.do_validate_page:
            return

        # Check if characters outside page exists
        # That is the case if bbox_text is larger / outside bbox

        if page.bbox_text.intersect(page.bbox) != page.bbox_text:
            diff_left = replace(
                page.bbox_text,
                right=page.bbox.left - 1,
                top=-sys.maxsize,
                bottom=sys.maxsize,
            )
            text_left = page.find_characters(
                diff_left, "left of page border", extract=True
            )

            diff_right = replace(
                page.bbox_text,
                left=page.bbox.right + 1,
                top=-sys.maxsize,
                bottom=sys.maxsize,
            )
            text_right = page.find_characters(
                diff_right, "right of page border", extract=True
            )

            raise DocumentLoaderException(
                f"Found character outside page. bbox_text={page.bbox_text} with page={page.bbox}. text_left={text_left}, content='{text_left.get_text()}', text_right={text_right}, content='{text_right.get_text()}'"
            )

        self.validate_empty_page(page, min_chars)

    def validate_empty_page(self, page: Page, min_chars=100):
        idx = page.page_source.page_index
        filename = page.page_source.filename

        num_chars = len(page.get_text())
        if num_chars < min_chars:
            if self.valid_empty_pages is None:
                logger.info(
                    "Found empty page -> process normally",
                    filename=filename,
                    index=idx,
                )
            elif (
                self.valid_empty_pages and f"{filename} {idx}" in self.valid_empty_pages
            ):
                logger.info("Ignore valid empty page", filename=filename, index=id)
            elif page.num_cid_chars > 100:
                raise DocumentLoaderCidFontException(
                    f"No characters on the page but found {page.num_cid_chars} (cid:XX) characters. Try other OCR method."
                )

            else:
                raise DocumentLoaderEmptyException(
                    f"Is the page not searchable or empty? I could not extract any text for page {idx} from path {page.page_source.path}"
                )
