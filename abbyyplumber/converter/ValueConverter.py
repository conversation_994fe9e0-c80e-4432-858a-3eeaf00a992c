import re
import sys
from abc import abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List

from regex import regex

from abbyyplumber.util.basic_string_utils import search_ahv_new
from abbyyplumber.util.currency_util import clean_number_text
from hypodossier.util.address_util import clean_address_line
from hypodossier.util.basis_string_util import (
    count_alpha,
    find_nth,
    remove_leading_title_from_name,
    clean_name,
    substring_before_newline,
    calc_string_stats,
    capitalize_uppercase,
    count_digit,
)
from hypodossier.util.canton_util import get_canton_shortcode
from hypodossier.util.constants import SPACER_CHAR
from hypodossier.util.date_util import (
    find_date,
    find_most_recent_date,
    find_earliest_date,
    MAX_YEAR_FOR_BIRTH_DATE_DETECTION,
)
from hypodossier.util.iban_util import get_first_iban_in_string
from mortgageparser.util.string_utils import (
    DEFAULT_HAMMING_DISTANCE,
    substring_after,
    substring_before,
)

import structlog

logger = structlog.getLogger(__name__)


class ConverterException(Exception):
    pass


@dataclass
class ValueConverter:
    @abstractmethod
    def convert(self, s: str):
        pass


class DebugConverter(ValueConverter):
    def convert(self, s: str):
        return s


class DoNothingConverter(ValueConverter):
    def convert(self, s: str):
        return s


@dataclass
class CompositeConverter(ValueConverter):
    converters: List[ValueConverter] = field(default_factory=list)

    def convert(self, s: str):
        if s:
            s2 = s
            for c in self.converters:
                s2 = s2.strip()
                s2 = c.convert(s2)
                if not s2:
                    break
            return s2
        return s


INVALID_LAND_REGISTER_AREA = -1


@dataclass
class LandRegisterAreaConverter(ValueConverter):
    min: int = 30
    max: int = 30000

    def convert(self, s: str):
        if s:
            val = s.replace("'", "").replace("’", "").replace(" ", "")
            try:
                i = int(val)
                if self.min <= i <= self.max:
                    return i
            except:
                pass
        return INVALID_LAND_REGISTER_AREA


@dataclass
class NewAhvConverter(ValueConverter):
    def convert(self, s: str, aggressive_conversion=True):
        val = s.replace(",", ".")
        if aggressive_conversion:
            val = val.replace("O", "0").replace("Q", "0")

        val = search_ahv_new(val)
        # val = re.sub("[^0-9\\.]", "", val)
        return val


@dataclass
class CurrencyConverter(ValueConverter):
    min_amount: int = None
    max_amount: int = 10000000
    cut_off_fractions: bool = True
    remove_non_digits: bool = True

    # If set to True this replaces '4 3 2 1' by '4 3 2'. If False this returns '4321'
    # If we know that the value is not in a box with a vertical line after the number this should be False
    cut_off_single_char_right: bool = True

    fail_if_multiple_lines: bool = True
    max_allowed_length: int = 15  # 8 digit number with 2 separators (') and .00
    allow_negative_values: bool = False
    multiplier: float = 1

    # OCR sometimes detects a 1 if there is a box around a number which is actually the righthand border of the box
    # So by default if the last digit is a 1 and the separators do not match groups of 3 numbers, cut off the trailing 1
    cut_off_trailing_one_with_separators: bool = True

    def convert(self, s: str):
        # Do basic cleanup of string that is applied for all conversions
        if not s:
            return None

        val = s.replace(SPACER_CHAR, " ")
        val = val.replace("*", " ").replace("+", " ").replace("=", " ")
        val = val.strip()

        if self.fail_if_multiple_lines and "\n" in val:
            logger.warning(
                "Invalid currency value because it contains a newline", val=val
            )
            return None
        else:
            # cut off everything after newline
            chunks = val.split("\n")
            val_first_line = chunks[0].strip()

            # Now test if first line contains only special chars
            if val_first_line and self.remove_non_digits:
                check = re.sub("[^0-9]", "", val_first_line)
                if check:
                    val = val_first_line
                if not check:
                    if len(chunks) == 1:
                        val = val_first_line
                    else:
                        # First line is only special chars, try second
                        val = val.split("\n")[1].strip()

        if self.max_allowed_length:
            real_length = len(val.replace(" ", ""))
            if real_length > self.max_allowed_length:
                logger.warning(
                    "Invalid currency value because too long", input_string=s
                )
                return None

        if self.cut_off_trailing_one_with_separators:
            # if a digit one is found at the end of the currency string and the thousands separator is
            # at the wrong place then probably the trailing 1 is actually a vertical line. Therefore cut of the 1
            # Example: 6'4321 is probably 6'432|.
            # For some reason everything must be in a capturing group ( )
            regex = r"""
                (.*[' ]\d\d\d)(1.*)
                """
            match = re.search(regex, val, re.VERBOSE)
            if match:
                val = match.group(1)

        if val and self.remove_non_digits:
            val = re.sub("[^0-9., -]", "", val)

        val = val.strip()
        # Do not cut off newlines as this might remove the number itself
        val = clean_number_text(
            val,
            cut_off_single_char_right=self.cut_off_single_char_right,
            cut_off_fractions=self.cut_off_fractions,
            cut_off_newlines=False,
            allow_negative_values=self.allow_negative_values,
        )
        if val == "-":
            return None

        if val and (self.min_amount or self.max_amount):
            try:
                v = int(val)
            except Exception:
                return None

            if self.multiplier:
                v *= self.multiplier
                val = str(v)

            if self.min_amount and v < self.min_amount:
                val = None
            if self.max_amount and v > self.max_amount:
                val = None

        if not val:
            return None

        return val


# "Reasonable amounts" for salary. If outside of this range then better do not parse anything
salary_converter = CurrencyConverter(min_amount=500, max_amount=500000)


@dataclass
class RegexConverter(ValueConverter):
    regex: str

    # Return match with this index
    match_index: int = 0

    # Set this to true for fuzzy matches ({e<=2})
    overlapped: bool = False

    def convert(self, s: str):
        if self.regex and s:
            matches = regex.findall(self.regex, s, overlapped=self.overlapped)
            if matches:
                found = None
                if self.overlapped:
                    # matches are fuzzy ({e<=2}) and result structure is [(a, 1), (b, 2)] where we want 1 or 2
                    found = matches[0][self.match_index]
                else:
                    # matches are non-fuzzy and result structure is ('1', '2') where we want 1 or 2
                    found = matches[self.match_index]

                return found


@dataclass
class YearConverter(ValueConverter):
    allow_two_digt_year: bool = False

    min_value: int = 1900
    max_value: int = 2099

    remove_non_digits: bool = False

    def convert(self, s: str):
        if s and self.remove_non_digits:
            s = re.sub("[^0-9]", "", s)

        regex = r"""
                            (20|19|18)(\d\d)   # year (20 or 19 then always 2 digit)
                        """
        if self.allow_two_digt_year:
            regex = r"""
                                        (20|19|18)*(\d\d)   # year (optionally 20 or 19 then always 2 digit)
                                    """
        match = re.search(regex, s, re.VERBOSE)

        if match:
            year = match.group(2)
            if match.group(1):
                year = f"{match.group(1)}{year}"
            elif self.allow_two_digt_year:
                year = f"20{year}"
            if len(year) == 4:
                success = True
                if self.min_value:
                    if int(year) < self.min_value:
                        success = False
                if self.max_value:
                    if int(year) > self.max_value:
                        success = False
                if success:
                    return year

            else:
                raise ValueError(f'Invalid year "{year}" extracted from text="{s}"')


@dataclass
class DateConverter(ValueConverter):
    convert_text_months: bool = True
    do_raise_exception: bool = False

    def convert(self, s: str):
        result = find_date(s, self.convert_text_months, self.do_raise_exception)
        if result:
            return result
        else:
            if self.do_raise_exception:
                raise ValueError(f'Could not extract any date from text="{s}"')


class ReduceWhitespaceConverter(ValueConverter):
    def convert(self, s: str):
        if not s:
            return s
        return re.sub(" {2,}", " ", s)


@dataclass
class MostRecentDateConverter(ValueConverter):
    def convert(self, s: str):
        result = find_most_recent_date(s)
        return result


@dataclass
class CleanIBANConverter(ValueConverter):
    def convert(self, s: str):
        ret = get_first_iban_in_string(s)
        if ret:
            iban_clean, iban_source = ret
            return iban_clean


@dataclass
class BirthDateConverter(ValueConverter):
    max_year: int = MAX_YEAR_FOR_BIRTH_DATE_DETECTION

    def convert(self, s: str):
        result = find_earliest_date(s, max_year=self.max_year)
        return result


@dataclass
class StringSelectorConverter(ValueConverter):
    """
    Deliver a string after a certain prefix or before a certain suffix
    from prefix, prefixes, suffix, suffix only first occurence is applied.
    If prefix, suffix is not found the full string is returned.
    """

    prefix: str = None
    prefixes: List[str] = None
    include_prefix: bool = False
    suffix: str = None
    suffixes: List[str] = None
    include_suffix: bool = False

    dist = DEFAULT_HAMMING_DISTANCE

    def convert(self, s: str):
        s2 = s
        s3 = s
        if s2:
            if self.prefix and self.prefix in s2:
                s3 = substring_after(
                    s2, self.prefix, include_needle=self.include_prefix
                )
            if self.suffix and self.suffix in s2:
                s3 = substring_before(
                    s2, self.suffix, include_needle=self.include_suffix
                )
            if self.prefixes:
                for prefix in self.prefixes:
                    s3 = substring_after(s2, prefix, include_needle=self.include_prefix)
                    if s3 != s2:
                        break
            if self.suffixes:
                for suffix in self.suffixes:
                    s3 = substring_before(
                        s2, suffix, include_needle=self.include_suffix
                    )
                    if s3 != s2:
                        break

        return s3.strip()


@dataclass
class ValidNumberConverter(ValueConverter):
    min: int = 0
    max: int = sys.maxsize

    # If this is set, everything after the first occurrence of this string (including the string) will be removed
    suffix_ignore_after: str = None

    ignore_thousands_separator: bool = False
    valid_thousand_separators: List[str] = field(
        default_factory=lambda: ["'", "`", "'"]
    )

    def convert(self, s: str):
        if s:
            s2 = s.strip()
            if self.ignore_thousands_separator:
                for sep in self.valid_thousand_separators:
                    s2 = s2.replace(sep, "")

            if self.suffix_ignore_after and self.suffix_ignore_after in s2:
                if s2.index(self.suffix_ignore_after):
                    s2 = s2[: s2.index(self.suffix_ignore_after)]
                    s2 = s2.strip()

            s2 = s2.replace(" ", "")
            try:
                zip = int(s2)
                if self.min <= zip <= self.max:
                    return str(zip)
            except:
                pass


AREA_NUMBER_CONVERTER = ValidNumberConverter(suffix_ignore_after=" m")


class ValidZipCodeConverter(ValueConverter):
    def convert(self, s: str):
        return ValidNumberConverter(min=1000, max=9999).convert(s)


@dataclass
class StringConstantConverter(ValueConverter):
    # Returns always 'value' and ignores the input
    value: str

    def convert(self, s: str):
        return self.value


@dataclass
class DictConverter(ValueConverter):
    dict: Dict

    def convert(self, s: str):
        if s and self.dict:
            if s in self.dict:
                v = self.dict[s]
                return v
            else:
                return ""


MAX_NUM_LINES_UNLIMITED = 99999


@dataclass
class ParagraphConverter(ValueConverter):
    do_replace_underscore_char: bool = (
        True  # replace all underscore characters with space
    )
    do_replace_spacer_char: bool = True  # replace the special SPACER_CHAR with ' '
    min_num_lines: int = -1  # return empty if not at least this many lines found
    min_char_per_line: int = -1  # ignore line if not at least this many chars
    min_decimal_per_line: int = -1  # If set, all lines with fewer decimals are ignored
    max_decimal_per_line: int = -1  # If set, all lines with more decimals are ignored
    min_alpha_per_line: int = -1  # ignore line if not at least this many letters
    max_alpha_total: int = -1
    max_special_char_total: int = (
        -1
    )  # ignore full paragraph if more than this many special characters in total
    max_decimal_total: int = -1
    max_num_spaces_per_line: int = -1  # Cut every line off after this many spaces
    max_num_lines_valid: int = (
        MAX_NUM_LINES_UNLIMITED  # return empty if more than this many lines found
    )

    max_num_lines: int = (
        sys.maxsize
    )  # Cut off after this many non-empty lines (does not count empty lines)

    max_num_empty_lines: int = (
        sys.maxsize
    )  # Cut off after this many really empty lines (only whitespace)

    do_capitalize_uppercase: bool = (
        False  # make all words with first letter uppercase and rest lowercase
    )

    # Make '   ' -> ' ' in the string. Keep all Newlines, non breaking spaces, etc.
    do_compress_multiple_spaces: bool = False

    def convert(self, s: str):
        if not s:
            return

        s2 = ""
        if self.do_replace_spacer_char:
            if s:
                for line in s.split("\n"):
                    if s2:
                        s2 += "\n"
                    # Replace by ' ' and not by '' so we can apply max_num_spaces_per_line later
                    s2 += line.replace(SPACER_CHAR, " ").strip()
        else:
            s2 = s.strip()

        if self.do_replace_underscore_char:
            s2 = s2.replace("_", "")

        if self.min_num_lines >= 0:
            num_lines = s2.count("\n") + 1
            if num_lines < self.min_num_lines:
                return
        if self.max_num_lines_valid < MAX_NUM_LINES_UNLIMITED:
            num_lines = s2.count("\n") + 1
            if num_lines > self.max_num_lines_valid:
                return

        s3 = ""
        num_lines = 0
        num_empty_lines = 0
        for line in s2.split("\n"):
            empty_line = len(line.strip()) == 0
            if empty_line:
                num_empty_lines += 1
                if num_empty_lines > self.max_num_empty_lines:
                    # Too many empty lines, end the paragraph here
                    break
            else:
                num_empty_lines = 0

            success = True
            if success:
                if self.min_char_per_line >= 0 and len(line) < self.min_char_per_line:
                    success = False

            if success:
                if (
                    self.min_alpha_per_line >= 0
                    and count_alpha(line) < self.min_alpha_per_line
                ):
                    success = False

            if success:
                cd = count_digit(line)
                if self.min_decimal_per_line >= 0 and cd < self.min_decimal_per_line:
                    success = False
                if 0 <= self.max_decimal_per_line < cd:
                    success = False

            if success:
                s3 += "\n" + line.strip()
                num_lines += 1

            s3 = s3.strip()
            if num_lines >= self.max_num_lines:
                break

        if self.max_alpha_total >= 0:
            num_alpha = 0
            for c in s3:
                if c.isalpha():
                    num_alpha += 1
            if num_alpha > self.max_alpha_total:
                # Too many alpha chars in paragraph
                return None

        if self.max_decimal_total >= 0:
            num_alpha = 0
            num_decimal = 0
            for c in s3:
                if c.isalpha():
                    num_alpha += 1
                elif c.isdecimal():
                    num_decimal += 1
            if num_decimal > self.max_decimal_total:
                # Too many numbers in paragraph
                return None

        if self.max_special_char_total >= 0:
            num_special = 0
            for c in s3:
                if not c.isalnum() and c != " ":
                    num_special += 1
            if num_special > self.max_special_char_total:
                # too many special chars -> could be bad scan or wrong field
                return None

        s4 = ""
        if self.max_num_spaces_per_line >= 1:
            for line in s3.split("\n"):
                pos_last_wanted_space = find_nth(
                    line, " ", self.max_num_spaces_per_line + 1
                )
                if pos_last_wanted_space == -1:
                    s4 += "\n" + line
                else:
                    s4 += "\n" + line[:pos_last_wanted_space]
                s4 = s4.strip()
        else:
            s4 = s3

        if self.do_capitalize_uppercase:
            s5 = capitalize_uppercase(s4)
        else:
            s5 = s4

        if self.do_compress_multiple_spaces:
            s6 = re.sub(" +", " ", s5).strip()
        else:
            s6 = s5

        return s6


@dataclass
class SingleLineParagraphConverter(ParagraphConverter):
    separator: str = ", "

    def convert(self, s: str):
        s2 = super().convert(s)
        if s2:
            if "\n" in s2:
                lines = s2.split("\n")
                stripped_lines = []
                for line in lines:
                    stripped_lines.append(line.strip())
                s2 = self.separator.join(stripped_lines)
        return s2


@dataclass
class AddressConverter(ParagraphConverter):
    def convert(self, s: str):
        self.do_replace_spacer_char = True
        self.min_num_lines = 3
        self.min_char_per_line = 6
        self.min_alpha_per_line = 3  # For short city names e.g. 'Rue'
        self.max_decimal_total = -1
        self.max_num_spaces_per_line = 4  # For long names. But if something is right of the paragraph with lots of space then cut it of

        # Hack to remove leading H
        if s.startswith("H  "):
            s = s[2:]

        s = remove_leading_title_from_name(s)

        return super().convert(s)


class AddressSingleLineConverter(ParagraphConverter):
    def convert(self, s: str):
        self.max_num_lines_valid = 1
        self.min_alpha_per_line = 5
        self.max_special_char_total = 7
        self.max_num_spaces_per_line = 15

        s = remove_leading_title_from_name(s)

        return super().convert(s)


class NameFromAddressConverter(AddressConverter):
    def convert(self, s: str):
        adr = super().convert(s)

        name = adr
        if adr:
            num_lines = adr.count("\n")
            if num_lines >= 1:
                name = substring_before_newline(adr).title()

        return name


@dataclass
class CleanEmailConverter(ParagraphConverter):
    max_num_lines = 1


@dataclass
class CleanNameConverter(ParagraphConverter):
    # first_line_only: bool = False              CHANGED: use max_num_lines=1 instead
    max_num_spaces_per_line: int = (
        6  # Cut off name after at most this number of spaces on the line
    )
    max_decimal_per_line: int = 0
    max_num_lines_valid: int = (
        6  # Make result empty to be conservative if too many lines found
    )
    min_alpha_per_line: int = 3

    # If there are two names separated by ' und ' then keep only the first
    skip_second_name: bool = True

    def convert(self, s: str):
        if s:
            s2 = (
                s.replace(",", " ")
                .replace(".", " ")
                .replace(":", " ")
                .replace("_", " ")
                .replace("*", " ")
                .replace("\t", "                ")
            )
            s2 = s2.strip()
            s2 = remove_leading_title_from_name(s2).strip()

            if s2:
                if len(s2) > 10:
                    name_separators = [" / ", " und ", " & ", "u/o", "uo", "&/OR", "OR"]
                    for sep in name_separators:
                        if self.skip_second_name and sep in s2:
                            first_part = s2.split(sep)[0].strip()
                            if " " in first_part:
                                # use this rule only if there are 2 words before the ' und '
                                # because it could be 'Max und Maria Mustermann'
                                s2 = first_part
                                break

            s3 = super().convert(s2)
            first_line_only = self.max_num_lines == 1
            max_num_spaces: int = 4
            ret = clean_name(s3, first_line_only, max_num_spaces)
            return ret


class CleanStreetConverter(ValueConverter):
    def convert(self, s_orig: str):
        s = ParagraphConverter(max_num_lines_valid=1).convert(s_orig)

        if s:
            alpha, digits, space, special = calc_string_stats(s)
            alpha_ok = 3 <= alpha <= 30
            digits_ok = digits <= 5
            space_ok = space <= 5
            special_ok = special <= 3
            if alpha_ok and digits_ok and space_ok and special_ok:
                return s.strip()


class CleanCityConverter(ValueConverter):
    def convert(self, s: str):
        if s:
            alpha, digits, space, special = calc_string_stats(s)
            alpha_ok = 3 <= alpha <= 30
            digits_ok = digits <= 3
            space_ok = space <= 3
            special_ok = special <= 2
            if alpha_ok and digits_ok and space_ok and special_ok:
                return s.strip()


@dataclass
class CleanPhonenumberConverter(ParagraphConverter):
    max_num_lines_valid: int = 1
    min_decimal_per_line: int = 10

    def convert(self, s: str):
        s2 = super().convert(s)
        ret = None
        if s2:
            ret = (
                s2.replace("_", "")
                .replace("|", "")
                .replace("I", "")
                .replace(".", "")
                .replace(",", "")
                .strip()
            )

        if not ret:
            ret = None
        return ret


@dataclass
class CleanBasicLineConverter(ParagraphConverter):
    max_num_spaces_per_line: int = 5
    max_num_lines_valid: int = (
        1  # Make result empty to be conservative if too many lines found
    )
    min_alpha_per_line = 3

    def convert(self, s: str):
        if s:
            s2 = s.replace("_", " ").replace("*", " ").replace("\t", "                ")
            s2 = s2.strip()
            s3 = super().convert(s2)
            return s3


class CleanEmployerConverter(CleanBasicLineConverter):
    pass


@dataclass
class CleanMaritalEmployerConverter(CleanBasicLineConverter):
    pass


@dataclass
class CleanProfessionConverter(CleanBasicLineConverter):
    # Currently same functionality as for Employer
    pass


@dataclass
class CleanAddressLineConverter(ValueConverter):
    first_line_only: bool = False

    def convert(self, s: str):
        return clean_address_line(s, self.first_line_only)


class CantonShortcodeConverter(ValueConverter):
    def convert(self, s: str):
        shortcode = get_canton_shortcode(s)
        if shortcode:
            return shortcode
        else:
            return s


class FilenameDateConverter(ValueConverter):
    def convert(self, s: str):
        if s:
            try:
                d = datetime.strptime(s, "%d.%m.%Y")
                if d:
                    return d.strftime("%Y-%m-%d")
            except ValueError:
                return None


class CompanyNameFromAddressBlockConverter(ValueConverter):
    def convert(self, s: str):
        if s:
            s2 = substring_before_newline(s)
            if s2:
                s2 = s2.strip()
            return s2


class PhoneConverter(ValueConverter):
    def convert(self, s_orig: str):
        s = ParagraphConverter(max_num_lines_valid=1).convert(s_orig)
        return s


class EmailConverter(ValueConverter):
    def convert(self, s_orig: str):
        s = ParagraphConverter(max_num_lines_valid=1).convert(s_orig)

        r = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,7}\b"

        if s:
            ret = re.search(r, s)
            if ret:
                return ret.group(0)

        return None
