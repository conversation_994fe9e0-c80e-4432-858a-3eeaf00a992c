import os
import subprocess
from pathlib import Path

from constants import COMMAND_LINE_CACHE_PATH, ABBY_CMD

import structlog

logger = structlog.getLogger(__name__)


class FinereaderEngineException(Exception):
    pass


def get_commandline_cache_path(
    input_file: Path,
    only_if_exists=True,
    use_xml=False,
    command_line_cache_path=COMMAND_LINE_CACHE_PATH,
):
    target_dir = command_line_cache_path
    if not target_dir:
        target_dir = input_file.parent
        target_dir = Path(os.path.join(str(target_dir), "cmd"))

    filename = input_file.name
    if use_xml:
        filename = filename[:-4] + ".xml"
    output_file = target_dir.joinpath(filename)
    if not only_if_exists or output_file.exists():
        return output_file


def transform_with_commandline_cache(
    input_file: Path, skip_if_exists=True, use_xml=False
):
    output_file = get_commandline_cache_path(
        input_file, only_if_exists=False, use_xml=use_xml
    )
    transform_with_commandline(input_file, output_file, skip_if_exists=skip_if_exists)
    return output_file


def transform_with_commandline(
    input_file: Path, output_file: Path, skip_if_exists: bool = True
):
    if skip_if_exists and output_file.exists():
        return

    assert Path(ABBY_CMD).exists()

    filename, extension = os.path.splitext(output_file)
    extension_param = str(extension[1:]).upper()

    # Create target dir if necessary
    output_file.parent.mkdir(parents=True, exist_ok=True)

    cmd = [
        ABBY_CMD,
        "-if",
        f"{str(input_file)}",
        "-f",
        extension_param,
        "-of",
        f"{str(output_file)}",
    ]

    # These have intentionally been disabled because some areas would not be detected at all
    # "-apma",
    # "-adp",  # do not detect pictures

    params = [
        "-pi",  # progress indicator
        "-adp",  # So docs are better with this, some are better without
        "-adtop",  # detect text on pictures
        "-aeate",  # Force text extraction
        "-xaca",  # Coords for every char inside "formatting"
        "-pfs",
        "MaxQuality",
        "-rl",
        "English",
        "German",
        "French",
        "Italian",
    ]
    cmd = cmd + params
    command = " ".join(cmd)
    logger.info(f"Run FineReader Engine cmd: {command}")
    p = subprocess.run(cmd)
    if p.returncode:
        raise FinereaderEngineException(
            f"Error occured when running the command line tool for {command}"
        )
