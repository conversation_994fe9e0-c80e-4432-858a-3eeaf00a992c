# text is the text that will be searched
from math import floor


def calculate_max_l_dist_absolute(max_l_dist: float, token: str):
    if 0 < max_l_dist < 1:
        # This is a relative measure and means "this percentage of characters can be wrong"
        max_abs = floor(len(token) * max_l_dist)
        return max_abs
    else:
        # This is an absolute measure and means "this number of characters can be wrong" or more precise
        # "this number of levenshtein operations is acceptable
        return max_l_dist
