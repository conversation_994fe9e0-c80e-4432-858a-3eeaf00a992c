import typer
from pathlib import Path
from rich.console import Console
import yaml
import time
import pandas as pd
import copy
import numpy as np

np.set_printoptions(precision=3, suppress=True)

from cut_sausage import cut_sausage

"""
run: 
python3 -m cut_sausage_on_folder -input /home/<USER>/data/Vivacious-Viper_sample_single_page_pdf -output output/
python3 -m cut_sausage_on_folder /home/<USER>/ml/data/2024_11_21_HD_VZ_example_single_page --output-path output/ --ckpts-path output/TABME_vision/v1.2_VZ2
python3 -m cut_sausage_on_folder /home/<USER>/ml/data/2024_12_12_VZ_RE_single_page_pdf --output-path output/ --ckpts-path output/TABME_vision/v1.2_VZ2

input folder should follow the following layout
data/
├── folder1/
│   ├── folder1_CU
|   │   ├── 0.pdf       (1 page pdf)
|   │   ├── 1.pdf       (1 page pdf)
|   │   └── 2.pdf       (1 page pdf)
│   ├── folder1_DI
|   │   ├── 0.pdf
|   │   ├── 1.pdf
|   │   ├── 2.pdf
|   │   ├── 3.pdf
|   │   └── 4.pdf
│   ├── folder1_KA
|   │   ├── etc.

"""


app = typer.Typer()


def cut_saussage_on_folder(config):
    # declare paths
    input_path = Path(config["input_path"])
    output_path = Path(config["output_path"])
    ckpts_path = Path(config["ckpts_path"])
    console.print(f"Weights: {ckpts_path}")
    console.print(f"Cut threshold: {config['cut_threshold']}")

    # create output dir
    output_path.mkdir(parents=True, exist_ok=True)

    # loop over folder hierarchy
    for folder in input_path.iterdir():

        console.print("folder:", folder)
        for subfolder in folder.iterdir():
            console.print("subfolder:", subfolder)

            # copy config for each sausage to update the path to the pdf directory
            sausage_config = copy.copy(config)
            sausage_config["path_model_folder"] = ckpts_path
            sausage_config["data_path"] = (
                ""  # strange the pdf list has the full path so it seems unecessary...
            )

            # edit config
            sausage_config["input_folder"] = subfolder.as_posix()
            save_folder = (
                output_path
                / "predictions"
                / folder.stem
                / f"{subfolder.stem}_{config['cut_threshold']}"
            )
            save_folder.mkdir(parents=True, exist_ok=True)
            sausage_config["output_path"] = save_folder

            # predict cuts
            predictions = cut_sausage(sausage_config)
            prediction = np.array(predictions)
            console.print("predictions")
            console.print(predictions)

            # save predictions
            save_path = save_folder / f"prediction.csv"
            pd.DataFrame(predictions).to_csv(save_path)

            console.print(f"Outputs are saved in {save_path}")


@app.command()
def run(
    input_path: str,
    output_path: str = "output",
    ckpts_path: str = "output/TABME_vision/dainty-sound-79",
    sort_sausage_per_cut: bool = True,
    cut_threshold: float = 0.5,
):
    config = yaml.load(open(Path(ckpts_path) / "config.yaml", "r"), yaml.CLoader)
    config["input_path"] = input_path
    config["output_path"] = output_path
    config["ckpts_path"] = ckpts_path
    config["sort_sausage_per_cut"] = sort_sausage_per_cut
    config["cut_threshold"] = cut_threshold

    cut_saussage_on_folder(config)


if __name__ == "__main__":
    console = Console(record=True, quiet=False)
    start_time = time.time()
    console.log("start_time", start_time)
    try:
        app()
    except SystemExit as e:
        # if e.code == 0:
        #     pass  # Suppress the traceback for successful exits
        # else:
        #     raise  # Re-raise the exception for non-zero exit codes to show the traceback
        console.print(e)
    except RuntimeError as e:
        console.print(e)
    except:
        console.print_exception(show_locals=True)

    # destroy NCCL process (remove a warning)
    import torch.distributed as dist

    if dist.is_available() and dist.is_initialized():
        dist.destroy_process_group()

    # Format elapsed time into hours, minutes, and seconds
    end_time = time.time()
    elapsed_time = end_time - start_time
    hours, rem = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(rem, 60)
    # Print formatted execution time
    console.log(f"Execution time: {int(hours)}h {int(minutes)}m {seconds:.2f}s")

    print("Saved logs to console.log")
    console.save_text("console.log")
