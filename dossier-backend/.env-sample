SECRET_KEY="some very long secret"
RABBIT_URL=amqp://admin:<EMAIL>:5672/

DJANGO_ALLOWED_HOSTS="localhost localhost:8000 dms.hypo.duckdns.org"
DJANGO_CORS_ORIGIN_WHITELIST="http://localhost:8000 https://service.hypo.duckdns.org https://service.hypo-staging-du.duckdns.org http://localhost:3000 http://localhost:1337"

POSTGRES_HOST=pgcluster1.hypo.duckdns.org
POSTGRES_DB=dms
POSTGRES_USER=dms
POSTGRES_PASSWORD=dms

DEBUG=1

# Enable this for Silk Django SQL profiling
DEBUG_ENABLE_SILK=False

S3_ENDPOINT=minio.hypo.duckdns.org
S3_ACCESS_KEY=S3_ACCESS_KEY
S3_SECRET_KEY=S3_SECRET_KEY
S3_SECURE=true
S3_REGION=ch-dk-2
S3_URL_EXPIRATION_SECONDS=86400

ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY=DossierZipper.DossierZipRequestV1

SENTRY_ENVIRONMENT=dev-${USERNAME}

ADMIN_S3_ENDPOINT_URL=https://minio.hypo.duckdns.org
BASE_DOMAIN=hypo.duckdns.org
KEYCLOAK_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkCmvvqDTcjFI+Xphm/cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv+ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27+CC1p/lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2+VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj+XqcZXvKAo/v1U2A9c5CztadZSa7TL8TZa/TOByxbYCaElxWKs+TWVmnj+TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITwIDAQAB
# Used for API used to Manage/create Accounts and JWKs
MANAGEMENT_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkCmvvqDTcjFI+Xphm/cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv+ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27+CC1p/lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2+VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj+XqcZXvKAo/v1U2A9c5CztadZSa7TL8TZa/TOByxbYCaElxWKs+TWVmnj+TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITwIDAQAB

DOSSIER_EVENTS_WORKER_QUEUE_NAME=DossierEvents.dev.DossierEventV1
IMAGE_EXPORTER_WORKER_QUEUE_NAME=DossierImageExporter.dev.ImageExportRequest

DOSSIER_EVENT_CONSUMER_PREFETCH=6
DOSSIER_EVENT_CONSUMER_THREAD_POOL_FILE_UPLOAD=11
DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE=80
DOSSIER_EVENT_CONSUMER_FINALIZE_PROCESS_BACKOFF_MAX_TRIES=6

PRETTY_PRINT_LOG=True
# Optional, useful for when path is not set in shell, e.g. for macs
# POSTGRES_PATH=/opt/homebrew/bin/



# ZKB configuration for authorization service
ZKB_SOCKS5_PROXY=socks5://localhost:1080
# zkbut
# ZKB_AUTHORIZATION_ENDPOINT=https://sicon-01.api.zkb.ch/auth/v1
# zkbit
ZKB_AUTHORIZATION_ENDPOINT=https://sicon-01.api.zkb.ch/auth/v1
# zkbat
# ZKB_AUTHORIZATION_ENDPOINT=https://sicon-st1.api.zkb.ch/auth/v1
# zkb production
# ZKB_AUTHORIZATION_ENDPOINT=https://sicon.api.zkb.ch/auth/v1
#ZKB_AUTHORIZATION_CERT_PEM_FILE=/home/<USER>/zkb/certs/c3.crt
#ZKB_AUTHORIZATION_CERT_KEY_FILE=/home/<USER>/zkb/certs/c3.key

# If this is True then the document category ZKB_VBV will be handled in a special way
# Semantic document is set to read-only directly after processing
# Download triggers download of extracted file, not of the processed file
# This only applies if the extracted file is a PDF
ZKB_VBV_USE_EXTRACTED_FILE=True

# For local usage via core services uncomment this.
# generally speaking we want to use the exoscale bucket for devs who don't run dp locally
# TEMPFILESTORE_S3_ENDPOINT=minio.hypo.duckdns.org
# TEMPFILESTORE_S3_ACCESS_KEY=S3_ACCESS_KEY
# TEMPFILESTORE_S3_SECRET_KEY=S3_SECRET_KEY

TEMPFILESTORE_S3_ENDPOINT = sos-ch-dk-2.exo.io
TEMPFILESTORE_S3_SECURE = True
TEMPFILESTORE_S3_REGION = ch-dk-2
TEMPFILESTORE_S3_BUCKET = dev-tempfilestore
TEMPFILESTORE_S3_ACCESS_KEY=EXO2705ead2ef01134267c1ce59
TEMPFILESTORE_S3_SECRET_KEY=jVkq3PqbzBpl7elGCGvSdg_lCOeCVdnd5jnA7oCTBjk

# upload file to the central temporary file store at exoscale, so the file is available via url in the processing zone
# if false, it might be not available in the processing zone and the file can not be processed. in production, this should be false
UPLOAD_TO_FILESTORE_FOR_PROCESSING=False

# If this is True and processing delivers a processed_page, semantic_document and/or semantic_page with a
# document_category which is not configured then the document category is added automatically.
# If this is False then the document_category will be mapped to DocumentCategory.UNKNOWN
ADD_NEW_DOCUMENT_CATEGORIES_FROM_PROCESSING=False

ENABLE_ADMIN_KEYCLOAK_LOGIN=False
KEYCLOAK_REALM_ENDPOINT=https://auth.hypo.duckdns.org/realms/dev-hypodossier/
ADMIN_KEYCLOAK_CLIENT_ID=django-admin
ADMIN_KEYCLOAK_SECRET_KEY=wqtvWuYecdJ6vfnM4g6PGM668k3D2ZeY
ADMIN_KEYCLOAK_ACCOUNT_DEFAULT_HTTP_PROTOCOL=https

# Namespace, e.g. BEKB, SWISSFEX, etc
# Inserted into RabbitMQ routing keys, as rabbitmq is currently shared between instances
DEPLOYMENT_NAMESPACE=dev

# If this is True, documents of certain document categories for BEKB will be grouped (= combined into one
# semantic document) directly before the BEKB archive export happens. Structure of semantic documents is
# permanently changed. Documents with different collaterals will NOT be merged into one document.
ENABLE_BEKB_ARCHIVE_GROUPING_BY_DOCUMENT_CATEGORY=True

# Whether to enable to output of pageobjects in data v2
# we are slowly depreciating them
# https://gitlab.com/hypodossier/document-universe/-/issues/512
ENABLE_PAGE_OBJECTS_IN_DATA_V2=True


# used for signing jwt tokens by the dms backend
# create a key via  python manage.py keys jwk create
INSTANCE_SIGNING_KEY={"alg":"RS256","d":"CSgmlxZnJYsjBrmwKhMA1qsel1yMruvEaj3Lh25YsVBwbx4hLk7w9G9Wed1P7frpvch9sxhPWzkHuKNB8Vy5y_tnwHBNYy_mUwaJXZaJF3wyNFXkS4TEXeyZNy2v4dwFDgCW-M5Wmn8_4DDnTLg5eF_JHKyUFovcbZjvna3qHKmjGUwOpe81p98LQkigsSu-bQ1AVeflu1WwhaDdOtZraPttdoq9l8yn3cdEBkpVDy4e4uxqPf2E9grDNbSjJM2gOfUpC0u5oDgFW937OgVNmEHwDHTnjlTanDGRkolL8SURt0hYX31LmL8jjBVG8veZ5ts06QDZ80kK4Q8roGPe4Q","dp":"wMjIxVJKSIIKcTsDxjyq0wUq3HHoiL-wpIfejUDorqYaH3l-hOfTnLqSqtOKRyiPL_SLViiV7BKrS-MeU3CgQmV-lSTNZBHU_PTaReUmxdAL9oNFuw5GUgsyqjyF4DpASZuevGl2MT20xzP7dGbsLm5QEMZHNty6EWj4qfylCRE","dq":"B6vsvzl0cHntT8r-0WWMbD_wm2X1jcTUk5isxF3o9BoOEC22v_VjHB9Mh3t2-PmN0lh9QoE_yX1QfGG_OueuDs6W1JYq6cIDgSJSLNiO-XRD2CMDmJM6FkmQ3IAIxiVHp41m3CSF41pwJnOQ3VpmJE-SJfNeCMaMz_4luymxVQE","e":"AQAB","kid":"7d56996d-39da-4e4b-af5a-b4b60b2e2ec0","kty":"RSA","n":"0bzUP1haAOAWWwL3DdDisO0IblSwTPxW4KL_DvP0bYZXVEPvfKq8JbGSA1YTTqdU0GZPGbOWSuCc5zExsJCCsQmG-dqKaUntP8Ie7GtWRk43BxlmMM_r12B8KpRkyy5FefwbDkasnsxuRKZIH_z5ltlqCtfUFKnQfKqNMjfyGBjfesq_S9mEx3j05YZ9wuFDzeiHkFf0wltA1leFuhSgElW3aO0qxrO4HL4MpXBJ-p7UGnkZUhbHilGzr68hUkstLud0_akC0Dwf-U9fnqn-H4t3LHoCWbly5kJ4zFoksqMNz2xfX91tOCbHsA-_K9_-J_fgIZ3RDq0kEVp06RisjQ","p":"7KeHTudGTzJ6RWuu6Vmig3ZGZkrdlYMHDzZkG4pWgE-GgAblMohCdmkky2sIFn4y3mUOzQ6AL3R6q6gMsMLQMqnndjmDytQQGw2YXNSAj5QPwmOay-f-KQPNFURg4izYmlgARMgaD1MZUolh4Q3olljzglxfWdcmfGsTGT9i5u0","q":"4uIDIulRjTZFg0UnUsHT2MOQjxVdxg01pvXgPGGngqhzyA-WX6tURoQ77nCSiNcyW0ypeOV2nMbttlKrgxvWPJvqL7YB1vsQEygfU34hgNYZdioFCRsb5ot0OWcxsiS2n4techDetwbfwZWjr_kCn57IQy2-3Hx8mNU5c3T3iCE","qi":"6ZJIyeJwa8HFcmWKr-B4cPkpjEVN898_XVym-zC537DsUKKwa8LoO7W-8UqM8cnwWjreWYvoUQEV0ybrqfNOV4cjim5VVllX4sxmBioICOH0JBl8ilovcAEkQYhiOn2ar-ZcKERonBWvaRVC0mLSOyDqcRI3G1yMIu5N-g-8VIo","use":"sig"}


# Only used for api demo in bcge/docs/api_demo.py
HYPODOSSIER_BCGE_API_CLIENT_SECRET = "dl7hWj6VWkvdrtNGBayYCIfkMjr4WHyH"

HYPYFREP_PDFA_CONVERT_REQUEST_ROUTING_KEY=Hypyfrep.PDFA.Convert.Request.RoutingKey

CDP_CONFIDENCE_THRESHOLD=0.7

