import inspect

import structlog
from django.apps import AppConfig
from django.dispatch import Signal

from hyrabbit.signals import broker_before_start, broker_started
from projectconfig import container
from projectconfig.asgi_signals import asgi_startup, asgi_shutdown

logger = structlog.get_logger(__name__)


class HyrabbitConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "hyrabbit"

    def ready(self):
        super().ready()

        broker = container.broker()

        async def start(**kwargs):
            await dispatch_signal(
                broker_before_start, sender=broker.__class__, broker=broker
            )
            await broker.start()
            await dispatch_signal(
                broker_started, sender=broker.__class__, broker=broker
            )
            await logger.ainfo("hyrabbit broker started")

        async def stop(**kwargs):
            await broker.stop()

        asgi_startup.connect(start, weak=False)
        asgi_shutdown.connect(stop, weak=False)

        container.wire(packages=["hyrabbit"])


async def dispatch_signal(signal: Signal, sender, **named):
    response = signal.send(sender=sender, **named)
    for _, response in response:
        if not response:
            continue

        if inspect.isawaitable(response):
            await response
        else:
            response()
