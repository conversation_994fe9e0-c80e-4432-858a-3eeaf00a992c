import json
from typing import List, <PERSON>ct, <PERSON><PERSON>, Optional

import structlog
from django.conf import settings
from django.http import HttpRequest
from jwcrypto.jwk import JW<PERSON>
from jwcrypto.jwt import JWT, JWTExpired
from ninja import NinjaAP<PERSON>, Router
from ninja.pagination import paginate
from ninja.errors import HttpError

from cdp.auth_bearer import Auth<PERSON>earer
from cdp.schemas import (
    RecommendationRequest,
    RecommendationResponse,
    FieldDefinitionResponse,
    NumRecommendationsResponse,
    NumRecommendationRequest,
    AnalyticsRequest,
    UnifiedRecommendationResponse,
    NumRecommendations,
    FieldValue,
    Confidence,
    RecommendationSource,
    BoundingBox,
    PageObjectTitle,
)
from cdp.utils import parse_dossier_access_token
from cdp.repositories import (
    FieldRepository,
    PageObjectRepository,
    PriorityRepository,
    DocumentRepository,
    FieldContextRepository,
)
from cdp.services_simplified import (
    FieldContextService,
)
from dossier.models import <PERSON>ssie<PERSON>
from cdp.models import Assigned<PERSON>ield
from semantic_document.models import SemanticPagePageObject
from cdp.optimized_adapter import OptimizedRecommendationAdapter

logger = structlog.get_logger()


field_repo = FieldRepository()
field_context_repo = FieldContextRepository()

field_context_service = FieldContextService(field_repo, field_context_repo)


# Create an optimized adapter with the desired approach and services
# You can change this to "bottom_up" for benchmarking different approaches
optimized_adapter = OptimizedRecommendationAdapter(
    field_context_service=field_context_service, approach="top_down"
)

api = NinjaAPI(
    title="CDP API",
    version="1.0.0",
    description="API for CDP",
    auth=AuthBearer(),
    urls_namespace="cdp-api",
)

# Import the A/B testing router
from cdp.ab_testing.api import ab_testing_router

# Add the A/B testing router
api.add_router("/ab_testing/", ab_testing_router)


def get_assigned_field_or_error(
    field_definition_key: str, field_set_key: str
) -> AssignedField:
    """Get the assigned field or raise an appropriate HTTP error."""
    assigned_field = field_repo.get_assigned_field(field_definition_key, field_set_key)
    if not assigned_field:
        raise HttpError(
            404,
            f"Field {field_definition_key} not found in field set {field_set_key}",
        )
    return assigned_field


def get_dossier_or_error(dossier_uuid) -> Dossier:
    """Get the dossier or raise an appropriate HTTP error."""
    dossier = Dossier.objects.filter(uuid=dossier_uuid).first()
    if not dossier:
        raise HttpError(404, f"Dossier {dossier_uuid} not found")
    return dossier


def process_recommendations(
    dossier_uuid,
    field_set_key: str,
    field_definition_key: str,
    assigned_field: AssignedField,
    field_context: Optional[List[str]] = None,
) -> List[UnifiedRecommendationResponse]:
    """Process recommendations for a field."""
    # Get the dossier
    dossier = get_dossier_or_error(dossier_uuid)

    # Use the optimized adapter to get recommendations (including hints)
    # The adapter handles sorting, applying the confidence threshold, and field context filtering
    recommendations = optimized_adapter.process_recommendations(
        dossier_uuid=dossier_uuid,
        field_set_key=field_set_key,
        field_definition_key=field_definition_key,
        assigned_field=assigned_field,
        field_context=field_context,
    )

    return recommendations


@api.post(
    "/recommendations/",
    response={200: List[RecommendationResponse]},
    operation_id="recommendation-post",
    url_name="recommendation-post",
)
@paginate
def recommendations_post(
    request: HttpRequest,
    recommendation_request: RecommendationRequest,
):
    """Get recommendations for a field."""
    dossier_uuid, field_set_key = parse_dossier_access_token(request)
    field_definition_key = recommendation_request.field_definition
    field_context = recommendation_request.field_context

    try:
        assigned_field = get_assigned_field_or_error(
            field_definition_key, field_set_key
        )
        recommendations = process_recommendations(
            dossier_uuid,
            field_set_key,
            field_definition_key,
            assigned_field,
            field_context,
        )

        # Convert to legacy format
        legacy_recommendations = []
        for rec in recommendations:
            for doc_detail in rec.source.source_document_details:
                for page in doc_detail.semantic_pages:
                    # Get the page object details for this page
                    for page_obj in page.page_objects:
                        # Create a RecommendationResponse object with the required fields
                        legacy_recommendations.append(
                            RecommendationResponse(
                                confidence=Confidence(
                                    confidence_value=page_obj.confidence.confidence_value,
                                    confidence_level=page_obj.confidence.confidence_level,
                                    confidence_formatted=page_obj.confidence.confidence_formatted,
                                ),
                                source=RecommendationSource(
                                    image_url=str(page.image_url),
                                    bbox=page_obj.bbox,
                                    page_object_uuid=page_obj.page_object_uuid,
                                    semantic_page_uuid=page.semantic_page_uuid,
                                    processed_page_uuid=page.processed_page_uuid,
                                    semantic_document_uuid=doc_detail.semantic_document_uuid,
                                    dossier_uuid=doc_detail.dossier_uuid,
                                    semantic_document_title=doc_detail.semantic_document_title,
                                    semantic_document_suffix=doc_detail.semantic_document_suffix,
                                    document_category=doc_detail.document_category,
                                    document_category_translated=doc_detail.document_category_translated,
                                    page_object_title=page_obj.page_object_title,
                                    document_date=doc_detail.document_date,
                                    semantic_page_rotation_angle=page.rotation_angle,
                                    semantic_document_page_number=1,  # Default to 1 for now
                                    semantic_document_page_count=doc_detail.semantic_document_page_count,
                                    page_images=[],  # Empty list for now
                                    page_index_preferred=0,  # Default to 0 for now
                                ),
                                field_value=rec.field_value,
                                priority=rec.source.priority,
                            )
                        )

        return legacy_recommendations

    except HttpError as e:
        raise e
    except Exception as e:
        logger.error(
            "Error generating recommendations",
            exc_info=True,
            dossier=dossier_uuid,
            field=field_definition_key,
            error=str(e),
        )
        raise HttpError(500, f"An unexpected error occurred: {str(e)}")


@api.post(
    "/grouped_recommendations/",
    response={200: List[UnifiedRecommendationResponse]},
    operation_id="grouped-recommendation-post",
    url_name="grouped-recommendation-post",
)
@paginate
def grouped_recommendations_post(
    request: HttpRequest,
    recommendation_request: RecommendationRequest,
):
    dossier_uuid, field_set_key = parse_dossier_access_token(request)
    field_definition_key = recommendation_request.field_definition
    field_context = recommendation_request.field_context

    try:
        assigned_field = get_assigned_field_or_error(
            field_definition_key, field_set_key
        )
        return process_recommendations(
            dossier_uuid,
            field_set_key,
            field_definition_key,
            assigned_field,
            field_context,
        )
    except HttpError as e:
        raise e
    except Exception as e:
        logger.error(
            "Error generating grouped recommendations",
            exc_info=True,
            dossier=dossier_uuid,
            field=field_definition_key,
            error=str(e),
        )
        raise HttpError(500, f"An unexpected error occurred: {str(e)}")


@api.get(
    "/field_definitions/",
    response={200: List[FieldDefinitionResponse]},
    operation_id="field_definitions-get",
    url_name="field_definitions-get",
)
def get_field_definitions(request: HttpRequest):
    """Get the field definitions for the field_set specified in the token."""
    _dossier_uuid, field_set_key = parse_dossier_access_token(request)

    try:
        assigned_fields = field_repo.get_field_definitions_for_fieldset(field_set_key)
        if not assigned_fields:
            logger.info(f"No field definitions found for field set {field_set_key}")
            return []

        response = [
            FieldDefinitionResponse(
                field_definition_key=af.field_definition.key,
                field_definition_flavour=af.field_definition.flavour,
                field_definition_return_type=field_repo.get_return_type_key(af),
            )
            for af in assigned_fields
        ]
        return response

    except Exception as e:
        logger.error(
            f"Error getting field definitions for field set {field_set_key}",
            error=str(e),
            exc_info=True,
        )
        raise HttpError(
            500, f"Error retrieving field definitions for field set: {field_set_key}"
        )


@api.post(
    "/num_recommendations/",
    response={200: NumRecommendationsResponse},
    operation_id="num-recommendations-post",
    url_name="num-recommendations-post",
)
def num_recommendations_post(
    request: HttpRequest,
    num_recommendation_request: NumRecommendationRequest,
):
    """Get the number of recommendations for specified fields."""
    dossier_uuid, field_set_key = parse_dossier_access_token(request)
    field_context = num_recommendation_request.field_context

    if num_recommendation_request.field_definitions:
        field_definition_keys = num_recommendation_request.field_definitions
    else:
        assigned_fields = field_repo.get_field_definitions_for_fieldset(field_set_key)
        field_definition_keys = [af.field_definition.key for af in assigned_fields]

    num_recommendations_response: Dict[str, NumRecommendations] = {}

    for fd_key in field_definition_keys:
        try:
            # Get the assigned field
            assigned_field = get_assigned_field_or_error(fd_key, field_set_key)

            # Get recommendations
            final_recs = process_recommendations(
                dossier_uuid, field_set_key, fd_key, assigned_field, field_context
            )

            # Count total recommendations and hints
            hint_rec_count = len([rec for rec in final_recs if rec.is_hint])
            grouped_sppo_rec_count = len(final_recs) - hint_rec_count
            total_recommendation_count = 0
            for recommendation in final_recs:
                total_recommendation_count += len(
                    recommendation.source.source_document_details
                )
            # Check if we have a single recommendation
            is_single = len(final_recs) == 1
            single_rec = final_recs[0] if is_single else None

            num_recommendations_response[fd_key] = NumRecommendations(
                total_recommendation_count=total_recommendation_count,
                grouped_sppo_recommendation_count=grouped_sppo_rec_count
                + hint_rec_count,
                hint_recommendation_count=hint_rec_count,
                is_single_recommendation=is_single,
                single_recommendation=single_rec,
                field_context=field_context,
            )
        except HttpError as e:
            # Field not found, return empty response
            if e.status_code == 404:
                logger.warning(
                    f"Field {fd_key} not found in field set {field_set_key} for num_recommendations"
                )
                num_recommendations_response[fd_key] = NumRecommendations(
                    total_recommendation_count=0,
                    grouped_sppo_recommendation_count=0,
                    hint_recommendation_count=0,
                    is_single_recommendation=False,
                    single_recommendation=None,
                    field_context=field_context,
                )
            else:
                raise e
        except Exception as e:
            logger.error(
                f"Error getting num recommendations for field {fd_key}",
                exc_info=True,
                dossier=dossier_uuid,
                error=str(e),
            )
            num_recommendations_response[fd_key] = NumRecommendations(
                total_recommendation_count=0,
                grouped_sppo_recommendation_count=0,
                hint_recommendation_count=0,
                is_single_recommendation=False,
                single_recommendation=None,
                field_context=field_context,
            )

    return NumRecommendationsResponse(root=num_recommendations_response)


@api.post(
    "/analytics/",
    response={200: None},
    operation_id="analytics-post",
    url_name="analytics-post",
)
def analytics_post(request: HttpRequest, analytics_request: AnalyticsRequest):
    dossier_uuid = parse_dossier_access_token(request)
    analytics_payload = {"dossier_uuid": dossier_uuid[0], **analytics_request.payload}
    # TODO: move logs prefix to constant/logging class
    logger.info("[CDP] [Analytics] Num recommendations", **analytics_payload)

    return None
