from collections import Counter
from typing import List

from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.db import models
from django.db.models import CASCADE, SET_NULL, QuerySet

from core.behaviors import Timestampable, SoftDeletionModel
from doccheck.models import Case, CompletenessRule
from dossier.models import (
    Dossier,
    DocumentCategory,
    ConfidenceLevel,
    PageCategory,
    Languages,
)
from dossier import models as dossier_models
from processed_file.models import PageObject, ProcessedPage
from statemgmt.models import Status


# Don't merge there is only one doc
class SemanticDocument(SoftDeletionModel, Timestampable):
    dossier = models.ForeignKey(
        Dossier, on_delete=CASCADE, related_name="semantic_documents"
    )
    document_category = models.ForeignKey(
        DocumentCategory, on_delete=CASCADE, blank=True, null=True
    )
    confidence_level = models.CharField(max_length=16, choices=ConfidenceLevel.choices)
    confidence_formatted = models.CharField(max_length=16, blank=True, null=True)
    confidence_value = models.FloatField()
    # document_category_original = models.ForeignKey(DocumentCategory, on_delete=PROTECT,
    #                                                related_name='document_categories_original')

    title_custom = models.CharField(max_length=255, blank=True, null=True)
    title_suffix = models.CharField(max_length=255, blank=True, null=True)

    # Datetime field to keep track of the last time a page was deleted or moved (if any)
    # For deletion we can track the deleted_at field on the page, however for moving we need to track it here
    last_page_change_date = models.DateTimeField(blank=True, null=True)
    # Datetime field to keep track of the last time an entity (e.g. estate) was deleted or moved (if any)
    last_entity_change_date = models.DateTimeField(blank=True, null=True)

    class SemanticDocumentAccessMode(models.TextChoices):
        READ_WRITE = "read_write", "read_write"
        READ_ONLY = "read_only", "read_only"

    access_mode = models.CharField(
        max_length=20,
        choices=SemanticDocumentAccessMode.choices,
        default=SemanticDocumentAccessMode.READ_WRITE,
    )

    # Analogous to external_dossier_id for dossiers
    external_semantic_document_id = models.CharField(
        max_length=255, blank=True, null=True
    )

    # Custom field to be used by clients to store data related to this semantic document
    # Can be set a) when original file is added or b) via client specific api.
    # Can be read by client specific api
    custom_attribute = models.CharField(
        max_length=255, blank=True, null=True, default=None
    )

    semantic_pages: models.manager.Manager["SemanticPage"]

    # semantic_pages: models.QuerySet["SemanticPage"]

    # Used to handle the export process of the semantic document. Currently used by BCGE
    # https://gitlab.com/hypodossier/document-universe/-/issues/537
    # Note: there are two work status fields, one on the semantic document and one on the dossier
    # you can set the value via get_start_work_status_from_dossier_account
    work_status = models.ForeignKey(Status, on_delete=SET_NULL, blank=True, null=True)

    custom_semantic_document_date = models.DateField(blank=True, null=True)

    # I've added unique_together for dossier and external_semantic_document_id. We could have also made it
    # unique by account, however django does not support unique_together across fields of different models.
    # If need be we could add an FK to account, i.e. denormalize the database
    class Meta:
        unique_together = ["dossier", "external_semantic_document_id"]

    @property
    def title(self):
        # Also known as formatted title in the frontend
        return self.calculate_title(
            self.dossier.lang.lower(),
            self.title_custom,
            self.document_category,
            self.title_suffix,  # Suffix is NOT a filename extension
        )

    def title_lang(self, lang: str):
        # Also known as formatted title in the frontend
        return self.calculate_title(
            lang=lang,
            title_custom=self.title_custom,
            document_category=self.document_category,
            # Suffix is NOT a filename extension but a custom addition to document title
            # document title = doc_cat_id + ' ' + doc_cat_title_translated + ' ' + title_suffix
            title_suffix=self.title_suffix,
        )

    @staticmethod
    def calculate_title(
        lang: str,
        title_custom: str,
        document_category: DocumentCategory,
        title_suffix: str,
    ):
        if title_custom:
            return title_custom
        title = f"{document_category.id} {getattr(document_category, lang)}"
        if title_suffix:
            title += f" {title_suffix}"
        return title

    @staticmethod
    def get_document_type_recommendations(
        semantic_pages: QuerySet["SemanticPage"],
        document_category: DocumentCategory,
        exclude_document_category: bool = True,
        nr_of_recommendations: int = 3,
    ) -> List[DocumentCategory]:
        c = Counter(
            [
                page.document_category
                for page in semantic_pages
                if not page.document_category.exclude_for_recommendation
                and (
                    page.document_category != document_category
                    if exclude_document_category
                    else True
                )
            ]
        )
        return [category for category, count in c.most_common(nr_of_recommendations)]

    @property
    def document_type_recommendations(self):
        return self.get_document_type_recommendations(
            self.semantic_pages.select_related("document_category").all(),
            self.document_category,
        )

    @property
    def title_suffix_recommendation(self):
        suffix_recommendation = []

        for page in self.semantic_pages.all():
            suffix_recommendation.append(
                page.processed_page.processed_file.extracted_file.file.name
            )

        most_commonon_original_file_names = Counter(suffix_recommendation).most_common(
            3
        )

        return [value for value, count in most_commonon_original_file_names]

    @property
    def document_category_key(self):
        # There's a flaw in the underlying model, name should actually be key and is treated as such in the code
        return str(self.document_category.name)

    def __str__(self):
        return f"{self.dossier.account.key}/{self.dossier_id}/{self.title}"


class SemanticDocumentPageObject(Timestampable):
    semantic_document = models.ForeignKey(
        SemanticDocument, on_delete=CASCADE, related_name="aggregated_page_objects"
    )
    page_object = models.ForeignKey(PageObject, on_delete=CASCADE)


class SemanticPage(SoftDeletionModel, Timestampable):
    __rotation_angle_step = 90
    __rotation_angle_max_range = 359

    dossier = models.ForeignKey(
        Dossier, on_delete=CASCADE, related_name="semantic_pages"
    )
    page_category = models.ForeignKey(
        PageCategory, on_delete=CASCADE, related_name="semantic_pages"
    )
    semantic_document = models.ForeignKey(
        SemanticDocument, on_delete=CASCADE, related_name="semantic_pages"
    )
    document_category = models.ForeignKey(
        DocumentCategory, on_delete=CASCADE, related_name="semantic_pages"
    )
    processed_page = models.ForeignKey(
        ProcessedPage,
        on_delete=CASCADE,
        null=True,
        blank=True,
        related_name="semantic_pages",
    )

    lang = models.CharField(
        max_length=2, choices=Languages.choices, default=Languages.GERMAN
    )
    number = models.IntegerField(validators=[MinValueValidator(0)])
    confidence_value = models.FloatField()
    confidence_formatted = models.CharField(max_length=16, blank=True, null=True)
    confidence_level = models.CharField(max_length=16, choices=ConfidenceLevel.choices)
    rotation_angle = models.IntegerField(
        default=0,
        validators=[
            MaxValueValidator(__rotation_angle_max_range),
            MinValueValidator(0),
        ],
    )

    def get_new_value_for_rotation_angle_field(self):
        new_value = self.rotation_angle + self.__rotation_angle_step
        return 0 if new_value >= self.__rotation_angle_max_range else new_value

    @property
    def sorting_string(self):
        """Returns a string used to alpha-numerically sort pages

        Business logic:
        We can have a user take multiple pictures of a document, page by page.
        This usually creates a set of pages with increasing filenames, e.g.:
        IMG_9755.JPG, IMG_9756.JPG, IMG_9757.JPG, IMG_9756.JPG

        When we combine pages, we ideally want to keep this in the order of the pages.

        If we have a semantic document, which is created from multiple single pages
        the original filename is captured as part of self.semantic_document.title and as photo number

        If we are combining multiple semantic documents, we would want to order by
        semantic document, then suffix contained in self.semantic_document.title and then photo number

        The purpose of this function is to create a string which can be used to sort pages
        """

        return (
            f"{self.semantic_document.title}{self.semantic_document.uuid}{self.number}"
        )


class SemanticPageProxy(SemanticPage):
    """Used to have semantic pages in dossier/models and semantic_document/models"""

    class Meta:
        proxy = True


class SemanticPagePageObject(Timestampable):
    semantic_page = models.ForeignKey(
        SemanticPage, on_delete=CASCADE, related_name="semantic_page_page_objects"
    )
    page_object = models.ForeignKey(
        PageObject, on_delete=CASCADE, related_name="semantic_page_page_objects"
    )


class FulfillmentType(models.IntegerChoices):
    # This assignment tries to fulfill the requirements by assigning 1..n docs.
    # This is successful if at least 1 doc is assigned
    DOC_ASSIGNMENT = 1
    # This assignment tries to fulfill the requirements by adding a comment
    BYPASSING = 2


class DocCheckAssignment(Timestampable):
    case = models.ForeignKey(Case, on_delete=CASCADE)
    context_model_uuid = models.UUIDField()
    completeness_rule = models.ForeignKey(CompletenessRule, on_delete=CASCADE)

    fulfillment_type = models.IntegerField(
        choices=FulfillmentType.choices,
        default=FulfillmentType.DOC_ASSIGNMENT,
    )

    assigned_documents = models.ManyToManyField(SemanticDocument)
    comment = models.TextField(blank=True, null=True)

    class Meta:
        unique_together = ["case", "context_model_uuid", "completeness_rule"]

    def __str__(self):
        return f"{self.case}/{self.context_model_uuid}/{self.completeness_rule}"


class AssignedRealEstatePropertySemanticDocument(Timestampable):
    """Creates a relationship between a semantic document and a realestate property
    We manually create an intermediate table as we can store additional information
    by inheriting from Timestampable (i.e. updated_at, created_at)

    There are actually two AssignedRealestateProperty intermediate tables, this one and one
    for original files. Hence, I've added the suffix OriginalFile to this one.
    """

    semantic_document = models.ForeignKey(SemanticDocument, on_delete=CASCADE)
    realestate_property = models.ForeignKey(
        dossier_models.RealestateProperty, on_delete=CASCADE
    )


class SemanticPageUserAnnotations(Timestampable):
    semantic_page = models.ForeignKey(
        SemanticPage, on_delete=CASCADE, related_name="user_annotations"
    )
    annotation_group_uuid = models.UUIDField()
    annotation_type = models.CharField(
        choices=[("highlight", "highlight"), ("comment", "comment")]
    )
    bbox_top = models.FloatField()
    bbox_left = models.FloatField()
    bbox_width = models.FloatField()
    bbox_height = models.FloatField()
    text = models.TextField(blank=True, null=True)
