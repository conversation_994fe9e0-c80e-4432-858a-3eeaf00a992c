from django.apps import AppConfig

from projectconfig import container


class SemanticDocumentConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "semantic_document"

    def ready(self):
        super().ready()
        # Implicitly connect signal handlers decorated with @receiver.
        from . import signals  # noqa: F401

        container.wire(packages=[".services"])
