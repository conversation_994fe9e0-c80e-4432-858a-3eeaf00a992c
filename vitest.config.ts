import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import alias from '@rollup/plugin-alias'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@components': path.resolve(__dirname, 'src/components'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@hooks': path.resolve(__dirname, 'src/utils/hooks')
    }
  },
  base: './',
  server: {
    allowedHosts: [
      'service.hypo.duckdns.org'
    ]
  },
  test: {
    include: ['./**/*.test.ts', './**/*.test.tsx'],
    globals: true,
    environment: 'jsdom',
    coverage: {
      reporter: ['text', 'cobertura'], // Specify Cobertura along with any other reporters you want
      reportsDirectory: './coverage',
      provider: 'v8'
    }
  }
});
