{"name": "dossier-manager", "version": "0.1.0", "private": true, "workspaces": ["e2e-tests", "dossier-webcomponents"], "config-overrides-path": "node_modules/react-app-rewired/config-overrides.js", "engines": {"node": ">=22.0.0 <23.0.0"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@babel/core": "^7.24.7", "@babel/plugin-syntax-flow": "^7.24.7", "@babel/plugin-transform-react-jsx": "^7.24.7", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/pro-duotone-svg-icons": "^6.6.0", "@fortawesome/pro-light-svg-icons": "^6.6.0", "@fortawesome/pro-regular-svg-icons": "^6.6.0", "@fortawesome/pro-solid-svg-icons": "^6.6.0", "@fortawesome/pro-thin-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@fortawesome/sharp-light-svg-icons": "^6.6.0", "@fortawesome/sharp-regular-svg-icons": "^6.6.0", "@fortawesome/sharp-solid-svg-icons": "^6.6.0", "@fortawesome/sharp-thin-svg-icons": "^6.6.0", "@r2wc/react-to-web-component": "^2.0.2", "@redux-saga/is": "^1.1.3", "@redux-saga/symbols": "^1.1.3", "@reduxjs/toolkit": "^2.2.3", "@sentry/react": "^8.33.1", "@sentry/tracing": "^7.114.0", "@storybook/test": "^8.1.11", "@tanstack/react-query": "5.29.2", "@tanstack/react-query-devtools": "5.28.10", "@types/i18next-browser-languagedetector": "^3.0.0", "@types/jest": "^29.5.12", "@types/node": "22", "@types/react-beforeunload": "^2.1.0", "@types/react-dom": "^17.0.0", "@types/react-redux": "^7.1.33", "@types/react-router-dom": "^5.1.7", "@types/redux-saga": "^0.10.5", "antd": "^5.20.5", "autoprefixer": "^10.4.19", "axios": "^1.7.5", "dayjs": "^1.11.13", "global": "^4.4.0", "html-to-image": "^1.10.8", "i18next": "^23.11.5", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "immutability-helper": "^3.1.1", "js-file-download": "^0.4.12", "jsdom": "^22.0.0", "jwt-decode": "^4.0.0", "keycloak-js": "^18.0.0", "konva": "^9.3.11", "less": "^4.2.0", "postcss": "^8.4.39", "primereact": "^9.3.1", "rc-menu": "9.8.4", "re-resizable": "^6.11.2", "react": "^18.3.1", "react-animate-height": "^2.0.23", "react-beforeunload": "^2.5.3", "react-cache-buster": "^0.1.8", "react-cookie": "^7.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-scrolling": "^1.3.3", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-dropzone": "12.1.0", "react-hotkeys-hook": "^3.4.7", "react-i18next": "^14.1.2", "react-intersection-observer": "^9.5.3", "react-is": "^18.2.0", "react-json-view-lite": "^1.5.0", "react-konva": "^18.2.10", "react-pdf": "^9.2.1", "react-redux": "^9.1.1", "react-refresh": "^0.16.0", "react-router-dom": "^6.2.2", "react-test-renderer": "^18.3.1", "redux": "^5.0.1", "redux-saga": "^1.3.0", "redux-saga-test-plan": "^4.0.6", "styled-components": "^6.1.8", "stylis": "^4.0.0", "ts-node": "^10.9.1", "vite-plugin-theme": "^0.8.6", "web-vitals": "^3.3.1"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@chromatic-com/storybook": "^1.5.0", "@rollup/plugin-alias": "^5.1.0", "@storybook/addon-essentials": "^8.1.11", "@storybook/addon-interactions": "^8.1.11", "@storybook/addon-links": "^8.1.11", "@storybook/addon-mdx-gfm": "^8.1.11", "@storybook/addon-onboarding": "^8.1.11", "@storybook/blocks": "^8.1.11", "@storybook/react": "^8.1.11", "@storybook/react-vite": "^8.1.11", "@storybook/test-runner": "^0.22.0", "@testing-library/dom": "^9.3.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "15.0.2", "@testing-library/user-event": "^14.4.3", "@types/jsonwebtoken": "^9.0.6", "@types/react-lazy-load-image-component": "^1.5.2", "@types/testing-library__jest-dom": "^6.0.0", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.6.0", "babel-plugin-named-exports-order": "^0.0.2", "chromatic": "^11.3.0", "dotenv": "^16.3.1", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "5.2.5", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-react-refresh": "0.4.19", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-storybook": "^0.8.0", "eslint-plugin-unused-imports": "3.2.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jsonwebtoken": "^9.0.2", "less-loader": "^12.2.0", "minimist": "^1.2.8", "minio": "^7.1.3", "msw": "^2.3.1", "msw-storybook-addon": "^2.0.2", "nanoid": "^5.0.6", "openapi-typescript-codegen": "^0.25.0", "prettier": "3.2.5", "prop-types": "^15.8.1", "storybook": "^8.1.11", "storybook-addon-mock-date": "^0.6.0", "storybook-react-i18next": "^3.1.1", "ts-jest": "^29.2.5", "ts-jest-mock-import-meta": "^1.2.0", "typescript": "^5.4.5", "vite": "^5.1.7", "vitest": "^1.6.0", "webpack": "^5.88.2"}, "overrides": {"react-dev-utils": {"fork-ts-checker-webpack-plugin": "8.0.0"}}, "scripts": {"generate-meta-tag": "node ./node_modules/react-cache-buster/dist/generate-meta-tag.js", "dev": "vite --port 3000", "build": "yarn generate-meta-tag && tsc -p ./tsconfig.build.json && vite build", "lint": "eslint . --ext ts,tsx,js --fix", "preview": "vite preview", "test": "vitest run", "test-watch": "vitest", "test:e2e": "npx playwright test --project=e2e", "test:webcomponents": "npx playwright test --project=webcomponents", "test:all": "npx playwright test", "test:ci": "vitest run --coverage --reporter=junit --outputFile=./junit.xml", "format": "prettier 'src/**/*.{js,ts,tsx}' --write", "schema:dms": "curl http://localhost:8000/api/openapi.json | jq > openapi/dms.json && openapi -c axios --input openapi/dms.json --output ./src/gen/dms --name DmsClient", "schema:cdp": "curl http://localhost:8000/cdp/api/v1/openapi.json | jq > openapi/cdp.json && openapi -c axios --input openapi/cdp.json --output ./src/gen/cdp --name CdpClient", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test-storybook": "test-storybook", "serve-build": "npx serve -s build", "deploy": "node deploy.js", "chromatic": "DISABLE_ESLINT_PLUGIN=true npx --node-options='--max-old-space-size=4096' chromatic --project-token=chpt_1458062d269449b", "chromatic:debug": "DISABLE_ESLINT_PLUGIN=true npx --node-options='--max-old-space-size=4096' chromatic --project-token=chpt_1458062d269449b --debug"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "msw": {"workerDirectory": "public"}, "readme": "ERROR: No README data found!", "_id": "dossier-manager@0.1.0", "packageManager": "yarn@4.9.0"}