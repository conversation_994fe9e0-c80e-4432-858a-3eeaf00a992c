import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { VIEWS } from '../../constants/cfg';

import type { IUseRotatedStyles } from './types';
import { RootState } from '../../store/redux/types';
import { useAppSelector } from '../../store/app/hooks';

const needRotateAngle = [90, 270];

const useRotatedStyles = (
  imageRef: React.RefObject<HTMLAnchorElement | HTMLDivElement>,
  imagePreviewRef: React.RefObject<HTMLImageElement>,
  rotation_angle: number | undefined,
  imageLoad: boolean,
  addScale = true,
  isMakeBigger = true,
  isSortingModalView = false,
  isThumbnail = true
): IUseRotatedStyles => {
  const semantic_documents = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.semantic_documents
  );
  const selectedView = useAppSelector(
    (state: RootState) => state.dossierPageTabView.selectedView
  );
  const pageVisited = useAppSelector(
    (state: RootState) => state.dossierManager.pageVisited
  );

  const [isLandscapeMode, setLandscapeMode] = useState(false);

  const [initPositionIsLandscape, setInitPositionIsLandscape] = useState<
    null | boolean
  >(null);

  const updateInitPosition = (status: boolean) => {
    if (initPositionIsLandscape === null) {
      setInitPositionIsLandscape(status);
    }
  };

  const getLandScapeStatus = () => {
    if (imagePreviewRef.current) {
      const { width, height } = imagePreviewRef.current.getBoundingClientRect();
      if (imageLoad) {
        const status = width < height;

        updateInitPosition(status);

        return initPositionIsLandscape === null ? status : !status;
      }
    }

    return isLandscapeMode;
  };

  const getScale = useCallback(
    (styles: React.CSSProperties) => {
      if (needRotateAngle.includes(rotation_angle as number) && addScale) {
        const statusScale = selectedView === undefined;
        const status = statusScale ? !isLandscapeMode : isLandscapeMode;

        if (status || isSortingModalView) {
          styles['transform'] =
            styles['transform'] + (isThumbnail ? ' scale(0.65)' : '');
        } else if (isMakeBigger) {
          styles['transform'] = styles['transform'] + ' scale(1.30)';
        }
      }
    },
    [
      addScale,
      rotation_angle,
      initPositionIsLandscape,
      imageLoad,
      isLandscapeMode,
      pageVisited
    ]
  );

  const rotatedStyles = useMemo(() => {
    const styles: React.CSSProperties = {};

    if (!isThumbnail) {
      getScale(styles);
      return styles;
    }

    if (selectedView === VIEWS.structureDetails && rotation_angle) {
      styles['transform'] = `rotate(${rotation_angle}deg)`;

      getScale(styles);
    }
    if (rotation_angle && initPositionIsLandscape !== null && imageLoad) {
      styles['transform'] = `rotate(${rotation_angle}deg)`;

      getScale(styles);
    }

    return styles;
  }, [
    isLandscapeMode,
    imagePreviewRef,
    rotation_angle,
    imageLoad,
    getScale,
    imageRef,
    addScale,
    initPositionIsLandscape,
    pageVisited,
    semantic_documents
  ]);

  useEffect(() => {
    if (imageLoad) {
      setLandscapeMode(getLandScapeStatus());
    }
  }, [imagePreviewRef, imageLoad, rotation_angle, pageVisited]);

  useEffect(() => {
    return () => {
      setInitPositionIsLandscape(null);
    };
  }, []);

  // I need to normalize the rotation angle to be between 0 and 360 in that sence when the rotation angle is 270 and we will move to 360 instead of 0
  const normlizedRotationAngle = useMemo(() => {
    if (rotation_angle) {
      return rotation_angle % 360;
    }
    return 0;
  }, [rotation_angle]);

  return {
    rotatedStyles,
    isLandscapeMode,
    initPositionIsLandscape,
    normlizedRotationAngle
  };
};

export { useRotatedStyles, needRotateAngle };
