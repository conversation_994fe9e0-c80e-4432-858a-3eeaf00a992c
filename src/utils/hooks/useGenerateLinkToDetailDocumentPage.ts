import { useCallback } from 'react';

import { ROUTES } from '../../constants';
import { queryParamPageNumber } from '../../constants/routes';
import { findSemanticDocumentIndexByUUID } from '../../components/DossierImages/DossierPageViewImage/utils';

import type { RootState } from '../../store/redux/types';
import { useAppSelector } from '../../store/app/hooks';
import { useTranslation } from 'react-i18next';

export const linkToDetailDocumentPage = (
  validLang: string,
  uuid: string,
  semanticUUID: string,
  pageNumber: number
) => {
  return `${ROUTES(validLang).dynamic.documents(
    uuid,
    semanticUUID
  )}&${queryParamPageNumber}=${pageNumber}`;
};

export const useGenerateLinkToDetailDocumentPage = () => {
  const { i18n } = useTranslation();

  const semantic_documents = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.semantic_documents
  );
  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );

  const generateLinkToDetailDocumentPage = useCallback(
    (semanticDocumentUUID: string, pageNumber: number) => {
      const semanticDocumentIndex = findSemanticDocumentIndexByUUID(
        semantic_documents || [],
        semanticDocumentUUID
      );

      // The fallback as an object with uuid as an empty string
      const defaultSemanticDocument = { uuid: '' };

      // If semantic_documents is empty or no document is found, use the fallback object
      const semanticDocument =
        semantic_documents?.length && semanticDocumentIndex !== -1
          ? semantic_documents[semanticDocumentIndex]
          : defaultSemanticDocument;

      // Ran into an issue where sometimes uuid is undefined
      // so factored out linkToDetailDocumentPage
      return linkToDetailDocumentPage(
        i18n.language,
        uuid || '',
        semanticDocument.uuid,
        pageNumber
      );
    },
    [semantic_documents, uuid, i18n.language]
  );

  return { generateLinkToDetailDocumentPage };
};
