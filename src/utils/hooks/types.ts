import React from 'react';
import { AnonymousArrowFunction } from '../types';

export type locationType = string | null;

export interface IDownloadOriginalFileHook {
  handleDownloadInOnline: AnonymousArrowFunction;
}

export interface IWebSocketMessage {
  [key: string]: string;
}

export interface IUseWebsocket {
  websocket: WebSocket | null;
  lastMessage: IWebSocketMessage | null;
  clearLocation(): void;
  deleteWebSocket(): void;
  createWebsocketConnection(): void;
}

export interface IDimensions {
  width: number;
  height: number;
}

export interface IUseResize {
  dimensions: IDimensions;
}

export interface IUseRotatedStyles {
  rotatedStyles: React.CSSProperties;
  isLandscapeMode: boolean;
  normlizedRotationAngle: number;
  initPositionIsLandscape: boolean | null;
}

export interface IUseRedirectToDossierManager {
  onClickRedirectToDossierManager: AnonymousArrowFunction;
}

export interface IUseShowDeletedStatus {
  statusShowDeletedDocuments: boolean;
}

export interface IPreventEventByStatusShowDeletedStatus {
  preventEventByStatusShowDeletedStatus: (
    e: React.MouseEvent<HTMLElement>
  ) => void;
}

export type IAuthenticatedUser = {
  name: string;
  email: string;
  username: string;
};
