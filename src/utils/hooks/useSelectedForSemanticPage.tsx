import React, { useEffect, useMemo, useState } from 'react';

import { EventForShift, useShiftSelected } from './useShiftSelected';

import type { RootState } from '../../store/redux/types';
import type {
  ISelectedDocument,
  ISemanticDocumentWithInitialDocuments
} from '../../components/DossierImages/DossierPageViewImage/types';
import {
  findSemanticDocumentIndexByUUID,
  findSemanticPageIndexByUUID
} from '../../components/DossierImages/DossierPageViewImage/utils';
import {
  setCheckedDocument,
  setInitDocs
} from '../../components/DossierImages/DossierPageViewImage/actions';
import { useAppDispatch, useAppSelector } from '../../store/app/hooks';
import { arraysEqual } from './utils';

export interface IUseSelectedForSemanticPage {
  onChange: (
    event: EventForShift,
    stringItem: string,
    initDocs: ISemanticDocumentWithInitialDocuments[]
  ) => void;
  selectMultipleDocuments: (
    selectedDocuments: string[],
    isLeftRightSelection: boolean
  ) => void;
  selectedDocumentToString: (selectedDocument: ISelectedDocument) => string;
  setSelectedLocalDocuments: React.Dispatch<
    React.SetStateAction<ISelectedDocument[]>
  >;
}

export interface ISelectedDocumentToInitState {
  semanticDocumentUUID: string;
  semanticPageUUID: string;
  key: string;
}

const useSelectedForSemanticPage = (): IUseSelectedForSemanticPage => {
  const semantic_documents = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.semantic_documents
  );
  const selectedDocuments = useAppSelector(
    (state: RootState) => state.pageView.selectedDocuments
  );
  const initDocuments = useAppSelector(
    (state: RootState) => state.pageView.initDocuments
  );
  const isEndDrag = useAppSelector(
    (state: RootState) => state.pageView.isEndDrag
  );

  const dispatch = useAppDispatch();
  const [selectedLocalDocuments, setSelectedLocalDocuments] =
    useState<ISelectedDocument[]>(selectedDocuments);

  useEffect(() => {
    if (!selectedDocuments.length) setSelectedLocalDocuments([]);
  }, [selectedDocuments]);

  const [prevSelectedDocuments, setPrevSelectedDocuments] = useState<
    ISelectedDocument[]
  >([]);

  const generateNewSelectedDocuments = (
    dataItems: string[],
    statusChecked: boolean
  ) => {
    return dataItems.map((strItem): ISelectedDocument => {
      const itemFromArray = JSON.parse(strItem);
      const documentIndex = findSemanticDocumentIndexByUUID(
        semantic_documents ?? [],
        itemFromArray.semanticDocumentUUID
      );
      const page = findSemanticPageIndexByUUID(
        (semantic_documents ?? [])[documentIndex].semantic_pages,
        itemFromArray.semanticPageUUID
      );

      return {
        ...itemFromArray,
        document: (semantic_documents ?? [])[documentIndex].semantic_pages[
          page
        ],
        value: statusChecked,
        access_mode:
          semantic_documents?.[documentIndex]?.access_mode || 'read_only'
      };
    });
  };

  const updateCheckedDocument = (newDocs: ISelectedDocument[]) => {
    setSelectedLocalDocuments((prevSelected) => {
      const unique1 = prevSelected.filter(
        (prevSelectedItem) =>
          newDocs.findIndex(
            (selectedDocument) =>
              selectedDocument.semanticPageUUID ===
                prevSelectedItem.semanticPageUUID &&
              selectedDocument.semanticDocumentUUID ===
                prevSelectedItem.semanticDocumentUUID
          ) === -1
      );

      const unique2 = newDocs.filter(
        (newSelectedItem) =>
          unique1.findIndex(
            (prevSelectedDocument) =>
              prevSelectedDocument.semanticPageUUID ===
                newSelectedItem.semanticPageUUID &&
              prevSelectedDocument.semanticDocumentUUID ===
                newSelectedItem.semanticDocumentUUID
          ) === -1
      );

      return unique1.concat(unique2);
    });
  };

  const updateInitDocuments = (
    newDocs: ISelectedDocument[],
    isLeftRightSelection?: boolean,
    initDocs?: ISemanticDocumentWithInitialDocuments[]
  ) => {
    const lastValues = [...(initDocs ?? [])].filter((initDoc) => {
      const findElem = newDocs.find(
        (selectedDocument) =>
          selectedDocument.semanticPageUUID === initDoc.semanticPageUUID
      );

      return !findElem;
    });

    const newItems = newDocs
      .filter((item) => item.value)
      .map((itemFromArray) => {
        const documentIndex = findSemanticDocumentIndexByUUID(
          semantic_documents ?? [],
          itemFromArray.semanticDocumentUUID
        );
        const pageIndex = findSemanticPageIndexByUUID(
          (semantic_documents ?? [])[documentIndex].semantic_pages,
          itemFromArray.semanticPageUUID
        );

        return {
          semanticPageUUID: (semantic_documents ?? [])[documentIndex]
            .semantic_pages[pageIndex].uuid,
          semanticDocumentUUID: itemFromArray.semanticDocumentUUID,
          index: pageIndex,
          access_mode: itemFromArray.access_mode
        };
      });

    const result = isLeftRightSelection
      ? newItems
      : newItems.concat(
          lastValues.map(
            (lastValue: ISemanticDocumentWithInitialDocuments) => ({
              semanticPageUUID: lastValue.semanticPageUUID,
              semanticDocumentUUID: lastValue.semanticDocumentUUID,
              index: lastValue.index || 0, // provide a default if index is not set
              access_mode: lastValue.access_mode
            })
          )
        );
    dispatch(setInitDocs(result));
  };

  const changeCallback = (
    statusChecked: boolean,
    dataItems: string[],
    isLeftRightSelection?: boolean,
    initDocs?: ISemanticDocumentWithInitialDocuments[]
  ) => {
    if (dataItems.length > 0) {
      const newSelectedDocuments = generateNewSelectedDocuments(
        dataItems,
        statusChecked
      );
      updateCheckedDocument(newSelectedDocuments);
      if (isLeftRightSelection) {
        updateInitDocuments(newSelectedDocuments, isLeftRightSelection);
      } else {
        updateInitDocuments(newSelectedDocuments, false, initDocs);
      }
    }
  };

  const selectedDocumentToString = (
    selectedDocument: ISelectedDocumentToInitState
  ): string => {
    return JSON.stringify({
      semanticDocumentUUID: selectedDocument.semanticDocumentUUID,
      semanticPageUUID: selectedDocument.semanticPageUUID,
      key: selectedDocument.key
    });
  };

  const selectedDocumentToInitState = useMemo((): string[] => {
    const initState = (semantic_documents ?? []).reduce(
      (prev: ISelectedDocumentToInitState[], semanticDocument, indexSD) => {
        const semantic_pages: ISelectedDocumentToInitState[] =
          semanticDocument.semantic_pages.map((semanticPage, indexSP) => {
            return {
              semanticDocumentUUID: semanticDocument.uuid,
              semanticPageUUID: semanticPage.uuid,
              key: `${indexSD}_${indexSP}`
            };
          });

        return [...prev, ...semantic_pages];
      },
      []
    );

    return initState.map(selectedDocumentToString);
  }, [selectedDocuments, semantic_documents]);

  const { onChangeShift, resetValues } = useShiftSelected(
    selectedDocumentToInitState,
    changeCallback
  );

  const onChange = (
    event: EventForShift,
    stringItem: string,
    initDocs: ISemanticDocumentWithInitialDocuments[]
  ) => {
    onChangeShift(event, stringItem, initDocs);
  };

  const selectMultipleDocuments = (
    selectedDocuments: string[],
    isLeftRightSelection: boolean
  ) => {
    changeCallback(true, selectedDocuments, isLeftRightSelection);
  };

  const allSelectedDocumentsAreDisabled = useMemo(() => {
    return (
      selectedDocuments.length > 0 &&
      selectedDocuments.every((item) => item.value === false)
    );
  }, [selectedDocuments]);

  const resetAfterMoveOrCopy = useMemo(() => {
    return (
      selectedDocuments < prevSelectedDocuments &&
      selectedDocuments.length === 0
    );
  }, [selectedDocuments, prevSelectedDocuments]);

  const isSelectedEqual = useMemo(() => {
    return arraysEqual(selectedLocalDocuments, prevSelectedDocuments);
  }, [selectedLocalDocuments, prevSelectedDocuments]);

  useEffect(() => {
    if (isEndDrag || allSelectedDocumentsAreDisabled || resetAfterMoveOrCopy) {
      resetValues();
    }
  }, [resetAfterMoveOrCopy, isEndDrag, allSelectedDocumentsAreDisabled]);

  useEffect(() => {
    if (selectedLocalDocuments.length > 0) {
      if (!arraysEqual(selectedDocuments, selectedLocalDocuments)) {
        dispatch(setCheckedDocument(selectedLocalDocuments));
      }
    }
  }, [selectedLocalDocuments]);

  useEffect(() => {
    setPrevSelectedDocuments((prevSelectedDocuments) => {
      return !isSelectedEqual ? selectedDocuments : prevSelectedDocuments;
    });
  }, [selectedDocuments, setPrevSelectedDocuments, isSelectedEqual]);

  useEffect(() => {
    if (
      prevSelectedDocuments.length === selectedLocalDocuments.length &&
      allSelectedDocumentsAreDisabled &&
      !isSelectedEqual
    ) {
      setSelectedLocalDocuments(selectedDocuments);
    }
  }, [
    isSelectedEqual,
    prevSelectedDocuments,
    selectedDocuments,
    allSelectedDocumentsAreDisabled
  ]);

  useEffect(() => {
    if (isEndDrag && !isSelectedEqual && initDocuments.length === 0) {
      setSelectedLocalDocuments(selectedDocuments);
    }
  }, [isEndDrag, isSelectedEqual, initDocuments]);

  return {
    onChange,
    selectMultipleDocuments,
    selectedDocumentToString,
    setSelectedLocalDocuments
  };
};

export { useSelectedForSemanticPage };
