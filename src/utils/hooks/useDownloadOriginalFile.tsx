import { useCallback } from 'react';
import { IDownloadOriginalFileHook } from './types';
import { RootState } from '../../store/redux/types';
import { getDownloadURLAction } from '../../components/DossierFileStatus/actions';
import { useAppDispatch, useAppSelector } from '../../store/app/hooks';

export const useDownloadOriginalFile = (
  originalFileUUID: string
): IDownloadOriginalFileHook => {
  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );

  const dispatch = useAppDispatch();

  const callBackAfterGettingDownloadURLFromBackend = useCallback(
    (downloadURL: string, filename: string) => {
      const link = document.createElement('a');
      link.href = downloadURL;
      link.setAttribute('download', filename);
      link.setAttribute('target', '_blank');
      link.click();
    },
    []
  );

  const handleDownloadInOnline = useCallback(() => {
    if (uuid) {
      dispatch(
        getDownloadURLAction(
          uuid,
          originalFileUUID,
          callBackAfterGettingDownloadURLFromBackend
        )
      );
    }
  }, [uuid, originalFileUUID, callBackAfterGettingDownloadURLFromBackend]);

  return {
    handleDownloadInOnline
  };
};
