import { actionClick, actionDataClick, actionScroll } from '../../constants';

import type { IActionDefault } from '../../store/redux/types';
import type { IListPagesOfDossiersAndNumberOfDossier } from '../../components/DocumentListPreview/types';

export const SET_HIGHLIGHT_STATUS = 'DOCUMENTS/SET_HIGHLIGHT_STATUS';
export const SET_CLEAR_LOADED_IMAGES = 'DOCUMENTS/SET_CLEAR_LOADED_IMAGES';
export const SET_HIGHLIGHT_PAGE_OBJECT = 'DOCUMENTS/SET_HIGHLIGHT_PAGE_OBJECT';
export const SET_SCROLL_ACTION = 'DOCUMENTS/SET_SCROLL_ACTION';
export const SET_EXPAND = 'DOCUMENTS/SET_EXPAND';
export const SET_DOCUMENT_TITLE = 'DOCUMENTS/SET_DOCUMENT_TITLE';
export const NEW_DOCUMENT = 'DOCUMENTS/NEW_DOCUMENT';
export const SET_VISIBLE = 'DOCUMENTS/SET_VISIBLE';
export const RESET_VISIBLE_DOCS = 'DOCUMENTS/RESET_VISIBLE_DOCS';
export const SET_LOAD = 'DOCUMENTS/SET_LOAD';
export const SCROLL_AFTER_LOAD = 'DOCUMENTS/SCROLL_AFTER_LOAD';
export const ENABLE_ZOOM = 'DOCUMENTS/ENABLE_ZOOM';
export const SET_REFRESH_DOCUMENT = 'DOCUMENTS/SET_REFRESH_DOCUMENT';

export type ScrollAction =
  | typeof actionClick
  | typeof actionScroll
  | typeof actionDataClick;

export interface IDossiersData {
  dossiersData: IListPagesOfDossiersAndNumberOfDossier[];
}

export interface IDocumentListNavBarProps extends IDossiersData {
  currentPageAnnotationMode: string;
  setCurrentPageAnnotationMode: React.Dispatch<React.SetStateAction<string>>;
}

export interface IHighlightAction extends IActionDefault {
  payload: { value: boolean };
}

export interface INeedRefreshDocument extends IActionDefault {
  payload: { needRefreshDocument: boolean };
}

export interface IDocumentsState {
  isHighlight: boolean;
  highlightPageObjectUUID: string | null;
  highlightSemanticPageUUID: string | undefined;
  isExpand: boolean;
  visibleDocs: IVisibleObjects;
  loadedImg: ILoadedImg;
  scroll: IScroll;
  scrollAfterLoad: boolean;
  zoomEnable: boolean;
  documentTitle: null | string;
  needRefreshDocument: boolean;
}

export interface ISetHighlightPageObjectAction extends IActionDefault {
  payload: {
    highlightPageObjectUUID: string | null;
    highlightSemanticPageUUID: string | undefined;
  };
}

export interface ISetSelectedId extends IActionDefault {
  payload: {
    semanticDocumentUUID: string | null;
    semanticPageUUID: string | null;
    action: ScrollAction;
  };
}

export interface ISetExpandAction extends IActionDefault {
  payload: { isExpand: boolean };
}

export interface IResetVisibleAction extends IActionDefault {}

export interface ISetVisibleAction extends IActionDefault {
  payload: { isVisible: boolean; id: string; valueVisible: number };
}

export interface ISetLoadImg extends IActionDefault {
  payload: { isLoad: boolean; id: string };
}

export interface IDocumentDetailZoomAction extends IActionDefault {
  payload: { zoomEnable: boolean };
}

export interface IVisibleObject {
  isVisible: boolean;
  valueVisible: number;
}

export interface IVisibleObjects {
  [key: string]: IVisibleObject;
}

export interface ILoadedImg {
  [key: string]: boolean;
}

export interface IScrollAfterLoad extends IActionDefault {
  payload: { scrollAfterLoad: boolean };
}

export interface ISetDocumentTitleAction extends IActionDefault {
  payload: { documentTitle: string | null };
}

export interface IScroll {
  selectedSemanticDocumentUUID: string | null;
  selectedSemanticPageUUID: string | null;
  action: ScrollAction;
  lastSelectedSemanticPageUUID?: string | null;
  lastSelectedSemanticDocumentUUID?: string | null;
}

export interface IDocumentDetailPageRedirect extends IActionDefault {
  payload: { documentDetailPageRedirect: boolean };
}

export type DocumentsTypes = IHighlightAction &
  ISetHighlightPageObjectAction &
  ISetVisibleAction &
  ISetExpandAction &
  ISetDocumentTitleAction &
  IScrollAfterLoad &
  INeedRefreshDocument &
  IDocumentDetailZoomAction &
  ISetLoadImg &
  ISetSelectedId &
  IDocumentDetailPageRedirect;
