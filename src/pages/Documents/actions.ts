import {
  ENABLE_ZOOM,
  IDocumentDetailZoomAction,
  IHighlightAction,
  INeedRefreshDocument,
  IResetVisibleAction,
  IScrollAfterLoad,
  ISetDocumentTitleAction,
  ISetExpandAction,
  ISetHighlightPageObjectAction,
  ISetLoadImg,
  ISetSelectedId,
  ISetVisibleAction,
  NEW_DOCUMENT,
  RESET_HIGHLIGHT_PAGE_OBJECT,
  RESET_VISIBLE_DOCS,
  SCROLL_AFTER_LOAD,
  ScrollAction,
  SET_CLEAR_LOADED_IMAGES,
  SET_DOCUMENT_TITLE,
  SET_EXPAND,
  SET_HIGHLIGHT_PAGE_OBJECT,
  SET_HIGHLIGHT_STATUS,
  SET_LOAD,
  SET_REFRESH_DOCUMENT,
  SET_SCROLL_ACTION,
  SET_VISIBLE
} from './types';
import { IActionDefault } from '../../store/redux/types';
import type { Bbox } from '../../components/Dossiers/types';

const setHighlight = (value: boolean): IHighlightAction => ({
  type: SET_HIGHLIGHT_STATUS,
  payload: { value }
});

const setClearLoadedImages = (): IActionDefault => ({
  type: SET_CLEAR_LOADED_IMAGES
});

const setHighlightPageObject = (
  highlightPageObjectUUID: string | null,
  highlightSemanticPageUUID: string | undefined,
  highlightBbox: Bbox | undefined,
  highlightFinhurdle: boolean | undefined
): ISetHighlightPageObjectAction => ({
  type: SET_HIGHLIGHT_PAGE_OBJECT,
  payload: {
    highlightPageObjectUUID,
    highlightSemanticPageUUID,
    highlightBbox,
    highlightFinhurdle
  }
});

const setScroll = (
  semanticDocumentUUID: null | string,
  semanticPageUUID: null | string,
  action: ScrollAction
): ISetSelectedId => ({
  type: SET_SCROLL_ACTION,
  payload: { semanticDocumentUUID, semanticPageUUID, action }
});

const setExpand = (value: boolean): ISetExpandAction => ({
  type: SET_EXPAND,
  payload: { isExpand: value }
});

const refreshDocument = (): IActionDefault => {
  return { type: NEW_DOCUMENT };
};

const resetHighlightPageObject = (): IActionDefault => {
  return { type: RESET_HIGHLIGHT_PAGE_OBJECT };
};

const setVisibleDocument = (
  id: string,
  isVisible: boolean,
  valueVisible: number
): ISetVisibleAction => {
  return { type: SET_VISIBLE, payload: { id, isVisible, valueVisible } };
};

const resetVisibleDocuments = (): IResetVisibleAction => {
  return { type: RESET_VISIBLE_DOCS };
};

const setLoadDocument = (id: string, isLoad: boolean): ISetLoadImg => {
  return { type: SET_LOAD, payload: { id, isLoad } };
};

const setScrollAfterLoad = (scrollAfterLoad: boolean): IScrollAfterLoad => {
  return { type: SCROLL_AFTER_LOAD, payload: { scrollAfterLoad } };
};

const setDocumentTitle = (
  documentTitle: string | null
): ISetDocumentTitleAction => {
  return { type: SET_DOCUMENT_TITLE, payload: { documentTitle } };
};

const setZoomOnDocumentDetailPage = (
  zoomEnable: boolean
): IDocumentDetailZoomAction => {
  return { type: ENABLE_ZOOM, payload: { zoomEnable } };
};

const setRefreshDocument = (
  needRefreshDocument: boolean
): INeedRefreshDocument => {
  return { type: SET_REFRESH_DOCUMENT, payload: { needRefreshDocument } };
};

export {
  setRefreshDocument,
  setDocumentTitle,
  setZoomOnDocumentDetailPage,
  setScrollAfterLoad,
  setLoadDocument,
  setVisibleDocument,
  resetVisibleDocuments,
  setHighlight,
  setHighlightPageObject,
  setScroll,
  setExpand,
  refreshDocument,
  setClearLoadedImages,
  resetHighlightPageObject
};
