import { useNavigate } from 'react-router-dom';
import { useHotkeys } from 'react-hotkeys-hook';
import React, { useEffect, useMemo, useState, useCallback } from 'react';

import {
  setScrollTo,
  setShouldScroll
} from '@components/DossierEditor/actions.ts';
import { resetVisibleDocuments, setScroll } from './actions';
import { ROUTES } from '../../constants';
import { Pages } from '../../enums/pages';
import { actionClick } from '../../constants';
import { findSemanticDocument } from '@utils';
import { DocumentData } from '@components/DocumentData';
import { setHeroModeStatus } from '@components/HeroMode/actions.ts';
import { useDossiersDnDHandler } from '@components/Dossiers/hooks.ts';
import { DocumentListHeader } from '@components/DocumentListHeader';
import { DocumentListNavBar } from '@components/DocumentListNavBar';
import { setPageVisited } from '@components/App/actions.ts';
import { setDocumentTitle } from '@components/Dossiers/utils.tsx';
import { LayoutWrapper, BodyWrapper, HeaderWrapper } from '../../styles';
import { DocumentListMaxView } from '@components/DocumentListMaxView';
import { DocumentListPreview } from '@components/DocumentListPreview';
import { setStatusDeletedSemanticDocuments } from '@components/Header/actions.ts';
import { findSemanticDocumentIndexByUUID } from '@components/DossierImages/DossierPageViewImage/utils.ts';
import { useGenerateLinkToDetailDocumentPage } from '@hooks/useGenerateLinkToDetailDocumentPage.ts';
import { useGetSemanticPageOnDocumentDetailPage } from '@hooks/useGetSemanticPageOnDocumentDetailPage.ts';

import type { RootState } from '../../store/redux/types';
import type { IListPagesOfDossiersAndNumberOfDossier } from '@components/DocumentListPreview/types.ts';
import { useAppDispatch, useAppSelector } from '../../store/app/hooks';
import { useTranslation } from 'react-i18next';

const Documents: React.FC = () => {
  const { i18n } = useTranslation();

  const semantic_documents = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.semantic_documents
  );
  const processed_files = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.processed_files
  );
  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );
  const shouldScroll = useAppSelector(
    (state: RootState) => state.dossierEditor.shouldScroll
  );
  const canSetDocumentTitle = useAppSelector(
    (state: RootState) => state.dossiers.canSetDocumentTitle
  );
  const selectedSemanticPageUUID = useAppSelector(
    (state: RootState) => state.documents.scroll.selectedSemanticPageUUID
  );
  const documentTitle = useAppSelector(
    (state: RootState) => state.documents.documentTitle
  );
  const semanticDocumentUUID = useAppSelector(
    (state: RootState) => state.semanticDocument.semanticDocumentUUID
  );
  const pageNumber = useAppSelector(
    (state: RootState) => state.semanticDocument.pageNumber
  );
  const selectedView = useAppSelector(
    (state: RootState) => state.dossierPageTabView.selectedView
  );

  const [currentPageAnnotationMode, setCurrentPageAnnotationMode] =
    useState<string>(null);

  const dispatch = useAppDispatch();
  const [isHeroMode, setIsHeroMode] = useState(false);

  useEffect(() => {
    dispatch(setStatusDeletedSemanticDocuments(false));
  }, []);

  const showHeroMode = () => {
    dispatch(setHeroModeStatus(true));
  };
  const hideHeroMode = () => {
    dispatch(setHeroModeStatus(false));
  };

  useHotkeys(
    'ctrl+alt+shift+H',
    () => {
      setIsHeroMode((prev) => !prev);
    },
    { enabled: true }
  );

  useEffect(() => {
    isHeroMode ? showHeroMode() : hideHeroMode();
  }, [isHeroMode]);

  const { generateLinkToDetailDocumentPage } =
    useGenerateLinkToDetailDocumentPage();

  useDossiersDnDHandler();

  const navigate = useNavigate();

  const { semanticPage, semanticDocument } =
    useGetSemanticPageOnDocumentDetailPage();

  const selectedSemanticPageIndex = useMemo(() => {
    if (selectedSemanticPageUUID) {
      return semanticDocument.semantic_pages.findIndex((item) => {
        return item.uuid === selectedSemanticPageUUID;
      });
    }
  }, [semanticDocument, selectedSemanticPageUUID]);

  const getDossiersModify = useCallback(():
    | IListPagesOfDossiersAndNumberOfDossier[]
    | undefined => {
    if (semanticDocumentUUID && processed_files) {
      if (semanticDocument) {
        return semanticDocument.semantic_pages.map((semanticPage, index) => {
          const key = semanticPage.source_file_uuid;
          const neededPage: number = semanticPage.source_page_number;
          return {
            extracted_file_uuid: processed_files[key].extracted_file_uuid,
            semantic_page: semanticPage,
            image: processed_files[key].pages[neededPage].image,
            page_objects: semanticPage.page_objects,
            dossierPage: index,
            original_file_path: processed_files[key].original_file_path,
            semanticNumber: semanticPage.number,
            statusDeletedForSemanticPage: semanticPage.status_deleted || false,
            rotation_angle: semanticPage.rotation_angle,
            semantic_page_uuid: semanticPage.uuid,
            semantic_document_uuid: semanticDocument.uuid,
            filename: semanticDocument.filename
          };
        });
      }
    }
  }, [
    semantic_documents,
    semanticDocument,
    semanticDocumentUUID,
    processed_files
  ]);

  const handleClickLeftAndRightButtonsOfKeyboard = (
    indexOfDocument: number
  ) => {
    const document = findSemanticDocument(indexOfDocument, semantic_documents);

    if (document) {
      dispatch(resetVisibleDocuments());
      const url = generateLinkToDetailDocumentPage(document.uuid, 0);
      navigate(url, { replace: true });
    }
  };

  const prevSemanticDocumentIndex = useMemo(() => {
    const currentSemanticDocumentIndex = findSemanticDocumentIndexByUUID(
      semantic_documents,
      semanticDocumentUUID
    );

    return currentSemanticDocumentIndex - 1;
  }, [semantic_documents, semanticDocumentUUID]);

  const nextSemanticDocumentIndex = useMemo(() => {
    const currentSemanticDocumentIndex = findSemanticDocumentIndexByUUID(
      semantic_documents,
      semanticDocumentUUID
    );

    return currentSemanticDocumentIndex + 1;
  }, [semantic_documents, semanticDocumentUUID]);

  const handleClickUpAndDownButtonsOfKeyboard = (
    semanticDocumentUUID: string,
    indexOfPage: number
  ) => {
    if (
      indexOfPage <= semanticDocument.semantic_pages.length &&
      semanticDocument.semantic_pages[indexOfPage]
    ) {
      dispatch(
        setScroll(
          semanticDocumentUUID,
          semanticDocument.semantic_pages[indexOfPage].uuid,
          actionClick
        )
      );
      const newurl = generateLinkToDetailDocumentPage(
        semanticDocumentUUID,
        indexOfPage
      );
      navigate(newurl, { replace: true });
    }
  };

  const handleKeyboardEvents = (event: KeyboardEvent) => {
    if (event.key === 'ArrowLeft') {
      handleClickLeftAndRightButtonsOfKeyboard(prevSemanticDocumentIndex);
    } else if (event.key === 'ArrowRight') {
      handleClickLeftAndRightButtonsOfKeyboard(nextSemanticDocumentIndex);
    } else if (event.key === 'ArrowUp') {
      handleClickUpAndDownButtonsOfKeyboard(
        semanticDocument.uuid,
        Number(selectedSemanticPageIndex) - 1
      );
    } else if (event.key === 'ArrowDown') {
      handleClickUpAndDownButtonsOfKeyboard(
        semanticDocument.uuid,
        Number(selectedSemanticPageIndex) + 1
      );
    }
  };

  const memoizedValue = useCallback(
    (event: KeyboardEvent) => {
      handleKeyboardEvents(event);
    },
    [
      selectedSemanticPageIndex,
      prevSemanticDocumentIndex,
      nextSemanticDocumentIndex,
      semantic_documents,
      pageNumber
    ]
  );

  useEffect(() => {
    document.addEventListener('keydown', memoizedValue);
    return () => {
      document.removeEventListener('keydown', memoizedValue);
    };
  }, [memoizedValue]);

  useEffect(() => {
    if (!shouldScroll) {
      dispatch(setShouldScroll(true));
    }

    if (semanticPage) {
      dispatch(setScroll(semanticDocumentUUID, semanticPage.uuid, actionClick));
    }

    if (semanticDocument) {
      dispatch(setScrollTo(semanticDocument.uuid));
      // updateDocumentName();
    }
  }, [shouldScroll, documentTitle, semanticDocumentUUID]);

  const dossiersData = useMemo(
    () => getDossiersModify() || [],
    [semantic_documents, semanticDocument, semanticDocumentUUID]
  );

  useEffect(() => {
    //set document title
    setDocumentTitle(semanticDocument?.formatted_title, canSetDocumentTitle);
  }, [semanticDocument, dossiersData]);

  useEffect(() => {
    dispatch(setPageVisited(Pages.DETAIL));
  }, []);

  useEffect(() => {
    if (i18n.language && uuid) {
      window.onpopstate = () => {
        navigate(ROUTES(i18n.language).dynamic.dossierView(uuid, selectedView));
      };
    }
  });

  if (!semanticDocumentUUID) {
    return <div>Document not found!</div>;
  }

  if (!semantic_documents) {
    return null;
  }

  if (!semanticDocument) {
    return <div>Document not found!</div>;
  }

  return (
    <>
      <LayoutWrapper style={{ gridTemplateColumns: '15% 60% 25%' }}>
        <HeaderWrapper style={{ gridColumn: 'span 3' }}>
          <DocumentListHeader />
          <DocumentListNavBar
            dossiersData={dossiersData}
            currentPageAnnotationMode={currentPageAnnotationMode}
            setCurrentPageAnnotationMode={setCurrentPageAnnotationMode}
          />
        </HeaderWrapper>
        <BodyWrapper>
          <DocumentListPreview dossiersData={dossiersData} />
        </BodyWrapper>
        <BodyWrapper id="dossier-detail-view-center-section">
          <DocumentListMaxView
            dossiersData={dossiersData}
            pageNumber={pageNumber ? Number(pageNumber) : 0}
            currentPageAnnotationMode={currentPageAnnotationMode}
            setCurrentPageAnnotationMode={setCurrentPageAnnotationMode}
          />
        </BodyWrapper>
        <BodyWrapper>
          <DocumentData
            dossiersData={dossiersData}
            semanticDocumentUUID={semanticDocument.uuid}
          />
        </BodyWrapper>
      </LayoutWrapper>
    </>
  );
};

export { Documents };
