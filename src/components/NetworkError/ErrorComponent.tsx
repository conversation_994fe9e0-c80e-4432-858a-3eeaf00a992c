import React from 'react';
import { NetworkErrorModal } from './Modal/NetworkErrorModal';

interface ErrorComponentProps {
  showError: boolean;
}

const ErrorComponent: React.FC<ErrorComponentProps> = ({ showError }) => (
  <div>
    {/* Error while getting account Information: {accountQuery.error?.name} */}
    <NetworkErrorModal isModalVisible={showError} />
  </div>
);

export { ErrorComponent };
