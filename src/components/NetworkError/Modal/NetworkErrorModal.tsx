import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModalWrapper } from './style';
import { ReloadButton } from '../ReloadButton';

import type { INetworkErrorModalModalProps } from './types';
import { StyledHypodossierModal } from '../../Modal/GenericModal';
import { faCircleX } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { HYPPO_DOSSIER_RED } from '../../../constants/theme';

const NetworkErrorModal: React.FC<INetworkErrorModalModalProps> = (props) => {
  const { t } = useTranslation();

  function handleOk() {
    window.location.reload();
  }
  const isVisible =
    props.isModalVisible !== null && props.isModalVisible != false;

  const isAuthError =
    typeof props.isModalVisible == 'number' && props.isModalVisible == 401;

  const modalStyles = isAuthError
    ? {
        body: { minHeight: '12vh', overflow: 'auto' }
      }
    : {
        body: { padding: '25px', overflow: 'auto' }
      };

  // const title = isAuthError
  //   ? t('NOTIFY.ERROR.TITLE_FOR_AUTH_NETWORK_ERROR')
  //   : t('NOTIFY.ERROR.TITLE_FOR_DOSSIER_NETWORK_ERROR');

  const body = isAuthError ? (
    <>
      <span> {t('NOTIFY.ERROR.BODY_FOR_AUTH_NETWORK_ERROR')}</span>
    </>
  ) : props.isDossierAccessCheckError ? (
    <span> {t('NOTIFY.ERROR.BODY_FOR_DOSSIER_NETWORK_ERROR')}</span>
  ) : (
    <>
      <span> {t('NOTIFY.ERROR.BODY_FOR_DOSSIER_NETWORK_ERROR')}</span>
      <span> {t('NOTIFY.ERROR.RETRY_TEXT_FOR__NETWORK_ERROR')}</span>
    </>
  );

  const modalIcon = (
    <>
      <FontAwesomeIcon
        icon={faCircleX}
        style={{
          height: '14px',
          color: HYPPO_DOSSIER_RED
        }}
      />
    </>
  );

  return (
    <StyledHypodossierModal
      icon={modalIcon}
      title={t('NOTIFY.ERROR.TITLE_FOR_DOSSIER_NETWORK_ERROR')}
      open={isVisible}
      onOk={handleOk}
      cancelButtonProps={{ className: 'cancel-button' }}
      okButtonProps={{ className: 'save-button' }}
      centered={true}
      width={600}
      styles={modalStyles}
      footer={null}
      zIndex={2000}
    >
      <ModalWrapper>
        {body}
        <ReloadButton authError={isAuthError} />
      </ModalWrapper>
    </StyledHypodossierModal>
  );
};

export { NetworkErrorModal };
