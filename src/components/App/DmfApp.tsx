import React, { lazy, useEffect } from 'react';
import <PERSON><PERSON><PERSON><PERSON> from 'react-cache-buster';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';

import { AxiosBaseURLConfigurator } from './AxiosBaseURLConfigurator.tsx';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { QueryClientProvider } from '@tanstack/react-query';
import type { QueryClient } from '@tanstack/react-query';
import { Authentication } from '../Auth/Authentication.tsx';
import { CustomRouter } from '../../routes/index.tsx';
import { AccountContextProvider } from '../Account/AccountContextProvider.tsx';

import { ThemeConfigurator } from '../../ThemeConfigurator.tsx';
import MultiTenancyContextProvider from '@components/Auth/MultiTenancyContextProvider.tsx';

// Lazy load WebComponents
// @prettier-ignore
const loadWebComponents = () => import('../../SwissFex/webcomponents');

const RuntimeConfigContextProvider = lazy(
  () => import('./RuntimeConfigContextProvider.tsx')
);

interface Props {
  queryClient: QueryClient;
}

const DmfApp: React.FC<Props> = (props) => {
  const version = import.meta.env.VITE_VERSION;

  // Load webcomponents when the app mounts
  useEffect(() => {
    loadWebComponents().catch((err) =>
      console.error('Failed to load web components:', err)
    );
  }, []);

  return (
    <QueryClientProvider client={props.queryClient}>
      <RuntimeConfigContextProvider>
        <AxiosBaseURLConfigurator>
          <CacheBuster
            currentVersion={version}
            isEnabled={true} //If false, the library is disabled.
            isVerboseMode={false} //If true, the library writes verbose logs to console.
            onCacheClear={() => window.location.reload()} // Required prop
          >
            <BrowserRouter>
              <Authentication>
                <MultiTenancyContextProvider>
                  <AccountContextProvider>
                    <ThemeConfigurator>
                      <CustomRouter />
                    </ThemeConfigurator>
                  </AccountContextProvider>
                </MultiTenancyContextProvider>
              </Authentication>
            </BrowserRouter>
          </CacheBuster>
          <ReactQueryDevtools initialIsOpen={false} />
        </AxiosBaseURLConfigurator>
      </RuntimeConfigContextProvider>
    </QueryClientProvider>
  );
};

export default DmfApp;
