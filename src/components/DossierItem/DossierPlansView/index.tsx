import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';

import {
  areaCalculation,
  calculatingAreaSizeRelativeReferenceArea,
  convertFigures,
  loadImageSize
} from '../../AreaCalculator/utils';
import {
  ColumnWrapperDiv,
  GridContainer,
  GridItem,
  StyledDivContainer
} from '../style';
import { Document } from '../Document';
import { filterPageObjectsByKeys } from '../utils';
import { AreaCalculator } from '../../AreaCalculator';
import { checkIfSectionLoad, useSavingOfScrollParams } from '../../../utils';
import { useResize } from '../../../utils/hooks/useResize';
import { aggregatedPlanKeys } from '../../Dossiers/constant';
import { updatePageObject } from '../../DocumentData/actions';
import { useAreaCalculate } from '../../AreaCalculator/hooks';
import { CreateNewDossierButton } from '../../ManagerHeader/style';
import { DEFAULT_IMAGE_DIMENSIONS } from '../../AreaCalculator/constants';
import { sortPageObjects } from '../../DocumentData/DocumentDataItem/utils';
import { MARGIN_DOCUMENT, MAX_HEIGHT_DOCUMENT } from '../../../constants/theme';
import { AreaCalculatorVersion } from '../../DocumentData/DocumentDataItem/constant';
import { usePreventEventByStatusShowDeletedStatus } from '../../../utils/hooks/useShowDeletedStatus';
import { useGenerateLinkToDetailDocumentPage } from '../../../utils/hooks/useGenerateLinkToDetailDocumentPage';

import type { RootState } from '../../../store/redux/types';
import type { DossierPhotosViewProps } from '../DossierPhotosView/types';
import { useAppDispatch, useAppSelector } from '../../../store/app/hooks';
import { faRulerTriangle } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useAccountContext } from '@components/Account/AccountContextProvider';

const DossierPlansView: FC<DossierPhotosViewProps> = (props) => {
  const processed_files = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.processed_files
  );
  const semantic_documents = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.semantic_documents
  );
  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );
  const access_mode = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.access_mode
  );
  const areaList = useAppSelector(
    (state: RootState) => state.areaCalculator.areaList
  );
  const { frontend_theme } = useAccountContext();

  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { dimensions } = useResize();
  const navigate = useNavigate();

  const documentHeight = MARGIN_DOCUMENT * 2 + MAX_HEIGHT_DOCUMENT;

  const plansViewRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef(null);
  const floorPlanRef = useRef(null);

  const [isShowAreaCalculator, setIsShowAreaCalculator] =
    useState<boolean>(false);
  const [scale, setScale] = useState<number>(1);
  const [imageDimensions, setImageDimensions] = useState(
    DEFAULT_IMAGE_DIMENSIONS
  );
  const [imageRenderSize, setImageRenderSize] = useState(
    DEFAULT_IMAGE_DIMENSIONS
  );
  const [imageUrl, setImageUrl] = useState<string>('');
  const [semanticPageUUID, setSemanticPageUUID] = useState<string>('');
  const [pageObjectUUID, setPageObjectUUID] = useState<string>('');
  const [pageObjectCategory, setPageObjectCategory] = useState<string>('');
  const [pageObjectValue, setPageObjectValue] = useState<string>('');

  const areaCalculateState = useAreaCalculate(
    pageObjectCategory,
    pageObjectValue,
    isShowAreaCalculator,
    scale,
    imageDimensions,
    {
      width: imageRenderSize.width || 0,
      height: imageRenderSize.height || 0
    }
  );

  const { generateLinkToDetailDocumentPage } =
    useGenerateLinkToDetailDocumentPage();

  const saveScrollParams = useSavingOfScrollParams();

  const { preventEventByStatusShowDeletedStatus } =
    usePreventEventByStatusShowDeletedStatus(saveScrollParams);

  const handleClick = (
    event: React.MouseEvent<HTMLElement>,
    imageUrl: string,
    semanticPageUUID: string,
    pageObjectUUID: string,
    pageObjectCategory: string,
    pageObjectValue: string
  ) => {
    event.preventDefault();
    setImageUrl(imageUrl);
    setPageObjectUUID(pageObjectUUID);
    setSemanticPageUUID(semanticPageUUID);
    setPageObjectCategory(pageObjectCategory);
    setPageObjectValue(pageObjectValue);

    setIsShowAreaCalculator(true);
  };

  const handleCloseAreaCalculation = () => {
    setIsShowAreaCalculator(false);
    setScale(1);
    setImageDimensions(DEFAULT_IMAGE_DIMENSIONS);
    areaCalculateState.resetAreaCalculationState();
    setImageRenderSize(DEFAULT_IMAGE_DIMENSIONS);
  };

  const handleOkAreaCalculation = useCallback(() => {
    if (access_mode === 'read_only') {
      return;
    }
    const { width, height } = imageRenderSize;
    const areas = [...areaCalculateState.areas].map((area) => area.points);

    const totalNet = areaList.find(
      (filter) =>
        filter.semanticPageUUID === semanticPageUUID &&
        filter.pageObjectUUID === pageObjectUUID
    );
    const referenceDistanceData = { referenceFigure: {}, referenceValue: '' };
    const referenceAreaData = { referenceFigure: {}, referenceValue: '' };

    if (
      areaCalculateState?.reference?.referenceFigure?.type ===
      'referenceDistance'
    ) {
      referenceDistanceData.referenceFigure = areaCalculateState?.reference
        ?.referenceFigure?.points as number[][];
      referenceDistanceData.referenceValue =
        areaCalculateState?.reference?.value;
      referenceAreaData.referenceFigure = areaCalculateState?.hiddenReference
        ?.referenceFigure?.points as number[][];
      referenceAreaData.referenceValue =
        areaCalculateState?.hiddenReference?.value;
    } else if (
      areaCalculateState?.reference?.referenceFigure?.type === 'referenceArea'
    ) {
      referenceAreaData.referenceFigure = areaCalculateState?.reference
        ?.referenceFigure?.points as number[][];
      referenceAreaData.referenceValue = areaCalculateState?.reference?.value;
      referenceDistanceData.referenceFigure = areaCalculateState
        ?.hiddenReference?.referenceFigure?.points as number[][];
      referenceDistanceData.referenceValue =
        areaCalculateState?.hiddenReference?.value;
    }

    const data = {
      originalSize: imageDimensions,
      renderSize: {
        width: width as number,
        height: height as number
      },
      areas: areas as number[][][],
      reference: areaCalculateState.reference?.referenceFigure
        ? (areaCalculateState.reference?.referenceFigure?.points as number[][])
        : [],
      referenceDistance: referenceDistanceData?.referenceFigure as number[][],
      referenceDistanceValue: referenceDistanceData?.referenceValue,
      referenceArea: referenceAreaData?.referenceFigure as number[][],
      referenceAreaValue: referenceAreaData?.referenceValue
    };

    const figureOriginalSize = convertFigures(
      data.originalSize,
      data.renderSize,
      data.areas,
      data.reference,
      data.referenceDistance,
      data.referenceArea
    ).toOriginalSize(scale);

    const pageObjectValue = JSON.stringify({
      version: AreaCalculatorVersion,
      data: {
        areas: figureOriginalSize.areas,
        reference: figureOriginalSize.reference,
        referenceValue: areaCalculateState?.reference?.value,
        referenceType: areaCalculateState?.reference?.referenceFigure?.type,
        referenceDistance: figureOriginalSize.referenceDistance,
        referenceArea: figureOriginalSize.referenceArea,
        referenceDistanceValue: data?.referenceDistanceValue,
        referenceAreaValue: data?.referenceAreaValue,
        totalNet: totalNet?.totalNet || 0
      }
    });

    if (pageObjectValue && uuid) {
      dispatch(
        updatePageObject(
          uuid,
          semanticPageUUID,
          pageObjectUUID,
          pageObjectValue
        )
      );
    }
    handleCloseAreaCalculation();
  }, [areaList, areaCalculateState]);

  const filterSemanticDocument = { ...props.semantic_document };

  if (!filterSemanticDocument?.semantic_pages?.length) {
    return null;
  }

  filterSemanticDocument.semantic_pages =
    filterSemanticDocument.semantic_pages.filter(
      (semantic_page) =>
        filterPageObjectsByKeys(semantic_page.page_objects, aggregatedPlanKeys)
          .length
    );

  useEffect(() => {
    if (pageObjectCategory !== 'plan_floor') return;

    if (
      isShowAreaCalculator &&
      !imageDimensions.width &&
      !imageDimensions.height &&
      imageUrl
    ) {
      loadImageSize(setImageDimensions, imageUrl);
    }
  }, [
    isShowAreaCalculator,
    imageDimensions.width,
    imageDimensions.height,
    imageUrl
  ]);

  useEffect(() => {
    if (pageObjectCategory !== 'plan_floor') return;

    if (isShowAreaCalculator) {
      const floorPlanRefWidth =
        floorPlanRef.current?.getBoundingClientRect()?.width;
      const floorPlanRefHeight =
        floorPlanRef.current?.getBoundingClientRect()?.height;

      if (floorPlanRefWidth && floorPlanRefHeight) {
        setImageRenderSize({
          width: floorPlanRefWidth,
          height: floorPlanRefHeight
        });
      }
    }
  }, [
    floorPlanRef.current?.getBoundingClientRect()?.width,
    floorPlanRef.current?.getBoundingClientRect()?.height,
    imageRenderSize.width,
    imageRenderSize.height,
    isShowAreaCalculator,
    dimensions
  ]);

  useEffect(() => {
    if (pageObjectCategory !== 'plan_floor') return;

    if (imageDimensions.height && imageDimensions.width) {
      const { areas, reference, setAreas } = areaCalculateState;

      if (areas.length && reference?.referenceFigure?.area) {
        let pixelToMeter = 0;
        if (reference?.value && reference?.referenceFigure?.area) {
          pixelToMeter =
            Number(reference.value) / Number(reference.referenceFigure.area);
        }

        const updateArea = areas.map((area) => {
          if (area.type === 'area') {
            let areaSize: number;

            if (reference.referenceFigure.type === 'referenceDistance') {
              areaSize = pixelToMeter
                ? areaCalculation(area.points, pixelToMeter)
                : pixelToMeter;
            } else {
              areaSize = calculatingAreaSizeRelativeReferenceArea(
                areaCalculation(area.points),
                reference.referenceFigure.area,
                reference.value
              );
            }

            area.area = areaSize;
          }
          return area;
        });

        setAreas(updateArea);
      }
    }
  }, [
    areaCalculateState?.reference?.value,
    areaCalculateState?.reference?.referenceFigure?.area
  ]);

  const checkIsLoad = () => {
    checkIfSectionLoad(
      plansViewRef,
      documentHeight,
      props.sectionLoad,
      true,
      props.dossiers,
      () => {
        props.setSectionLoad(true);
      }
    );
  };

  useEffect(() => {
    checkIsLoad();
  }, [props.semanticDocumentUUID]);

  // WARNING_INFO: There is a warning about the div not having unique keys in the browser console.
  // Tried a lot of refactoring but the warning sticks even though Key attribute is being set.
  // Adding this comment here for future references, and leaving it for now.

  return (
    <div
      style={{ display: 'flex', flexDirection: 'column' }}
      ref={plansViewRef}
    >
      {filterSemanticDocument.semantic_pages.map((semantic_page) => {
        const style: React.CSSProperties = {};
        const filterPageObjects = filterPageObjectsByKeys(
          semantic_page.page_objects,
          aggregatedPlanKeys
        );

        const semanticDocument = semantic_documents?.find(
          (semantic_document) =>
            semantic_document.semantic_pages.filter(
              (page) => page.uuid === semantic_page.uuid
            ).length
        );

        if (!filterPageObjects.length) {
          return null;
        }

        return (
          <>
            {sortPageObjects(filterPageObjects).map((page_object, index) => {
              // Parse the JSON string into a JavaScript object
              // The AreaCalculator page object saved value is
              // {
              //   version: '1.0'
              //   data: {...}
              // }
              const data = JSON.parse(page_object.value);

              // Access the totalNet property
              const totalNetValue = data?.data?.totalNet
                ? +data.data.totalNet
                : 0;

              const pageNumber = useMemo(() => {
                return semanticDocument?.semantic_pages
                  .map((item, index) =>
                    item.uuid === page_object.semantic_page_uuid ? index : null
                  )
                  .find((item) => item !== null);
              }, [semanticDocument]);

              const handleClickLink = (
                event: React.MouseEvent<HTMLElement>
              ) => {
                event.preventDefault();

                if (!semanticDocument && !pageNumber) {
                  return;
                }

                const url = generateLinkToDetailDocumentPage(
                  semanticDocument.uuid,
                  pageNumber
                );

                navigate(url);
                preventEventByStatusShowDeletedStatus(event);
              };

              const semanticPage = props.semantic_document?.semantic_pages.find(
                (sp) => sp.uuid === page_object.semantic_page_uuid
              );

              if (semanticPage?.rotation_angle !== 0) {
                style['transform'] =
                  `rotate(${semanticPage?.rotation_angle}deg)`;
              }

              return (
                <GridContainer key={`${index}_${page_object.uuid}`}>
                  <div onClick={handleClickLink} style={{ cursor: 'pointer' }}>
                    <Document
                      semanticDocumentUUID={props.semanticDocumentUUID}
                      semanticPageUUID={page_object.semantic_page_uuid}
                      image={
                        processed_files[semantic_page.source_file_uuid].pages[
                          semantic_page.source_page_number
                        ].image
                      }
                      bbox={page_object.bbox}
                      pageObjectUuid={page_object.uuid}
                    />
                  </div>

                  <GridItem>
                    {page_object.key === 'plan_floor' && (
                      <StyledDivContainer>
                        <CreateNewDossierButton
                          type="primary"
                          style={{ width: 'max-content' }}
                          icon={
                            <FontAwesomeIcon
                              icon={faRulerTriangle}
                              style={{ height: '16px' }}
                            />
                          }
                          onClick={(event) =>
                            handleClick(
                              event,
                              processed_files[semantic_page.source_file_uuid]
                                .pages[semantic_page.source_page_number].image,
                              page_object.semantic_page_uuid,
                              page_object.uuid,
                              page_object.key,
                              page_object.value
                            )
                          }
                        >
                          {t('EDITOR.PLANS.CALCULATE_AREA_BUTTON_CAPTION')}
                        </CreateNewDossierButton>
                      </StyledDivContainer>
                    )}

                    {page_object.key === 'plan_floor' &&
                      totalNetValue !== 0 && (
                        <ColumnWrapperDiv>
                          <div
                            style={{ marginBottom: '5px', marginRight: '20px' }}
                          >
                            {t('EDITOR.PLANS.AREA_CALCULATED')}
                          </div>
                          <div>
                            {t('EDITOR.PLANS.NET')}: {totalNetValue.toFixed(2)}{' '}
                            m
                            <sup style={{ lineHeight: 0, fontSize: '9px' }}>
                              2
                            </sup>
                          </div>
                        </ColumnWrapperDiv>
                      )}
                  </GridItem>
                </GridContainer>
              );
            })}
          </>
        );
      })}
      {isShowAreaCalculator && (
        <AreaCalculator
          onCancel={handleCloseAreaCalculation}
          onOk={handleOkAreaCalculation}
          areaCalculateState={areaCalculateState}
          canvasRef={canvasRef}
          floorPlanRef={floorPlanRef}
          semanticPageImage={imageUrl}
          visible={isShowAreaCalculator}
          scale={scale}
          setScale={setScale}
          semanticPageUUID={semanticPageUUID}
          pageObjectUUID={pageObjectUUID}
        />
      )}
    </div>
  );
};

export { DossierPlansView };
