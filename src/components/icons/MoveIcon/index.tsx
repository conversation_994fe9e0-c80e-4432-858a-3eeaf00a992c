import React from 'react';
import { IDefaultIconProps } from '../types';

export interface IMoveIconProps extends IDefaultIconProps {
  color: string;
  isHover: boolean;
}

const MoveIcon: React.FC<IMoveIconProps> = (props) => {
  return props.isHover ? (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 22 20"
      height={props.height ? props.height : undefined}
      width={props.width ? props.width : 22}
      color={props.color}
      className={props.className}
    >
      <path
        style={{
          fillRule: 'evenodd',
          fill: '#5a9dcf',
          fillOpacity: 1,
          strokeWidth: 27.5,
          strokeLinecap: 'butt',
          strokeLinejoin: 'miter',
          stroke: '#fff',
          strokeOpacity: 1,
          strokeMiterlimit: 8
        }}
        d="M1620.025 557.366c0-17.32 14.025-31.343 31.341-31.343h185.257c17.316 0 31.341 14.024 31.341 31.343v268.268c0 17.32-14.025 31.343-31.341 31.343h-185.257c-17.316 0-31.341-14.024-31.341-31.343Zm0 0"
        transform="matrix(.0546 0 0 .05571 -87.672 -28.524)"
      />
      <path
        style={{
          fillRule: 'evenodd',
          fill: '#fff',
          fillOpacity: 1,
          strokeWidth: 13.75,
          strokeLinecap: 'butt',
          strokeLinejoin: 'miter',
          stroke: '#5a9dcf',
          strokeOpacity: 1,
          strokeMiterlimit: 8
        }}
        d="M1777.017 632.672h123.004v-40.668l98.961 99.005-98.96 99.006v-40.668h-123.005Zm0 0"
        transform="matrix(.0546 0 0 .05571 -87.672 -28.524)"
      />
    </svg>
  ) : (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 21 19"
      height={props.height ? props.height : undefined}
      width={props.width ? props.width : 22}
      color={props.color}
      className={props.className}
    >
      <path
        style={{
          fillRule: 'evenodd',
          fill: '#f0f8ff',
          fillOpacity: 1,
          strokeWidth: 20.625,
          strokeLinecap: 'butt',
          strokeLinejoin: 'miter',
          stroke: '#5a9dcf',
          strokeOpacity: 1,
          strokeMiterlimit: 8
        }}
        d="M1620.465 1173.829c0-17.296 14.101-31.336 31.394-31.336h185.325c17.293 0 31.32 14.04 31.32 31.336v268.342c0 17.296-14.027 31.336-31.32 31.336h-185.325c-17.293 0-31.394-14.04-31.394-31.336Zm0 0"
        transform="matrix(.05263 0 0 .05398 -84.737 -61.102)"
      />
      <path
        style={{
          fillRule: 'evenodd',
          fill: '#5a9dcf',
          fillOpacity: 1,
          strokeWidth: 13.75,
          strokeLinecap: 'butt',
          strokeLinejoin: 'miter',
          stroke: '#f0f8ff',
          strokeOpacity: 1,
          strokeMiterlimit: 8
        }}
        d="M1776.992 1248.658h122.98v-40.671l99.008 99-99.007 99v-40.599h-122.98Zm0 0"
        transform="matrix(.05263 0 0 .05398 -84.737 -61.102)"
      />
    </svg>
  );
};

export { MoveIcon };
