import React from 'react';
import { ICreationDateIconProps } from '../types';

const CreationDate: React.FC<ICreationDateIconProps> = (props) => {
  return props.isRecent ? (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        width="18px"
        height="18px"
        viewBox="0 0 15 15"
      >
        <g id="CreationDateRecent">
          <path
            style={{
              fill: 'none',
              strokeWidth: 20.625,
              strokeLinecap: 'butt',
              strokeLinejoin: 'miter',
              stroke: 'rgb(5.098039%,46.27451%,59.215686%)',
              strokeOpacity: 1,
              strokeMiterlimit: 8
            }}
            d="M 1926.511719 1044.5 C 1926.511719 953.367969 2000.367969 879.511719 2091.5 879.511719 C 2182.632031 879.511719 2256.488281 953.367969 2256.488281 1044.5 C 2256.488281 1135.632031 2182.632031 1209.488281 2091.5 1209.488281 C 2000.367969 1209.488281 1926.511719 1135.632031 1926.511719 1044.5 Z M 1926.511719 1044.5 "
            transform="matrix(0.042735,0,0,0.042735,-81.880342,-37.136752)"
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'evenodd',
              fill: 'rgb(5.098039%,46.27451%,59.215686%)',
              fillOpacity: 1
            }}
            d="M 6.28125 7.457031 C 6.28125 6.785156 6.816406 6.238281 7.480469 6.238281 C 8.140625 6.238281 8.675781 6.785156 8.675781 7.457031 C 8.675781 8.128906 8.140625 8.675781 7.480469 8.675781 C 6.816406 8.675781 6.28125 8.128906 6.28125 7.457031 Z M 6.28125 7.457031 "
          />
        </g>
      </svg>
    </>
  ) : (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        width="18px"
        height="18px"
        viewBox="0 0 15 15"
      >
        <g id="CreationDateRegular">
          <path
            style={{
              fill: 'none',
              strokeWidth: 20.625,
              strokeLinecap: 'butt',
              strokeLinejoin: 'miter',
              stroke: 'rgb(49.803922%,49.803922%,49.803922%)',
              strokeOpacity: 1,
              strokeMiterlimit: 8
            }}
            d="M 1421.511719 1044.5 C 1421.511719 953.367969 1495.367969 879.511719 1586.5 879.511719 C 1677.632031 879.511719 1751.488281 953.367969 1751.488281 1044.5 C 1751.488281 1135.632031 1677.632031 1209.488281 1586.5 1209.488281 C 1495.367969 1209.488281 1421.511719 1135.632031 1421.511719 1044.5 Z M 1421.511719 1044.5 "
            transform="matrix(0.042735,0,0,0.042735,-60.299145,-37.136752)"
          />
          <path
            style={{
              stroke: 'none',
              fillRule: 'evenodd',
              fill: 'rgb(49.803922%,49.803922%,49.803922%)',
              fillOpacity: 1
            }}
            d="M 6.238281 7.457031 C 6.238281 6.785156 6.785156 6.238281 7.457031 6.238281 C 8.128906 6.238281 8.675781 6.785156 8.675781 7.457031 C 8.675781 8.128906 8.128906 8.675781 7.457031 8.675781 C 6.785156 8.675781 6.238281 8.128906 6.238281 7.457031 Z M 6.238281 7.457031 "
          />
        </g>
      </svg>
    </>
  );
};

export { CreationDate };
