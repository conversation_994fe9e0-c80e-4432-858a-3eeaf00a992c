import React from 'react';

const PagesInHeader: React.FC = () => {
  return (
    <svg
      width="20"
      height="20"
      xmlns="http://www.w3.org/2000/svg"
      overflow="hidden"
    >
      <defs>
        <clipPath id="sdjfsdkljfskdj">
          <rect x="1749" y="525" width="513" height="513" />
        </clipPath>
        <clipPath id="sdkgjsdhjfhs">
          <rect x="-1.86265e-09" y="0" width="4.8768e+06" height="4.8768e+06" />
        </clipPath>
        <image
          width="20"
          height="20"
          xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAMAAADDpiTIAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAINUExURQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACVafQYAAACudFJOUwACAwQGDA0TFhgbICEiJiwtLi8wNzg7PD1AREhQUVJUVldYWlxdXl9gYWJjZGVmZ2hpa21vcHFzdHV3eXt9fn+Bg4SFhoeIiYqLjI2Oj5CRkpOUlZaXmJmam5ydnp+goaKjpKWmp6ipqqusra6vsLGys7S1tre4ubq7vL2+v8DBwsPExcbHyMnKy8zNzs/Q0dLT1NXW19jZ2tvd3+Dh5Obn6Orr7e/y9PX29/j8/cMBoBUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAt5SURBVHhe7dz5v1R1HcfxW4nZHpYVUWFFZQtLiposgaK4REiE4r4guOW+sai4oBiSmmaSRYttGNnnb+wyvIe5586cM2funTN+z/f9ev5072cGxs/1BZ/HgwszBQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAYB99PT/PfV7LYbiPRYb+s0zbYagsA4jYuUD7YYhMA4h3OAP15BoAZ6CmbAPgDNSTcQCcgTpyDoAzUEPWAXAGhss8AM7AMDMCuPbCltupRQo4A9VmBPBljVrre1pklrs4AxVyCuD7WmQ2zkCFnAL4oRbpwxkol1MAy7RIxPsn9EEXZ6BMTgGs0CIRfzj7qD7q4gyUyCmAH2uR6f/dU2fu1oddnIHBcgrgfC0S8fb0Zys4A3XkFMAFWiTizZOf9p+BhZ2nYaacAlilRSLe6HzedwZOLO/MMUNOAVykRSJe04QzMFROAVysRSKOaMIZGCqnAC7RIhGHNZk+A7s06uIMFOUUwGotEvGKJict5wxUySmANVok4mVNOjgDVXIKYK0WiTioySmcgQo5BbBOi0S8oEkXZ6BUTgGs1yIRBzQ5re8MHOUMnJJTABu0SMQzmvQs4AwMllMAl2qRiP2azMQZGCinADZqkYi9mhRwBgbJKYArtEjEU5oUcQYGyCmAK7VIxBOazMYZ6JNTAFdrkYjHNOmzkDMwS04B/EyLRDyiST/OwCw5BfBzLRLxkCaDcAYKcgrgWi0S8YAmA3EGZsopgF9okYj7NBmMMzBDTgH8UotE3KNJGc7AaTkFcJ0WiditSSnOQFdOAezQItO/oDUpt+AuPbXL9QzkFMCNWiTiTk2qcAY6cgrgZi0ScbsmlTgDJ+UUwK1aJOJWTar1n4EVesRITgHcrkUibtZkGM5AVgHcqUUibtRkqIXv6Ed02Z2BnALo/Y6+Q5Ph7M9ATgH0/ingdk3qMD8DOQVwjxaJ2KZJLd5nIKcA7tMiEVs1qcf6DOQUwK+0SMQWTepaNvsM7DpTj2QvpwAe1CIRmzWprf8MnK1HcpdTAA9rkYhrNKnP9gzkFMCjWiTiKk1GYXoGcgrgcS0SsUmTkXiegZwCeFKLRFyuyWgsz0BOAezRIhGXaTIqwzOQUwD7tEjEBk1G5ncGcgrgaS0SsV6T0dmdgZwCeFaLRKzTZC7MzkBOARzQIhFrNZmTPM7Ad86r5wdactpGjVK15CParczzWiRijSZzk8UZ+K/+23Oy7ywtV+JFPS/i3f3z875+ntPadwZyDCD+ukjbDfaSntaE1p2BLAOID35adQYO6VmN+KdepC3yDKD6DPxaz2nEcb1IW+QaQNUZeFVPaQQBpKL8DPxGz2gEAaSj7Ay8rscbQQAJKTkDv9XDjSCAlAw+A2/p0Ua0N4D3jrXc37VIwaAz8LYea0R7A/iqJq31DS1SNOAMrLps7HpvJkMAH5olWmSW6j8UGpPFejEC+BCdq0X6DPvewBgQQAK+pUX6DfnewBgQQAK+rUUi/tZ7J8BTGj8DBJCApVok4s9TX/+XPuxq+AwQQAK+q0Ui/jQ19enZ3/Fp9gwQQALO0yIRf5z+7IyJngECSEDvb6292/l8kmeAABLwIy0ScfTUYIJngAASsFyLRPxek8mdAQJIwEotEvE7TSZ3BgggAedrkYg3NZk2oTNAAAm4QItEvKHJSZM5AwSQgFVaJOI1TU6ZxBkggARcpEUijmgiEzgDBJCAi7VIxGFNupo/AwSQgEu0SMQhTXqaPgMEkIDVWiTiJU1maPgMEEAC1miRiBc1memMLXqwa6xngAASsFaLRDyvSVGTZ4AAErBOi0Qc0GSWBs8AASRgvRaJeEaT2frPwLoxnQECSMAGLRKxX5N+TZ0BAkjApVokYq8mAzR0BgggARu1SMRTmgzSzBkggARcoUUintBksCbOAAEk4EotEvGYJiUaOAMEkICrtUjEI5qUGf8ZIIAEbNYiEQ9pUm7cZ4AAEtD7Zf2AJhU+Nd4zQAAJ2KpFIu7XpMp4zwABJGCbFom4V5Nq4zwDBJCA7Vok4m5NhhjjGSCABFyvRSJ2aTLM+M4AASTgBi0SsVOT4cZ1BgggATdpkYg7NKlhTGeAABJwixaJuE2TOsZzBgggAbdpkYhbNKmn7wzsHf0MEEAC7tAiETdpUtMYzgABJGCnFom4QZO65n8GCCABvTdrvF6T+r42zzNAAAm4W4tEbNdkBPM8AwSQgHu1SMQ2TUYxvzNAAAm4X4tEbNVkNPM5AwSQgAe1SMQWTUY0jzNAAAl4WItEbNZkVHM/AwSQgEe1SMQ1moxurmeAABLwuBaJuEqTOZjjGSCABDypRSI2aTIXczsDBJCAPVok4nJN5mYuZ4AAErBPi0TsXjwvK/+tn6dr+BkggAQ8rUUaMPQMEEACntUijdijFylBAAl4Tos04gO9SAkCSMALWqQRBJC+g1qkEQSQvpe1SCMIIH2vaJFGEED6DmuRRhBA+o5okUYQQPoOHh+7E/raEICpRfraEIApAjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBmCMAcwRgjgCKPrNoEs7RqyWAAIo26anNOqZXSwABFBFAGQIYIwKYJAKoQgBFBFCGAMaIACaJAKoQQBEBlCGAMSKASSKAKgRgjgDMEYA5AjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBFH12cUY+rqWqEEDRZN4fYELq/I5HAEUEUIYA2ocAOgigCgEUEUAZAmgfAugggCoEUEQAZQigfQigY6QANhzPyCItVYUAzBGAOQIwRwDmCMAcAZgjAHMEYI4AzBGAOQIwRwDmCMAcAZgjAHMEYI4AzBGAOQIwRwDmCMAcAZgjAHMEYI4AzBGAOQIwRwDmCMAcAZgjgKLPLcnIWVqqCgEU8QYRZQigfQiggwCqEEARAZQhgPYhgA4CqEIARQRQhgDahwA6CKAKARStfS8jX9FSVQjAHAGYIwBzBGCOAMwRgDkCMEcA5gjAHAGYIwBzBGCOAMwRgDkCMEcA5gjAHAGYIwBzBGCOAMwRgDkCMEcA5gjAHAGYIwBzBGCOAMwRgDkCKPrC0ox8QktVIYAi3iCiDAG0DwF0EEAVAigigDIE0D4E0EEAVQigiADKEED7EEAHAVQhgKLVxzLyJS1VhQDMEYA5AjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBmCOAoi+uzMgntVQVAijiDSLKEED7EEAHAVQhgCICKEMA7UMAHQRQhQCKCKAMAbQPAXQQQBUCKLrwrYyco6WqEIA5AjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBmCMAcwRgjgDMEYA5AjBHAOYIwBwBmCMAc70A4i+V/qFnEUBeZgRQFwHkhADMEYA5AjBHAOYIwBwBmCMAcwRgrhfA/5bW9E390LYggCq9AIZ8L6C9CKAKAZgjAHMEYI4AzBGAOQIwRwDmCMAcAZgjAHMEYI4AzBGAOQIwRwDmCMAcAZgjAHMEYI4AzBGAOQIwRwDmCMAcAZgjAHMEYI4AzBGAOQIwRwDmCMAcAZgjAHMEYI4AzBGAOQIwRwDmCMAcAZgjAHMEYI4AzBGAOQIwRwDmCMCcVQA7foLZrtPXxiIAVCEAcwRgjgDMEYA5AjBHAOYIwBwBmMs2gIOvoo5D+noBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFA0NfV/4b4CnKSwBJ8AAAAASUVORK5CYII="
          preserveAspectRatio="none"
          id="sdkmfklmsdf"
        />
        <clipPath id="ksdlfjklsdjfjhg">
          <rect x="-1" y="-0.5" width="4.8768e+06" height="4.8768e+06" />
        </clipPath>
      </defs>
      <g clipPath="url(#sdjfsdkljfskdj)" transform="translate(-1749 -525)">
        <g
          clipPath="url(#sdkgjsdhjfhs)"
          transform="matrix(0.000104987 0 0 0.000104987 1749 525)"
        >
          <g clipPath="url(#ksdlfjklsdjfjhg)">
            <use
              width="100%"
              height="100%"
              xlinkHref="#sdkmfklmsdf"
              transform="matrix(9525 0 0 9525 -1 -0.5)"
            />
          </g>
        </g>
      </g>
    </svg>
  );
};

export { PagesInHeader };
