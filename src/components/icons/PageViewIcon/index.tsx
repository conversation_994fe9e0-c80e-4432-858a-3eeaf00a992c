import React from 'react';

interface PageViewIconProps {
  style: React.CSSProperties;
  width: number;
  height: number;
  stroke: string;
  onClick: () => void;
}

const PageViewIcon: React.FC<PageViewIconProps> = (props) => {
  return (
    <svg
      id="Group_87"
      data-name="Group 87"
      xmlns="http://www.w3.org/2000/svg"
      width={props.width}
      height={props.height}
      stroke={props.stroke}
      onClick={props.onClick}
      style={props.style}
    >
      <path id="Path_55" data-name="Path 55" d="M0,0H30V30H0Z" fill="none" />
      <line
        id="Line_65"
        data-name="Line 65"
        x2="0.012"
        transform="translate(18.75 10)"
        fill="none"
        stroke="current"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
      />
      <rect
        id="Rectangle_74"
        data-name="Rectangle 74"
        width="20"
        height="20"
        rx="3"
        transform="translate(5 5)"
        fill="none"
        stroke="current"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
      />
      <path
        id="Path_56"
        data-name="Path 56"
        d="M4,16.167l5-5a2.518,2.518,0,0,1,3.75,0L19,17.417"
        transform="translate(1 2.583)"
        fill="none"
        stroke="current"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
      />
      <path
        id="Path_57"
        data-name="Path 57"
        d="M14,14.417l1.25-1.25a2.518,2.518,0,0,1,3.75,0l2.5,2.5"
        transform="translate(3.5 3.083)"
        fill="none"
        stroke="current"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
      />
    </svg>
  );
};
export { PageViewIcon };
