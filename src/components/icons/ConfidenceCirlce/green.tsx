import React from 'react';

const GreenConfidence: React.FC = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="15px"
      viewBox="0 0 20 20"
      version="1.1"
    >
      <g id="surface1">
        <path
          style={{
            fillRule: 'evenodd',
            fill: '#3DCD28',
            fillOpacity: 1,
            strokeWidth: '4.58333',
            strokeLinecap: 'butt',
            strokeLinejoin: 'miter',
            stroke: '#d9d9d9',
            strokeOpacity: 1,
            strokeMiterlimit: 8
          }}
          d="M 1026.496875 2875 C 1026.496875 2857.048828 1040.601562 2842.501953 1058 2842.501953 C 1075.398438 2842.501953 1089.503125 2857.048828 1089.503125 2875 C 1089.503125 2892.951172 1075.398438 2907.498047 1058 2907.498047 C 1040.601562 2907.498047 1026.496875 2892.951172 1026.496875 2875 Z M 1026.496875 2875 "
          transform="matrix(0.294118,0,0,0.285714,-301.176471,-811.428571)"
        />
      </g>
    </svg>
  );
};

export { GreenConfidence };
