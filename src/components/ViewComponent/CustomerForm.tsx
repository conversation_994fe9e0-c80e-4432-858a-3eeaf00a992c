import React, { useState, useCallback, RefObject } from 'react';
import { CDPIframe } from './CDPIframe';
import { MessageType } from '@components/cdp/messagingProtocol/messageTypes.ts';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/redux/types';
import { useRuntimeConfig } from '../App/RuntimeConfigContextProvider';
import { useCdpDossierToken } from '../cdp/hooks';
import { FormFieldsController } from '@components/cdp/Form/FieldsController';
import { filterFieldContext } from '../cdp/utils';

export function CustomerForm() {
  const [iframeInitialized, setIframeInitialized] = useState(false);
  const [iframeRef, setIframeRef] =
    useState<RefObject<HTMLIFrameElement> | null>(null);

  const iframeRefFunc = useCallback((node: HTMLIFrameElement) => {
    if (node !== null) {
      setIframeInitialized(true);
      const iframeRevValue = {
        current: node
      };
      setIframeRef(iframeRevValue);
    }
  }, []);

  const [showIframe, setShowIframe] = useState(false);

  const dossierUuid = useSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );

  const config = useRuntimeConfig();
  const HYPODOSSIER_BACKEND_URL = config.DOSSIER_BACKEND_API_URI;

  const dossierToken = useCdpDossierToken(dossierUuid);

  // FIXME: depends on the components state - showIframe and then drill down with props to deep
  const handleGetRecommendations = (
    sourceId: string,
    fieldName: string,
    selectedValue?: string,
    isHandEdited?: boolean,
    fieldContext?: object
  ) => {
    // 1) set the field name to get recommendations for
    if (iframeRef.current) {
      const filteredFieldContext = filterFieldContext(fieldContext);

      const contentWindow = iframeRef.current.contentWindow;
      if (contentWindow) {
        contentWindow.postMessage(
          {
            type: MessageType.RECOMMENDATION_REQUESTED,
            payload: {
              requestId: sourceId,
              fieldName: fieldName,
              selectedValue: selectedValue,
              isHandEdited: isHandEdited,
              fieldContext: filteredFieldContext
            }
          },
          '*'
        );
      }
    }
    // 2) show the iframe with recommendations
    setShowIframe(true);
  };

  if (dossierToken.isError) {
    return <div>{'Token error' + dossierToken.error}</div>;
  }

  if (dossierToken.isLoading) {
    return <div>Loading Token</div>;
  }

  return (
    <div>
      <CDPIframe
        iframeRef={iframeRef}
        iframeRefFunc={iframeRefFunc}
        showIframe={showIframe}
        setShowIframe={setShowIframe}
        hypodossierBackendUrl={HYPODOSSIER_BACKEND_URL}
      />
      <FormFieldsController
        iframeRef={iframeRef}
        iframeInitialized={iframeInitialized}
        dossierUuid={dossierUuid}
        handleGetRecommendations={handleGetRecommendations}
        authToken={dossierToken.data}
      />
    </div>
  );
}
