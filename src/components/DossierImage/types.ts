import { AnonymousArrowFunction } from '../../utils/types';

import React from 'react';

export interface DossierImageProps {
  image?: string;
  style?: Record<string, any>;
  hovered?: boolean;
  onClick?(e?: React.MouseEvent): void;
  onDidMount?(): void;
  onWillUnmount?(): void;
  aspectRatio?: string;
  resize?: boolean;
  filteredColor?: string;
  borderSize?: number;
  layoutEffect?: AnonymousArrowFunction;
  isDocumentMaxView?: true;
  checked?: boolean;
  isSelected?: boolean;
  isPdfEditModeActive?: boolean;
}

export interface ImgStyleProps {
  $hovered: boolean;
  $resize: boolean;
  $resizeValue?: number;
  $filteredColor?: string;
  $borderSize?: number;
  $isDocumentMaxView?: boolean;
}
