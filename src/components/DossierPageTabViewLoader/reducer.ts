import {
  DossierPageTabViewActions,
  DossierPageTabViewState,
  SET_DOSSIER_PAGE_TAB_VIEW
} from './types';
import { VIEWS } from '../../constants/cfg';

const initialState: DossierPageTabViewState = {
  selectedView: VIEWS.pageView
};

const TabViewLoaderReducer = (
  state: DossierPageTabViewState = initialState,
  action: DossierPageTabViewActions
): DossierPageTabViewState => {
  switch (action.type) {
    case SET_DOSSIER_PAGE_TAB_VIEW:
      return {
        ...state,
        selectedView: action.payload.selectedView
      };

    default:
      return state;
  }
};

export { TabViewLoaderReducer };
