import { Col, Row } from 'antd';
import React from 'react';

import { IDossierRolesProps } from './types';
import { useTranslation } from 'react-i18next';
import { getTranslatedObjectField } from '../../utils';
import { IDossierRole } from '../Dossiers/types';

const DossierRoles: React.FC<IDossierRolesProps> = (props) => {
  const { i18n } = useTranslation();

  const getUniqueNamesFromRoles = (role: IDossierRole) => {
    const names =
      role.users?.map(
        (user) =>
          [user.first_name, user.last_name].filter(Boolean).join(' ') ||
          user.username
      ) || [];
    return [...new Set(names)];
  };

  const renderNames = (role: IDossierRole) => {
    return getUniqueNamesFromRoles(role).map((name, idx) => (
      <span key={idx}>
        {props.isOwner(name) ? <b>{name}</b> : name}
        <br />
      </span>
    ));
  };

  return (
    <div>
      {props.dossierRoles.map((role, index) => (
        <div key={role.key || index}>
          <Row>
            <Col xs={9} style={{ overflowWrap: 'anywhere' }}>
              {getTranslatedObjectField(role, 'name', i18n.language)}
            </Col>
            <Col xs={15} style={{ overflowWrap: 'anywhere' }}>
              {renderNames(role)}
            </Col>
          </Row>
        </div>
      ))}
    </div>
  );
};

export { DossierRoles };
