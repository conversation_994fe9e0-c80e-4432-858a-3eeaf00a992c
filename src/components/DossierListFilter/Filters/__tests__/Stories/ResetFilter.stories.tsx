import type { Meta, StoryObj } from '@storybook/react';
import { ResetFilters } from '../../ResetFilters';
import React from 'react';
import { configureStore } from '@reduxjs/toolkit';
import { prepareTestEnvironment } from '../../../../__tests__/utils';
import { MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { DossierListReducer } from '../../../../DossierList/reducer';
import { combineReducers } from 'redux';
import { userEvent, within } from '@storybook/test';
import { expect } from '@storybook/test';
import { AppReducer } from '../../../../App/reducer';
const store = configureStore({
  reducer: combineReducers({
    dossierList: DossierListReducer,
    dossierManager: AppReducer
  })
});

const meta: Meta<typeof ResetFilters> = {
  title: 'Dmf/Components/DossierListFilter/Filters/ResetFilter',
  component: ResetFilters,
  decorators: [
    (story) => {
      prepareTestEnvironment();
      return (
        <MemoryRouter initialEntries={['/']}>
          <Provider store={store}>{story()}</Provider>
        </MemoryRouter>
      );
    }
  ]
};

export default meta;

type Story = StoryObj<typeof ResetFilters>;

function createResetFilterPlayFn(): Story['play'] {
  return async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Test reset button to is displayed
    const resetAllBtn = canvas.getByTestId('btn-reset-all');
    await expect(resetAllBtn).toBeInTheDocument();
    await userEvent.click(resetAllBtn);
  };
}

export const ResetFilterBtn: Story = {
  render: () => <ResetFilters />,
  play: createResetFilterPlayFn()
};
