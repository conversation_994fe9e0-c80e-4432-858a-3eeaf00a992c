import {
  IDossiersState,
  SET_DOSSIERS,
  LOADING_WITH_ERRORS,
  REQUEST_DOSSIERS,
  DossiersTypes,
  SET_CREATED_DOSSIER,
  SET_ORDERING_DOSSIER_VALUE,
  SET_TYPE_ORDERING_DOSSIER,
  SET_TOTAL_DOSSIER_PAGES,
  SET_CURRENT_DOSSIER_PAGE,
  SET_SEARCH_VALUE,
  SET_FILTER_BUSINESSCASE_TYPE,
  SET_FILTER_DOSSIER_ROLES,
  SET_DOSSIER_WORK_STATUS,
  SET_FILTER_PARTNER_TYPE,
  REQUEST_DOSSIERS_FILTER,
  RESET_SEARCH_AND_FILTERS,
  SET_TOTAL_DOSSIERS_COUNT,
  SET_FILTER_STATUS_TYPE,
  SET_OWNER_NAME,
  LOADING_DOSSIERS,
  APPLY_ROLES_FILTERS,
  APPLY_STATUS_FILTERS,
  APPLY_BC_FILTERS
} from './types';
import { DESC, CREATED_AT } from '../../constants';
import { setLocalStoreFilterValues } from '../DossierListFilter/Filters/filterUtils';
import {
  DEFAULT_APPLIED_BC_FILTERS,
  DEFAULT_APPLIED_ROLES_FILTERS,
  DEFAULT_APPLIED_SEARCH_VALUE,
  DEFAULT_APPLIED_STATUS_FILTERS
} from '../DossierListFilter/constants';

const initialState: IDossiersState = {
  loading: false,
  data: [],
  searchValue: localStorage.getItem(DEFAULT_APPLIED_SEARCH_VALUE) || '',
  createdDossierUUID: null,
  orderingDossierValue: CREATED_AT,
  typeOrderingDossier: DESC,
  totalCountDossiersPage: 1,
  currentDossierPage: 1,
  filterBusinesscaseType: [],
  filterDossierRoles: [],
  filterStatusType: [],
  filterOwnerUsername: [],
  resetSearchAndFilters: false,
  filterPartnerType: [],
  totalDossiersCount: 0,
  appliedRoleFilters: [],
  appliedStatusFilters: [],
  appliedBCFilters: []
};

const DossierListReducer = (
  state: IDossiersState = initialState,
  action: DossiersTypes
): IDossiersState => {
  switch (action.type) {
    case LOADING_DOSSIERS:
      return { ...state, loading: true };

    case REQUEST_DOSSIERS:
      return { ...state, loading: true };

    case REQUEST_DOSSIERS_FILTER:
      return { ...state, loading: true };

    case SET_DOSSIERS:
      return { ...state, data: action.payload.data, loading: false };

    case LOADING_WITH_ERRORS:
      return { ...state, loading: false };

    case SET_CREATED_DOSSIER:
      return { ...state, createdDossierUUID: action.payload.uuid };

    case SET_ORDERING_DOSSIER_VALUE:
      return {
        ...state,
        orderingDossierValue: action.payload.orderingDossierValue
      };

    case SET_TYPE_ORDERING_DOSSIER:
      return {
        ...state,
        typeOrderingDossier: action.payload.typeOrderingDossier
      };

    case SET_TOTAL_DOSSIER_PAGES:
      return {
        ...state,
        totalCountDossiersPage: action.payload.paginationData.count
      };

    case SET_OWNER_NAME:
      return {
        ...state,
        filterOwnerUsername: action.payload.filterOwnerUsername
      };

    case SET_CURRENT_DOSSIER_PAGE:
      return {
        ...state,
        currentDossierPage: action.payload.currentDossierPage
      };

    case SET_SEARCH_VALUE:
      setLocalStoreFilterValues(
        DEFAULT_APPLIED_SEARCH_VALUE,
        action.payload.searchValue
      );
      return {
        ...state,
        searchValue: action.payload.searchValue
      };

    case SET_FILTER_BUSINESSCASE_TYPE:
      return {
        ...state,
        filterBusinesscaseType: action.payload.filterBusinesscaseType
      };

    case SET_FILTER_DOSSIER_ROLES:
      return {
        ...state,
        filterDossierRoles: action.payload.filterDossierRoles
      };
    case APPLY_ROLES_FILTERS:
      setLocalStoreFilterValues(
        DEFAULT_APPLIED_ROLES_FILTERS,
        action.payload.appliedRoleFilters
      );
      return {
        ...state,
        appliedRoleFilters: action.payload.appliedRoleFilters
      };
    case APPLY_STATUS_FILTERS:
      setLocalStoreFilterValues(
        DEFAULT_APPLIED_STATUS_FILTERS,
        action.payload.appliedStatusFilters
      );
      return {
        ...state,
        appliedStatusFilters: action.payload.appliedStatusFilters
      };
    case APPLY_BC_FILTERS:
      setLocalStoreFilterValues(
        DEFAULT_APPLIED_BC_FILTERS,
        action.payload.appliedBCFilters
      );
      return {
        ...state,
        appliedBCFilters: action.payload.appliedBCFilters
      };

    case SET_FILTER_PARTNER_TYPE:
      return {
        ...state,
        filterPartnerType: action.payload.filterPartnerType
      };

    case SET_FILTER_STATUS_TYPE:
      return {
        ...state,
        filterStatusType: action.payload.filterStatusType
      };

    case SET_DOSSIER_WORK_STATUS:
      const updated_data = [...state.data].map((item) => ({
        ...item,
        work_status_id:
          item.uuid === action.payload.dossier_uuid
            ? action.payload.workStatusId
            : item.work_status_id
      }));
      return {
        ...state,
        data: updated_data
      };

    case RESET_SEARCH_AND_FILTERS:
      return {
        ...state,
        resetSearchAndFilters: action.payload.resetSearchAndFilters
      };

    case SET_TOTAL_DOSSIERS_COUNT:
      return {
        ...state,
        totalDossiersCount: action.payload.totalDossiersCount
      };

    default:
      return state;
  }
};

export { DossierListReducer };
