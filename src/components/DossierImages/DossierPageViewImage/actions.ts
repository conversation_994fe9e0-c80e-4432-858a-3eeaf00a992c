import {
  IDossierPageViewInitDocumentAction,
  IDossierPageViewLastDocumentAction,
  ISetCheckedDocument,
  ISelectedDocument,
  SET_CHECKED_DOCUMENT,
  SET_INIT_DOCUMENT,
  SET_PREV_DOCUMENT,
  ISetInitDocuments,
  SET_INIT_DOCUMENTS,
  IUpdateIsEndDrag,
  UPDATE_END_DRAG,
  SET_LAST_DOCUMENTS,
  IDossierPageViewLastDocs,
  IDossierLastIndexAction,
  SET_LAST_INDEX,
  ISemanticDocumentWithInitialDocuments,
  SET_MULTIPLE,
  ISetHighlightSection,
  SET_HIGHLIGHT_SECTION,
  ISetMultipleAction,
  ISetOveredPage,
  SET_OVERED_PAGE,
  CLEAR_OVERED_PAGES,
  CLEAR_REQUEST_PAYLOAD,
  SET_REQUEST_PAYLOAD,
  IAddRequestPayloadAction,
  SemanticDocumentWithSectionFilenameAndTitle,
  SET_ANALYZE_DOCUMENT,
  ISetAnalyzeDocument,
  IAnalyzeDocument,
  C<PERSON>AR_PAGE_VIEW_STATE,
  ISetStatusRenameSemanticDocumentModal,
  CHANGE_STATUS_OF_SEMANTIC_DOCUMENT_RENAME_MODAL,
  ISetStatusSplitSemanticDocumentModal,
  CHANGE_STATUS_OF_SEMANTIC_DOCUMENT_SPLIT_MODAL,
  ISetStartSemanticDocumentsAction,
  SET_START_SEMANTIC_DOCUMENTS,
  DRAG_DND_END,
  SET_IS_LOADING_IMAGES,
  ISetIsLoaningImagesAction,
  ISetIsScrollingDocumentTabAction,
  SET_IS_SCROLLING_DOCUMENT_TAB,
  ISetSelectedDocuments,
  CLEAR_CHECKED_DOCUMENTS,
  SET_PREVIOUS_SELECTED_PAGE,
  ISetPreviousSelectedPageAction,
  SET_PREVIOUS_CHECKED,
  SET_CURRENT_SELECTED_PAGE,
  ISetPreviousCheckedAction,
  ISetCurrentSelectedPageAction
} from './types';
import { IActionDefault } from '../../../store/redux/types';
import type {
  AnonymousArrowFunction,
  IRequestedData
} from '../../../utils/types';
import { SemanticDocument } from '../../Dossiers/types';

const setLastDocument = (
  lastDocument: SemanticDocumentWithSectionFilenameAndTitle | null
): IDossierPageViewLastDocumentAction => ({
  type: SET_PREV_DOCUMENT,
  payload: { lastDocument }
});

const setInitDocument = (
  initDocument: SemanticDocumentWithSectionFilenameAndTitle | null
): IDossierPageViewInitDocumentAction => ({
  type: SET_INIT_DOCUMENT,
  payload: { initDocument }
});

const setCheckedDocument = (
  selectedDocument: ISelectedDocument[]
): ISetCheckedDocument => ({
  type: SET_CHECKED_DOCUMENT,
  payload: { selectedDocument }
});

const updateIsEndDrag = (isEndDrag: boolean): IUpdateIsEndDrag => ({
  type: UPDATE_END_DRAG,
  payload: { isEndDrag }
});

const setInitDocs = (
  initDocuments: ISemanticDocumentWithInitialDocuments[]
): ISetInitDocuments =>
  ({
    type: SET_INIT_DOCUMENTS,
    payload: { initDocuments }
  }) as ISetInitDocuments;

const setLastDocuments = (
  lastDocuments: ISemanticDocumentWithInitialDocuments[]
): IDossierPageViewLastDocs => ({
  type: SET_LAST_DOCUMENTS,
  payload: { lastDocuments }
});

const setLastIndex = (
  lastIndex: null | number | string
): IDossierLastIndexAction => ({
  type: SET_LAST_INDEX,
  payload: { lastIndex }
});

const setMultiple = (isMultiple: null | boolean): ISetMultipleAction => ({
  type: SET_MULTIPLE,
  payload: { isMultiple }
});

const setHighlightSection = (
  highlightSection: string | null
): ISetHighlightSection => {
  return { type: SET_HIGHLIGHT_SECTION, payload: { highlightSection } };
};

const setOveredPage = (
  pageId: string,
  isOverValue: boolean
): ISetOveredPage => {
  return { type: SET_OVERED_PAGE, payload: { pageId, isOverValue } };
};

const clearClearOveredPages = (): IActionDefault => ({
  type: CLEAR_OVERED_PAGES
});

const setRequestPayload = (
  requestPayload: IRequestedData[]
): IAddRequestPayloadAction => ({
  type: SET_REQUEST_PAYLOAD,
  payload: { requestPayload }
});

const clearRequestPayload = (): IActionDefault => ({
  type: CLEAR_REQUEST_PAYLOAD
});

const setAnalyzeDocument = (
  analyzeDocuments: IAnalyzeDocument[]
): ISetAnalyzeDocument => ({
  type: SET_ANALYZE_DOCUMENT,
  payload: { analyzeDocuments }
});

const clearPageViewState = (): IActionDefault => ({
  type: CLEAR_PAGE_VIEW_STATE
});

const dragDndEnd = (): IActionDefault => ({
  type: DRAG_DND_END
});

const clearSelectedDocumentsState = (
  selectedDocuments: ISelectedDocument[]
): ISetSelectedDocuments => ({
  type: CLEAR_CHECKED_DOCUMENTS,
  payload: { selectedDocuments }
});

const setStatusRenameSemanticDocumentModal = (
  semanticDocumentUUID: string,
  statusModal: boolean,
  cancelCallback?: AnonymousArrowFunction
): ISetStatusRenameSemanticDocumentModal => ({
  type: CHANGE_STATUS_OF_SEMANTIC_DOCUMENT_RENAME_MODAL,
  payload: {
    semanticDocumentUUID,
    statusModal,
    cancelCallback
  }
});

const setStatusSplitSemanticDocumentModal = (
  semanticDocumentUUID: string,
  statusModal: boolean,
  cancelCallback?: AnonymousArrowFunction
): ISetStatusSplitSemanticDocumentModal => ({
  type: CHANGE_STATUS_OF_SEMANTIC_DOCUMENT_SPLIT_MODAL,
  payload: {
    semanticDocumentUUID,
    statusModal,
    cancelCallback
  }
});

const setStartSemanticDocument = (
  semantic_documents: SemanticDocument[],
  semanticPageUUID: string,
  semanticDocumentUUID: string
): ISetStartSemanticDocumentsAction => ({
  type: SET_START_SEMANTIC_DOCUMENTS,
  payload: { semantic_documents, semanticPageUUID, semanticDocumentUUID }
});

const setIsLoadingImages = (
  isLoadingImages: boolean
): ISetIsLoaningImagesAction => ({
  type: SET_IS_LOADING_IMAGES,
  payload: { isLoadingImages }
});

const setIsScrollingDocumentTab = (
  isScrollingDocumentTab: boolean
): ISetIsScrollingDocumentTabAction => ({
  type: SET_IS_SCROLLING_DOCUMENT_TAB,
  payload: { isScrollingDocumentTab }
});

const setPreviousSelected = (
  previousSelected: any
): ISetPreviousSelectedPageAction => ({
  type: SET_PREVIOUS_SELECTED_PAGE,
  payload: { previousSelected }
});

const setPreviousChecked = (
  previousChecked: boolean
): ISetPreviousCheckedAction => ({
  type: SET_PREVIOUS_CHECKED,
  payload: { previousChecked }
});

const setCurrentSelected = (
  currentSelected: any
): ISetCurrentSelectedPageAction => ({
  type: SET_CURRENT_SELECTED_PAGE,
  payload: { currentSelected }
});

export {
  setStatusRenameSemanticDocumentModal,
  setStatusSplitSemanticDocumentModal,
  clearSelectedDocumentsState,
  clearPageViewState,
  setAnalyzeDocument,
  setRequestPayload,
  clearRequestPayload,
  clearClearOveredPages,
  setOveredPage,
  setHighlightSection,
  setMultiple,
  setLastIndex,
  setLastDocuments,
  updateIsEndDrag,
  setInitDocs,
  setLastDocument,
  setInitDocument,
  setCheckedDocument,
  setStartSemanticDocument,
  dragDndEnd,
  setIsLoadingImages,
  setIsScrollingDocumentTab,
  setPreviousSelected,
  setPreviousChecked,
  setCurrentSelected
};
