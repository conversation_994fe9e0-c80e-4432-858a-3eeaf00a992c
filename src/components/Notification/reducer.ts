import { NotificationPlacement } from '../../enums/notification';

import {
  ADD_ERROR_NOTIFICATION,
  ADD_SUCCESS_NOTIFICATION,
  DELETE_ERROR_NOTIFICATION,
  DELETE_SUCCESS_NOTIFICATION,
  INotificationState,
  NotificationTypes,
  SET_NOTIFICATION_PLACEMENT
} from './types';
import { RESET_REDUX_STATE } from '../../config/types';

const initialState: INotificationState = {
  errorNotification: [],
  successNotification: [],
  placement: NotificationPlacement.TOP_RIGHT
};

const NotificationReducer = (
  state: INotificationState = initialState,
  action: NotificationTypes
): INotificationState => {
  switch (action.type) {
    case ADD_ERROR_NOTIFICATION:
      return {
        ...state,
        errorNotification: [...state.errorNotification, action.payload]
      };

    case DELETE_ERROR_NOTIFICATION:
      return {
        ...state,
        errorNotification: action.payload.errors
      };

    case ADD_SUCCESS_NOTIFICATION:
      return {
        ...state,
        successNotification: [...state.errorNotification, action.payload]
      };

    case DELETE_SUCCESS_NOTIFICATION:
      return {
        ...state,
        successNotification: action.payload.successNotifications
      };

    case SET_NOTIFICATION_PLACEMENT:
      return {
        ...state,
        placement: action.payload.placement
      };

    case RESET_REDUX_STATE:
      return initialState;

    default:
      return state;
  }
};

export { NotificationReducer };
