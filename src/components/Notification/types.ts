import type { NotificationArgsProps } from 'antd';

import type { IActionDefault } from '../../store/redux/types';

export const ADD_ERROR_NOTIFICATION = 'NOTIFICATION/ADD_ERROR_NOTIFICATION';
export const ADD_SUCCESS_NOTIFICATION = 'NOTIFICATION/ADD_SUCCESS_NOTIFICATION';
export const DELETE_SUCCESS_NOTIFICATION =
  'NOTIFICATION/DELETE_SUCCESS_NOTIFICATION';
export const DELETE_ERROR_NOTIFICATION =
  'NOTIFICATION/DELETE_ERROR_NOTIFICATION';
export const SET_NOTIFICATION_PLACEMENT =
  'NOTIFICATION/SET_NOTIFICATION_PLACEMENT';

export interface INotification {
  title: string;
  text: string;
}

export interface IAddErrorNotificationAction extends IActionDefault {
  payload: INotification;
}

export interface IDeleteErrorNotificationAction extends IActionDefault {
  payload: {
    errors: INotification[];
  };
}

export interface IDeleteSuccessNotificationAction extends IActionDefault {
  payload: {
    successNotifications: INotification[];
  };
}

export interface INotificationPlacementAction extends IActionDefault {
  payload: {
    placement: NotificationArgsProps['placement'];
  };
}

export interface INotificationState {
  errorNotification: INotification[];
  successNotification: INotification[];
  placement: NotificationArgsProps['placement'];
}

export type NotificationTypes = IAddErrorNotificationAction &
  IDeleteErrorNotificationAction &
  IDeleteSuccessNotificationAction &
  INotificationPlacementAction;
