import styled from 'styled-components';
import { WHITE_COLOR } from '../../constants/theme';

const WidthRow = styled.div`
  width: 100%;
  height: 100%;
  background: #f2f2f2;
  overflow: auto;
  position: relative;
`;
const WrapperDiv = styled.div`
  padding-top: 10px;
  height: auto;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
`;

const Page = styled.div`
  background: ${WHITE_COLOR};
  padding: 0 15px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  margin: 5px 20px 35px;
`;

const HighlightDiv = styled.div`
  background-color: #ff74e85e;
`;

const HighlightValueDiv = styled.div`
  background-color: #ffb4e85e;
  display: block;
`;

const HighlightWindowDiv = styled.div`
  background-color: #ffb4e85e;
`;

const HighlightFinHurdleDiv = styled.div`
  background-color: #fee9a78a;
`;

const imageStyle = {
  height: '80vh'
};

const WrapperDossierImage = styled.div<{
  zoomEnable: boolean;
  isLandscape: boolean;
}>`
  display: flex;
  width: ${(props) =>
    props.zoomEnable && !props.isLandscape ? '100%' : 'fit-content'};
  height: 100%;
  position: relative;
  margin: 0px 20px 0px 20px;
`;

const WrapperHighlight = styled.div`
  margin: 5px;
  position: absolute;
`;

const RotateSectionWrapper = styled.div`
  background: linear-gradient(to left, #bfbfbf 0%, #ffffff 100%);
  position: absolute;
  width: 40px;
  border-radius: 0 10px 10px 0;

  display: flex;
  align-items: center;
  justify-content: center;

  box-shadow:
    5px 0px 5px 0 rgb(0 0 0 / 30%),
    3px 1px 3px 2px rgb(0 0 0 / 20%),
    1px 1px 6px 0 rgb(0 0 0 / 10%);
`;

export {
  RotateSectionWrapper,
  WrapperHighlight,
  WidthRow,
  imageStyle,
  WrapperDiv,
  Page,
  HighlightDiv,
  HighlightFinHurdleDiv,
  WrapperDossierImage,
  HighlightWindowDiv,
  HighlightValueDiv
};
