import React, {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';

// Redux Store Imports
import { useAppDispatch, useAppSelector } from '../../store/app/hooks';
import {
  setLoadDocument,
  setScroll,
  setScrollAfterLoad,
  setVisibleDocument
} from '../../pages/Documents/actions';
import type { RootState } from '../../store/redux/types';
import type { IVisibleObject } from '../../pages/Documents/types';

// Hooks
import { useResize } from '../../utils/hooks/useResize';
import { useOnScreen } from '../../utils';
import {
  needRotateAngle,
  useRotatedStyles
} from '../../utils/hooks/useRotatedStyles';
import { useHighlightBboxForDossierSemanticPage } from './useHighlightBboxForDossierSemanticPage';
import { useSemanticPageAnnotations } from '@components/DocumentListNavBar/AnnotationModal/hooks';
import { useAccountContext } from '@components/Account/AccountContextProvider';

// Components
import { DossierImage } from '../DossierImage';
import { HighlightPageObject } from './HighlightPageObject';
import HighlightAnnotation from '@components/DocumentListNavBar/AnnotationModal/HighlightAnnotation';
import CommentAnnotation from '@components/DocumentListNavBar/AnnotationModal/CommentAnnotation';

// Utils & Constants
import { generateVisibleDocKey, visibleParam } from './utils';
import { parseGetQueryParam, toPage } from '../../utils';
import { queryParamPageNumber } from '../../constants/routes';
import { actionClick, actionDataClick, actionScroll } from '../../constants';
import { getAnnotationsType } from '@components/DocumentListNavBar/AnnotationModal/utils';
import { ANNOTATION_TYPES } from '@components/DocumentListNavBar/AnnotationModal/constants';
import {
  mapAbsoluteCoordsToRelative,
  mapUserAnnotationsToRects,
  mapRelativeCoordsToAbsolute
} from '@components/DocumentListNavBar/AnnotationModal/rects';
import { UserAnnotationsSchema } from '../../gen/dms';

// Styles
import { Page, WrapperDiv, WrapperDossierImage } from './style';

// Types
import type { IDocumentProps } from './types';
import type {
  IRectPdfCoords,
  IRectViewportCoords
} from '@components/DocumentListNavBar/AnnotationModal/types';
import { useGetSemanticPageOnDocumentDetailPage } from '@utils/hooks/useGetSemanticPageOnDocumentDetailPage';
import { useGenerateLinkToDetailDocumentPage } from '@utils/hooks/useGenerateLinkToDetailDocumentPage';

const Document: React.FC<IDocumentProps> = ({
  dossierPage,
  index,
  isLoadPage,
  isThumbnail,
  currentPageAnnotationMode,
  setCurrentPageAnnotationMode,
  pageNumber
}) => {
  // --- Hooks ---
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();
  const { enable_semantic_document_annotations } = useAccountContext();
  const { dimensions } = useResize(); // Used in useEffect for margin calculation

  // --- Refs ---
  const imageRef = useRef<HTMLImageElement>(null);
  const wrapperImageRef = useRef<HTMLDivElement>(null);
  const imageWrapper = useRef<HTMLDivElement>(null);

  // --- Props Destructuring ---
  const {
    semantic_document_uuid: semanticDocumentUUID,
    semantic_page_uuid: semanticPageUUID,
    rotation_angle: rotationAngle,
    image: dossierImage,
    dossierPage: semanticPageNumber // Original page number from dossier data
  } = dossierPage;

  // --- State ---
  const [marginTop, setMarginTop] = useState(0); // Corrected typo: setMarinTop -> setMarginTop
  const [pdfPageRect, setPdfPageRect] = useState<IRectViewportCoords | null>(
    null
  );
  const [showCommentBubble, setShowCommentBubble] = useState(false);
  const [isZoomUpdateComplete, setIsZoomUpdateComplete] = useState(false);

  // --- Selectors ---
  const dossierAccessMode = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.access_mode
  );
  const zoomEnable = useAppSelector(
    (state: RootState) => state.documents.zoomEnable
  );
  const scrollAfterLoad = useAppSelector(
    (state: RootState) => state.documents.scrollAfterLoad
  );
  const visibleDocs = useAppSelector(
    (state: RootState) => state.documents.visibleDocs
  );
  const loadedImg = useAppSelector(
    (state: RootState) => state.documents.loadedImg // Used in useEffect for onLoad trigger
  );
  const scrollAction = useAppSelector(
    (state: RootState) => state.documents.scroll.action
  );
  const selectedSemanticDocumentUUID = useAppSelector(
    (state: RootState) => state.documents.scroll.selectedSemanticDocumentUUID
  );
  const selectedSemanticPageUUID = useAppSelector(
    (state: RootState) => state.documents.scroll.selectedSemanticPageUUID
  );
  const semantic_documents = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.semantic_documents
  );
  const isImageLoadedForPage: boolean = useAppSelector(
    (state: RootState) => state.documents.loadedImg[semanticPageUUID] ?? false // Ensure boolean value
  );
  const imgLoaded: boolean = useAppSelector(
    (state: RootState) => state.documents.loadedImg[semanticPageUUID]
  );
  const detailPageNavigationEventCallback = useAppSelector(
    (state: RootState) =>
      state.swissFexData.eventHandlers.detailPageNavigationEventCallback
  );

  // --- Derived State & Variables ---
  const queryPageId = parseGetQueryParam(queryParamPageNumber); // Renamed queryId for clarity
  const page = toPage((index + 1).toString()); // Page number for display (1-based index)
  const { visibleValue } = useOnScreen(imageRef); // On-screen visibility percentage
  const { semanticDocument } = useGetSemanticPageOnDocumentDetailPage();

  // --- Custom Hooks ---
  const { bbox, isFinHurdle } =
    useHighlightBboxForDossierSemanticPage(dossierPage);
  const { generateLinkToDetailDocumentPage } =
    useGenerateLinkToDetailDocumentPage();

  const { rotatedStyles, isLandscapeMode } = useRotatedStyles(
    wrapperImageRef,
    imageRef,
    rotationAngle,
    isImageLoadedForPage,
    true,
    false,
    isThumbnail
  );

  const enableAnnotations = enable_semantic_document_annotations;

  // Highlight Annotations Hook
  const {
    userAnnotationData: highlightAnnotationData,
    deleteAnnotationsMutation: highlightDeleteAnnotationsMutation
  } = useSemanticPageAnnotations(semanticPageUUID, ANNOTATION_TYPES.HIGHLIGHT);

  // Comment Annotations Hook
  const {
    userAnnotationData: commentAnnotationData,
    deleteAnnotationsMutation: commentDeleteAnnotationsMutation,
    createAnnotationsMutation: commentCreateAnnotationsMutation,
    updateAnnotationMutation: commentUpdateAnnotationMutation
  } = useSemanticPageAnnotations(semanticPageUUID, ANNOTATION_TYPES.COMMENT);

  // --- Callbacks ---

  const onLoad = useCallback(() => {
    // Checks if the image element exists, is fully loaded, and hasn't been marked as loaded yet
    if (imageRef.current?.complete && !isImageLoadedForPage) {
      dispatch(setLoadDocument(semanticPageUUID, true));
    }
  }, [dispatch, semanticPageUUID, isImageLoadedForPage]); // Depends on dispatch, page ID, and load status

  const calcMarginTop = useCallback(() => {
    if (imageRef.current && imageWrapper.current) {
      const { y: imageY } = imageRef.current.getBoundingClientRect();
      const { y: wrapperY } = imageWrapper.current.getBoundingClientRect();
      setMarginTop(imageY - wrapperY); // Corrected typo: setMarinTop -> setMarginTop
    }
  }, [imageRef, imageWrapper]); // Depends on refs

  const handleNavigate = useCallback(() => {
    const searchParams = new URLSearchParams(location.search);

    searchParams.set(queryParamPageNumber, semanticPageNumber.toString());

    const nextURL = generateLinkToDetailDocumentPage(
      semanticDocument.uuid,
      semanticPageNumber
    );

    navigate(nextURL, {
      replace: true // Use replace to avoid polluting history during scrolling
    });
  }, [
    location.search,
    location.pathname,
    semanticPageNumber,
    navigate,
    semanticDocument.uuid
  ]);

  const handleCommentAnnotation = useCallback(
    (
      annotationRects: IRectPdfCoords[],
      text: string,
      annotationGroupUuid?: string
    ) => {
      if (!pdfPageRect) return; // Guard against missing page dimensions

      const relativeCoords = mapAbsoluteCoordsToRelative(
        annotationRects,
        pdfPageRect.width,
        pdfPageRect.height
      );

      if (!relativeCoords.length) return; // Guard against empty coords

      const firstRelativeCoord = relativeCoords[0]; // Use the first rect for position/size data

      if (annotationGroupUuid) {
        // --- Update Existing Comment ---
        const updateData = {
          text,
          bbox_left: firstRelativeCoord.left,
          bbox_top: firstRelativeCoord.top,
          bbox_width: firstRelativeCoord.width,
          bbox_height: firstRelativeCoord.height
        };

        // Optimistic Update using React Query's cache
        if (commentAnnotationData?.user_annotations?.[annotationGroupUuid]) {
          const currentAnnotations =
            commentAnnotationData.user_annotations[annotationGroupUuid];
          const annotationToUpdateIndex = currentAnnotations.findIndex(
            (annotation) =>
              annotation.annotation_type === ANNOTATION_TYPES.COMMENT
          );

          if (annotationToUpdateIndex !== -1) {
            const updatedAnnotations = [...currentAnnotations];
            updatedAnnotations[annotationToUpdateIndex] = {
              ...updatedAnnotations[annotationToUpdateIndex],
              ...updateData // Apply updated fields
            };

            // Update the query cache
            queryClient.setQueryData(
              ['semanticPage', semanticPageUUID, ANNOTATION_TYPES.COMMENT],
              (oldData: typeof commentAnnotationData | undefined) => {
                if (!oldData) return oldData;
                return {
                  ...oldData,
                  user_annotations: {
                    ...oldData.user_annotations,
                    [annotationGroupUuid]: updatedAnnotations
                  }
                };
              }
            );
          }
        }
        // Trigger the actual mutation
        commentUpdateAnnotationMutation.mutate({
          annotationGroupUuid,
          updateData
        });
      } else {
        // --- Create New Comment ---
        const newCommentAnnotations = relativeCoords.map((annotation) => ({
          bbox_height: annotation.height,
          bbox_left: annotation.left,
          bbox_top: annotation.top,
          bbox_width: annotation.width,
          annotation_type: ANNOTATION_TYPES.COMMENT,
          text
        }));
        commentCreateAnnotationsMutation.mutate(newCommentAnnotations);
      }
    },
    [
      pdfPageRect,
      commentCreateAnnotationsMutation,
      commentUpdateAnnotationMutation,
      commentAnnotationData, // Include for optimistic update logic
      queryClient, // Include for setQueryData
      semanticPageUUID // Include for setQueryData key
    ]
  );

  const handleNewCommentAnnotation = useCallback(
    (annotationRects: IRectPdfCoords[], text: string) => {
      handleCommentAnnotation(annotationRects, text);
      setCurrentPageAnnotationMode(''); // Reset annotation mode after saving
    },
    [handleCommentAnnotation, setCurrentPageAnnotationMode]
  );

  // --- Memoized Styles ---

  const originalImagePageRect: IRectViewportCoords = useMemo(() => {
    if (imageRef.current && imgLoaded) {
      return {
        width: imageRef.current.width + 10,
        height: imageRef.current.height + 5,
        top: imageRef.current.offsetTop,
        left: imageRef.current.offsetLeft
      };
    }
    return {
      width: 0,
      height: 0,
      top: 0,
      left: 0
    };
  }, [
    imageRef?.current?.src,
    imageRef?.current?.width,
    imageRef?.current?.height,
    imgLoaded,
    zoomEnable
  ]);

  const imageAspectRatio = useMemo(() => {
    return `${imageRef?.current?.width}/${imageRef?.current?.height}`;
  }, [imageRef?.current?.src, imgLoaded]);

  // Memoized styles for the zoomed (width) and normal (height) views
  const zoomStyles = useMemo(() => {
    return zoomEnable && !needRotateAngle.includes(rotationAngle as number)
      ? {
          width: '100%',
          maxWidth: '100%',
          height: 'max-content',
          maxHeight: '100%'
        }
      : !zoomEnable && needRotateAngle.includes(rotationAngle as number)
        ? {
            maxHeight: 'min(80vh, 50vw)'
          }
        : {
            maxHeight: '80vh'
          };
  }, [rotationAngle, zoomEnable]);

  // Memoized styles for the rotated pages
  const documentStyles = useMemo(() => {
    const returnStyles = {
      ...zoomStyles
    };

    return returnStyles;
  }, [zoomStyles, rotationAngle, isLandscapeMode]);

  // --- Effects ---

  // Effect: Toggle comment bubble based on annotation mode and selected page
  useEffect(() => {
    if (
      currentPageAnnotationMode === ANNOTATION_TYPES.COMMENT &&
      semanticPageUUID === selectedSemanticPageUUID
    ) {
      setShowCommentBubble(true);
    } else {
      setShowCommentBubble(false);
    }
  }, [currentPageAnnotationMode, semanticPageUUID, selectedSemanticPageUUID]);

  // Effect: Update visibility status in Redux store when page scrolls into/out of view
  // This drives the cross-panel synchronization.
  useEffect(() => {
    if (scrollAction !== actionScroll) {
      dispatch(setScroll(semanticDocumentUUID, semanticPageUUID, actionScroll));
    }
    if (
      isLoadPage &&
      (Object.keys(visibleDocs).includes(
        generateVisibleDocKey(semanticDocumentUUID, semanticPageUUID)
      ) ||
        visibleValue > visibleParam)
    ) {
      dispatch(
        setVisibleDocument(
          generateVisibleDocKey(semanticDocumentUUID, semanticPageUUID),
          visibleValue > visibleParam,
          visibleValue
        )
      );
    }
  }, [visibleValue, isLoadPage]);

  // Effect: Auto-scroll to this page if it's the selected one upon initial load or specific navigation
  useEffect(() => {
    const currentPageNumber = detailPageNavigationEventCallback
      ? pageNumber
      : queryPageId ?? pageNumber;

    if (
      !scrollAfterLoad &&
      wrapperImageRef.current &&
      isImageLoadedForPage && // Only scroll if the page framework is loaded
      selectedSemanticDocumentUUID === semanticDocumentUUID &&
      currentPageNumber.toString() === semanticPageNumber.toString() // Check if URL matches the expected page
    ) {
      wrapperImageRef.current.scrollIntoView({
        block: 'center',
        inline: 'center'
      });

      // Mark scroll complete to prevent further automatic scrolls triggered by load events
      dispatch(setScroll(semanticDocumentUUID, semanticPageUUID, actionScroll));
      dispatch(setScrollAfterLoad(true));
    }
  }, [
    semanticPageNumber,
    wrapperImageRef,
    isImageLoadedForPage,
    selectedSemanticPageUUID,
    selectedSemanticDocumentUUID,
    pageNumber
  ]);

  useEffect(() => {
    if (
      !isZoomUpdateComplete &&
      imageRef.current &&
      selectedSemanticDocumentUUID === semanticDocumentUUID &&
      (queryPageId ?? pageNumber.toString()) === semanticPageNumber.toString()
    ) {
      requestAnimationFrame(() => {
        if (imageRef.current) {
          imageRef.current.scrollIntoView({
            block: 'center',
            inline: 'center',
            behavior: 'instant'
          });
          imageRef.current.scrollTop = 0;
          setIsZoomUpdateComplete(true);
        }
      });
    }
  }, [isZoomUpdateComplete, zoomEnable]);

  // Effect: Auto-scroll to this page if it's selected via a click action
  useEffect(() => {
    if (
      queryPageId === semanticPageNumber.toString() &&
      selectedSemanticDocumentUUID === semanticDocumentUUID &&
      (scrollAction === actionClick || scrollAction === actionDataClick) &&
      imageRef.current && // Ensure image is available
      wrapperImageRef.current && // Ensure wrapper is available
      scrollAfterLoad
    ) {
      wrapperImageRef.current.scrollIntoView({
        block: 'center',
        inline: 'center'
      });
    }
  }, [
    selectedSemanticPageUUID,
    selectedSemanticDocumentUUID,
    wrapperImageRef,
    imageRef,
    scrollAfterLoad,
    semanticPageNumber
  ]);

  // Effect: Update browser URL and Redux scroll state when this page becomes the most visible one during scrolling.
  useEffect(() => {
    if (
      visibleDocs[
        generateVisibleDocKey(
          semanticDocumentUUID,
          semanticPageUUID
        ) as keyof typeof visibleDocs
      ] !== undefined &&
      imageRef.current
    ) {
      const { bottom, top } = imageRef.current?.getBoundingClientRect();
      const valueBottom = window.innerHeight - bottom;
      const valueTop = window.innerHeight - top;
      if (
        valueBottom > window.innerHeight * 2 ||
        valueTop > window.innerHeight * 2 ||
        valueBottom < 0 - window.innerHeight ||
        valueTop < 0 - window.innerHeight
      )
        return;

      const visibleDocKey = Object.keys(visibleDocs).findIndex((key) => {
        return (
          key === generateVisibleDocKey(semanticDocumentUUID, semanticPageUUID)
        );
      });

      const visibleDoc: IVisibleObject =
        visibleDocs[
          Object.keys(visibleDocs)[visibleDocKey] as keyof typeof visibleDocs
        ];

      const maxValue = Math.max(
        ...Object.values(visibleDocs).map((value: IVisibleObject) => {
          return value.valueVisible;
        })
      );

      if (visibleDoc.valueVisible === maxValue) {
        if (
          selectedSemanticPageUUID === semanticPageUUID &&
          selectedSemanticDocumentUUID === semanticDocumentUUID &&
          scrollAction !== actionDataClick
        ) {
          dispatch(
            setScroll(semanticDocumentUUID, semanticPageUUID, actionClick)
          );
        } else if (visibleValue > visibleParam) {
          dispatch(
            setScroll(semanticDocumentUUID, semanticPageUUID, actionScroll)
          );
        }

        if (scrollAfterLoad) {
          handleNavigate();

          if (detailPageNavigationEventCallback) {
            detailPageNavigationEventCallback({
              semanticDocumentUUID: semanticDocumentUUID,
              pageNumber: semanticDocument?.semantic_pages.findIndex(
                (sp) => sp.uuid === semanticPageUUID
              )
            });
          }
        }
      }
    }
  }, [visibleDocs]);

  // Effect: Re-center the view on this page if it's selected and zoom level changes
  useEffect(() => {
    setIsZoomUpdateComplete(false);
  }, [zoomEnable]);

  // Effect: Trigger the onLoad callback when the image element is complete (useful for images loading after initial render)
  useEffect(() => {
    // Check if image ref exists, is complete, the page framework isn't loaded yet,
    // and the loaded status in Redux is specifically undefined (distinguishing initial load)
    if (
      imageRef.current?.complete &&
      !isLoadPage &&
      loadedImg[semanticPageUUID] === undefined // Check original condition from user code
    ) {
      onLoad();
    }
    // Using isImageLoadedForPage in onLoad callback handles subsequent checks
  }, [imageRef, isLoadPage, loadedImg, semanticPageUUID, onLoad]); // Depends on refs, load status, and memoized onLoad

  // Effect: Calculate margin top and page dimensions when relevant factors change
  useEffect(() => {
    if (
      imageRef.current &&
      imageWrapper.current &&
      isImageLoadedForPage // Calculate only when image is loaded
    ) {
      calcMarginTop(); // Recalculate margin

      // Calculate bounding rect for annotations
      const rect = imageRef.current.getBoundingClientRect();
      const pageDimensions = {
        // Add small buffer for potential inconsistencies? Original code added 10/5 px.
        width: rect.width + 10,
        height: rect.height + 5,
        top: rect.top,
        left: rect.left
      };
      setPdfPageRect(pageDimensions);
    }
  }, [
    isImageLoadedForPage, // Primary trigger
    imageRef, // Ref stability assumed
    imageWrapper, // Ref stability assumed
    zoomEnable, // Layout affecting
    semantic_documents, // Layout affecting? (Kept from original)
    dimensions, // Window resize affecting layout
    rotatedStyles, // Rotation affecting layout
    calcMarginTop, // Memoized callback
    imgLoaded,
    highlightAnnotationData,
    commentAnnotationData,
    dossierPage.rotation_angle
  ]);

  useEffect(() => {
    if (imageWrapper.current) {
      imageWrapper.current.style.transform = `rotate(${rotationAngle}deg)`;
    }
  }, [rotationAngle]);

  // Add new function to map annotations to absolute positions
  const mapAnnotationsToAbsolutePositions = useCallback(
    (annotations: UserAnnotationsSchema[]) => {
      if (!annotations || !annotations.length || !originalImagePageRect)
        return null;

      const rects = mapUserAnnotationsToRects(annotations);
      const absoluteRects = mapRelativeCoordsToAbsolute(
        rects,
        originalImagePageRect.width,
        originalImagePageRect.height
      );

      return absoluteRects[0] || null; // Return first rect since we only need one for comments
    },
    [originalImagePageRect]
  );

  return (
    <Fragment>
      <WrapperDiv
        ref={wrapperImageRef}
        style={{
          minHeight:
            Math.max(
              imageRef.current?.width + 20,
              imageRef.current?.height + 10
            ) || 0
        }}
        className="hd-override-max-image-wrapper"
      >
        <WrapperDossierImage
          ref={imageWrapper}
          zoomEnable={zoomEnable}
          isLandscape={needRotateAngle.includes(rotationAngle as number)}
        >
          <DossierImage
            ref={imageRef}
            aspectRatio={imageAspectRatio}
            image={dossierImage}
            style={documentStyles}
            onDidMount={onLoad}
            isDocumentMaxView={true}
          />
          {enableAnnotations &&
            highlightAnnotationData?.user_annotations &&
            Object.keys(highlightAnnotationData.user_annotations).map(
              (key) =>
                getAnnotationsType(
                  highlightAnnotationData.user_annotations[key]
                ) === ANNOTATION_TYPES.HIGHLIGHT && (
                  <HighlightAnnotation
                    key={key}
                    annotations={highlightAnnotationData.user_annotations[key]}
                    pdfPageRect={originalImagePageRect}
                    handleDeleteAnnotations={() => {
                      highlightDeleteAnnotationsMutation.mutate(key);
                    }}
                    showDeleteAnnotationIcon={
                      dossierAccessMode !== 'read_only' &&
                      semanticDocument.access_mode !== 'read_only'
                    }
                  />
                )
            )}
          {enableAnnotations &&
            commentAnnotationData?.user_annotations &&
            Object.keys(commentAnnotationData.user_annotations).map((key) => {
              const annotations = commentAnnotationData.user_annotations[key];
              const absolutePosition =
                mapAnnotationsToAbsolutePositions(annotations);

              return (
                getAnnotationsType(annotations) === ANNOTATION_TYPES.COMMENT &&
                absolutePosition && (
                  <CommentAnnotation
                    key={key}
                    rotationAngle={rotationAngle}
                    isReadOnly={rotationAngle !== 0}
                    position={absolutePosition}
                    text={annotations[0].text}
                    pdfPageRect={originalImagePageRect}
                    handleDeleteAnnotations={() => {
                      commentDeleteAnnotationsMutation.mutate(key);
                    }}
                    showAnnotationActionIcons={
                      dossierAccessMode !== 'read_only' &&
                      semanticDocument.access_mode !== 'read_only'
                    }
                    onSaveCommentHandler={handleCommentAnnotation}
                    pdfPageRef={imageRef}
                    annotationGroupUuid={key}
                  />
                )
              );
            })}
          {enableAnnotations && showCommentBubble && (
            <CommentAnnotation
              isNewComment={true}
              rotationAngle={rotationAngle}
              pdfPageRect={originalImagePageRect}
              onSaveCommentHandler={handleNewCommentAnnotation}
              pdfPageRef={imageRef}
              handleDeleteAnnotations={() => setCurrentPageAnnotationMode('')}
            />
          )}
        </WrapperDossierImage>
        {bbox && (
          <HighlightPageObject
            zoomStyles={zoomStyles}
            bbox={bbox}
            imageRef={imageRef}
            imgLoaded={imgLoaded}
            marginTop={marginTop}
            rotation_angle={0}
            isFinHurdle={isFinHurdle}
          />
        )}
      </WrapperDiv>
      <Page>{page}</Page>
    </Fragment>
  );
};
export { Document };
