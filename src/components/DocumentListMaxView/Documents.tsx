import React, { useEffect, useState } from 'react';

import { Document } from './Document';
import { useGetSemanticPageOnDocumentDetailPage } from '../../utils/hooks/useGetSemanticPageOnDocumentDetailPage';
import type { IDocumentsProps } from './types';
import type { RootState } from '../../store/redux/types';
import type { ILoadedImg } from '../../pages/Documents/types';
import { useAppDispatch, useAppSelector } from '../../store/app/hooks';
import { setScrollAfterLoad } from '../../pages/Documents/actions';

const Documents: React.FC<IDocumentsProps> = (props) => {
  const [isLoadPage, setLoadPage] = useState(false);
  const dispatch = useAppDispatch();
  const { semanticDocument } = useGetSemanticPageOnDocumentDetailPage();

  const loadedImg = useAppSelector(
    (state: RootState) => state.documents.loadedImg
  );

  useEffect(() => {
    const semanticPagesUUID = semanticDocument?.semantic_pages?.map(
      (page) => page.uuid
    );

    const loadedSemanticPagesUUID = Object.keys(loadedImg).filter((item) =>
      semanticPagesUUID?.includes(item)
    );

    const currentLoadedImages = loadedSemanticPagesUUID.reduce(
      (previousValue: ILoadedImg, uuid) => {
        previousValue[uuid] = loadedImg[uuid];
        return previousValue;
      },
      {}
    );

    if (
      loadedSemanticPagesUUID.length === props.dossiersData?.length &&
      Object.values(currentLoadedImages).every((item) => item)
    ) {
      setLoadPage(true);
    }
  }, [loadedImg, semanticDocument]);

  useEffect(() => {
    return () => {
      setLoadPage(false);
      dispatch(setScrollAfterLoad(false));
    };
  }, [semanticDocument]);

  if (!props.dossiersData) {
    return null;
  }

  return (
    <>
      {props.dossiersData?.map((dossierSemanticPage, index) => (
        <Document
          key={dossierSemanticPage.semantic_page_uuid}
          dossierPage={dossierSemanticPage}
          pageNumber={props.pageNumber}
          index={index}
          isLoadPage={isLoadPage}
          isThumbnail={false}
          currentPageAnnotationMode={props.currentPageAnnotationMode}
          setCurrentPageAnnotationMode={props.setCurrentPageAnnotationMode}
        />
      ))}
    </>
  );
};
export { Documents };
