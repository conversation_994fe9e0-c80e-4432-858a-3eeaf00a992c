export const mockDossierData3pages = [
  {
    extracted_file_uuid: '31d21807-abdd-498e-96c1-7adc70c10f24',
    semantic_page: {
      lang: 'de',
      uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6',
      status_deleted: false,
      source_file_uuid: '26a935e2-f5a2-4a05-916e-d9641224a1d3',
      source_page_number: 0,
      rotation_angle: 0,
      number: 0,
      page_category: {
        id: '100',
        name: 'VESTED_BENEFITS_ACCOUNT',
        de: null,
        en: null,
        fr: null,
        it: null,
        de_external: null,
        en_external: null,
        fr_external: null,
        it_external: null,
        additional_search_terms_de: null,
        additional_search_terms_en: null,
        additional_search_terms_fr: null,
        additional_search_terms_it: null
      },
      document_category: {
        id: '424',
        name: 'VESTED_BENEFITS_ACCOUNT',
        de: null,
        en: null,
        fr: null,
        it: null,
        de_external: null,
        en_external: null,
        fr_external: null,
        it_external: null,
        additional_search_terms_de: null,
        additional_search_terms_en: null,
        additional_search_terms_fr: null,
        additional_search_terms_it: null
      },
      confidence_summary: {
        value_formatted: '99%',
        value: 0.*********,
        level: 'high',
        color: 'yellow'
      },
      confidence: 0.*********,
      confidence_info: null,
      searchable_pdf: null,
      searchable_txt: null,
      page_objects: [
        {
          uuid: '6ad67572-f876-4597-b666-519c2612327a',
          key: 'rest_logos',
          title: 'rest_logos',
          titles: {
            de: 'rest_logos',
            en: 'rest_logos',
            fr: 'rest_logos',
            it: 'rest_logos'
          },
          visible: false,
          value: null,
          type: 'IMAGE',
          bbox: {
            ref_width: 827,
            ref_height: 1170,
            top: 10,
            left: 496,
            right: 762,
            bottom: 142
          },
          page_number: 0,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
        },
        {
          uuid: 'd16ed24d-181d-4622-bf32-8079ff47e5df',
          key: 'document_date',
          title: 'Datum',
          titles: {
            de: 'Datum',
            en: 'Date',
            fr: '[Date]',
            it: '[Date]'
          },
          visible: true,
          value: '31.12.2019',
          type: 'DATE',
          bbox: {
            ref_width: 2975,
            ref_height: 4210,
            top: 236,
            left: 247,
            right: 2707,
            bottom: 1586
          },
          page_number: 0,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
        },
        {
          uuid: '2521d2d0-4b2f-41bf-9354-a9dda3e27ad6',
          key: 'company',
          title: 'Firma',
          titles: {
            de: 'Firma',
            en: 'Company',
            fr: '[Company]',
            it: '[Company]'
          },
          visible: true,
          value: 'ZKB',
          type: 'STRING',
          bbox: {
            ref_width: 2975,
            ref_height: 4210,
            top: 236,
            left: 247,
            right: 2745,
            bottom: 4082
          },
          page_number: 0,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
        },
        {
          uuid: '281994f3-64a2-411a-bcb0-5daad1420eee',
          key: 'address_block',
          title: 'Adresse',
          titles: {
            de: 'Adresse',
            en: 'Address',
            fr: '[Address]',
            it: '[Address]'
          },
          visible: true,
          value: 'Manuel Thiemann\nBirmensdorferstrasse 578\n8055 Zürich',
          type: 'ADDRESS_BLOCK',
          bbox: {
            ref_width: 2975,
            ref_height: 4210,
            top: 900,
            left: 304,
            right: 793,
            bottom: 1117
          },
          page_number: 0,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
        },
        {
          uuid: '09759628-12e9-407d-b1fe-2d3e108348a6',
          key: 'fullname',
          title: 'Name',
          titles: {
            de: 'Name',
            en: 'Name',
            fr: '[Name]',
            it: '[Name]'
          },
          visible: true,
          value: 'Manuel Thiemann',
          type: 'STRING',
          bbox: {
            ref_width: 2975,
            ref_height: 4210,
            top: 952,
            left: 307,
            right: 654,
            bottom: 1013
          },
          page_number: 0,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
        },
        {
          uuid: '238453dd-33ad-468c-a0ed-e0065278a5d0',
          key: 'firstname',
          title: 'Vorname',
          titles: {
            de: 'Vorname',
            en: 'Firstname',
            fr: '[Firstname]',
            it: '[Firstname]'
          },
          visible: true,
          value: 'Manuel',
          type: 'STRING',
          bbox: {
            ref_width: 2975,
            ref_height: 4210,
            top: 952,
            left: 307,
            right: 452,
            bottom: 1013
          },
          page_number: 0,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
        },
        {
          uuid: '673a8ec3-a995-4d34-98d7-ecc1b0d09df7',
          key: 'street',
          title: 'Strasse',
          titles: {
            de: 'Strasse',
            en: 'Street',
            fr: '[Street]',
            it: '[Street]'
          },
          visible: true,
          value: 'Birmensdorferstrasse 578',
          type: 'STRING',
          bbox: {
            ref_width: 2975,
            ref_height: 4210,
            top: 1004,
            left: 307,
            right: 793,
            bottom: 1065
          },
          page_number: 0,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
        },
        {
          uuid: '89db8ecc-15a7-4ad9-a1d0-5969244328a2',
          key: 'zip',
          title: 'Postleitzahl',
          titles: {
            de: 'Postleitzahl',
            en: 'Zip Code',
            fr: '[Zip Code]',
            it: '[Zip Code]'
          },
          visible: true,
          value: '8055',
          type: 'INT',
          bbox: {
            ref_width: 2975,
            ref_height: 4210,
            top: 1056,
            left: 304,
            right: 408,
            bottom: 1117
          },
          page_number: 0,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
        },
        {
          uuid: '9bb7684b-0eec-43ab-b927-109ddf39bdde',
          key: 'city',
          title: 'Ort',
          titles: {
            de: 'Ort',
            en: 'City',
            fr: '[City]',
            it: '[City]'
          },
          visible: true,
          value: 'Zürich',
          type: 'STRING',
          bbox: {
            ref_width: 2975,
            ref_height: 4210,
            top: 1056,
            left: 416,
            right: 539,
            bottom: 1117
          },
          page_number: 0,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
        },
        {
          uuid: '72be5c38-4eb0-498a-ae53-6947edcb40bb',
          key: 'iban',
          title: 'IBAN',
          titles: {
            de: 'IBAN',
            en: 'IBAN',
            fr: '[IBAN]',
            it: '[IBAN]'
          },
          visible: true,
          value: 'CH33 0070 0110 0062 5719 6',
          type: 'STRING',
          bbox: {
            ref_width: 2975,
            ref_height: 4210,
            top: 1145,
            left: 2136,
            right: 2670,
            bottom: 1212
          },
          page_number: 0,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
        }
      ]
    },
    page_objects: [
      {
        uuid: '6ad67572-f876-4597-b666-519c2612327a',
        key: 'rest_logos',
        title: 'rest_logos',
        titles: {
          de: 'rest_logos',
          en: 'rest_logos',
          fr: 'rest_logos',
          it: 'rest_logos'
        },
        visible: false,
        value: null,
        type: 'IMAGE',
        bbox: {
          ref_width: 827,
          ref_height: 1170,
          top: 10,
          left: 496,
          right: 762,
          bottom: 142
        },
        page_number: 0,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
      },
      {
        uuid: 'd16ed24d-181d-4622-bf32-8079ff47e5df',
        key: 'document_date',
        title: 'Datum',
        titles: {
          de: 'Datum',
          en: 'Date',
          fr: '[Date]',
          it: '[Date]'
        },
        visible: true,
        value: '31.12.2019',
        type: 'DATE',
        bbox: {
          ref_width: 2975,
          ref_height: 4210,
          top: 236,
          left: 247,
          right: 2707,
          bottom: 1586
        },
        page_number: 0,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
      },
      {
        uuid: '2521d2d0-4b2f-41bf-9354-a9dda3e27ad6',
        key: 'company',
        title: 'Firma',
        titles: {
          de: 'Firma',
          en: 'Company',
          fr: '[Company]',
          it: '[Company]'
        },
        visible: true,
        value: 'ZKB',
        type: 'STRING',
        bbox: {
          ref_width: 2975,
          ref_height: 4210,
          top: 236,
          left: 247,
          right: 2745,
          bottom: 4082
        },
        page_number: 0,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
      },
      {
        uuid: '281994f3-64a2-411a-bcb0-5daad1420eee',
        key: 'address_block',
        title: 'Adresse',
        titles: {
          de: 'Adresse',
          en: 'Address',
          fr: '[Address]',
          it: '[Address]'
        },
        visible: true,
        value: 'Manuel Thiemann\nBirmensdorferstrasse 578\n8055 Zürich',
        type: 'ADDRESS_BLOCK',
        bbox: {
          ref_width: 2975,
          ref_height: 4210,
          top: 900,
          left: 304,
          right: 793,
          bottom: 1117
        },
        page_number: 0,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
      },
      {
        uuid: '09759628-12e9-407d-b1fe-2d3e108348a6',
        key: 'fullname',
        title: 'Name',
        titles: {
          de: 'Name',
          en: 'Name',
          fr: '[Name]',
          it: '[Name]'
        },
        visible: true,
        value: 'Manuel Thiemann',
        type: 'STRING',
        bbox: {
          ref_width: 2975,
          ref_height: 4210,
          top: 952,
          left: 307,
          right: 654,
          bottom: 1013
        },
        page_number: 0,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
      },
      {
        uuid: '238453dd-33ad-468c-a0ed-e0065278a5d0',
        key: 'firstname',
        title: 'Vorname',
        titles: {
          de: 'Vorname',
          en: 'Firstname',
          fr: '[Firstname]',
          it: '[Firstname]'
        },
        visible: true,
        value: 'Manuel',
        type: 'STRING',
        bbox: {
          ref_width: 2975,
          ref_height: 4210,
          top: 952,
          left: 307,
          right: 452,
          bottom: 1013
        },
        page_number: 0,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
      },
      {
        uuid: '673a8ec3-a995-4d34-98d7-ecc1b0d09df7',
        key: 'street',
        title: 'Strasse',
        titles: {
          de: 'Strasse',
          en: 'Street',
          fr: '[Street]',
          it: '[Street]'
        },
        visible: true,
        value: 'Birmensdorferstrasse 578',
        type: 'STRING',
        bbox: {
          ref_width: 2975,
          ref_height: 4210,
          top: 1004,
          left: 307,
          right: 793,
          bottom: 1065
        },
        page_number: 0,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
      },
      {
        uuid: '89db8ecc-15a7-4ad9-a1d0-5969244328a2',
        key: 'zip',
        title: 'Postleitzahl',
        titles: {
          de: 'Postleitzahl',
          en: 'Zip Code',
          fr: '[Zip Code]',
          it: '[Zip Code]'
        },
        visible: true,
        value: '8055',
        type: 'INT',
        bbox: {
          ref_width: 2975,
          ref_height: 4210,
          top: 1056,
          left: 304,
          right: 408,
          bottom: 1117
        },
        page_number: 0,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
      },
      {
        uuid: '9bb7684b-0eec-43ab-b927-109ddf39bdde',
        key: 'city',
        title: 'Ort',
        titles: {
          de: 'Ort',
          en: 'City',
          fr: '[City]',
          it: '[City]'
        },
        visible: true,
        value: 'Zürich',
        type: 'STRING',
        bbox: {
          ref_width: 2975,
          ref_height: 4210,
          top: 1056,
          left: 416,
          right: 539,
          bottom: 1117
        },
        page_number: 0,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
      },
      {
        uuid: '72be5c38-4eb0-498a-ae53-6947edcb40bb',
        key: 'iban',
        title: 'IBAN',
        titles: {
          de: 'IBAN',
          en: 'IBAN',
          fr: '[IBAN]',
          it: '[IBAN]'
        },
        visible: true,
        value: 'CH33 0070 0110 0062 5719 6',
        type: 'STRING',
        bbox: {
          ref_width: 2975,
          ref_height: 4210,
          top: 1145,
          left: 2136,
          right: 2670,
          bottom: 1212
        },
        page_number: 0,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6'
      }
    ],
    image:
      'https://minio.hypo.duckdns.org/dms-default-bucket/254e93ec-c0f2-4133-be04-24170c60e650/files/af927912-9373-42f8-920d-964e956b37d1/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20240617%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20240617T120000Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=cfe8843727cf9ecaa70625fd3169c6d317789e81b857a7fef64bb4809f4a4464',
    dossierPage: 0,
    original_file_path: 'sales_pitch_mix_with_errors.zip',
    semanticNumber: 0,
    statusDeletedForSemanticPage: false,
    rotation_angle: 0,
    semantic_page_uuid: '44160df0-aeaa-4fb6-8865-1ac1e14ad0f6',
    semantic_document_uuid: 'b0855f5e-a46f-4edb-8894-2c605dd158d5',
    filename: '424 Freizügigkeitskonto Manuel Thiemann ZKB 2019-12-31.pdf'
  },
  {
    extracted_file_uuid: '31d21807-abdd-498e-96c1-7adc70c10f24',
    semantic_page: {
      lang: 'de',
      uuid: '78b20895-6091-43ac-a7b2-dbbb0a4548f9',
      status_deleted: false,
      source_file_uuid: '26a935e2-f5a2-4a05-916e-d9641224a1d3',
      source_page_number: 1,
      rotation_angle: 0,
      number: 1,
      page_category: {
        id: '100',
        name: 'VESTED_BENEFITS_ACCOUNT',
        de: null,
        en: null,
        fr: null,
        it: null,
        de_external: null,
        en_external: null,
        fr_external: null,
        it_external: null,
        additional_search_terms_de: null,
        additional_search_terms_en: null,
        additional_search_terms_fr: null,
        additional_search_terms_it: null
      },
      document_category: {
        id: '424',
        name: 'VESTED_BENEFITS_ACCOUNT',
        de: null,
        en: null,
        fr: null,
        it: null,
        de_external: null,
        en_external: null,
        fr_external: null,
        it_external: null,
        additional_search_terms_de: null,
        additional_search_terms_en: null,
        additional_search_terms_fr: null,
        additional_search_terms_it: null
      },
      confidence_summary: {
        value_formatted: '99%',
        value: 0.*********,
        level: 'high',
        color: 'yellow'
      },
      confidence: 0.*********,
      confidence_info: null,
      searchable_pdf: null,
      searchable_txt: null,
      page_objects: [
        {
          uuid: '1d994279-e10b-4cd1-9a3c-c70bc021e565',
          key: 'company',
          title: 'Firma',
          titles: {
            de: 'Firma',
            en: 'Company',
            fr: '[Company]',
            it: '[Company]'
          },
          visible: true,
          value: 'ZKB',
          type: 'STRING',
          bbox: {
            ref_width: 4210,
            ref_height: 2975,
            top: 289,
            left: 91,
            right: 3975,
            bottom: 2831
          },
          page_number: 1,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: '78b20895-6091-43ac-a7b2-dbbb0a4548f9'
        },
        {
          uuid: '4a832c03-9c3c-4e2c-8454-3a32031b2d44',
          key: 'rest_logos',
          title: 'rest_logos',
          titles: {
            de: 'rest_logos',
            en: 'rest_logos',
            fr: 'rest_logos',
            it: 'rest_logos'
          },
          visible: false,
          value: null,
          type: 'IMAGE',
          bbox: {
            ref_width: 1170,
            ref_height: 827,
            top: 217,
            left: 71,
            right: 321,
            bottom: 265
          },
          page_number: 1,
          confidence: 0.64,
          confidence_summary: {
            value_formatted: '64%',
            value: 0.64,
            level: 'low',
            color: 'red'
          },
          semantic_page_uuid: '78b20895-6091-43ac-a7b2-dbbb0a4548f9'
        }
      ]
    },
    page_objects: [
      {
        uuid: '1d994279-e10b-4cd1-9a3c-c70bc021e565',
        key: 'company',
        title: 'Firma',
        titles: {
          de: 'Firma',
          en: 'Company',
          fr: '[Company]',
          it: '[Company]'
        },
        visible: true,
        value: 'ZKB',
        type: 'STRING',
        bbox: {
          ref_width: 4210,
          ref_height: 2975,
          top: 289,
          left: 91,
          right: 3975,
          bottom: 2831
        },
        page_number: 1,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: '78b20895-6091-43ac-a7b2-dbbb0a4548f9'
      },
      {
        uuid: '4a832c03-9c3c-4e2c-8454-3a32031b2d44',
        key: 'rest_logos',
        title: 'rest_logos',
        titles: {
          de: 'rest_logos',
          en: 'rest_logos',
          fr: 'rest_logos',
          it: 'rest_logos'
        },
        visible: false,
        value: null,
        type: 'IMAGE',
        bbox: {
          ref_width: 1170,
          ref_height: 827,
          top: 217,
          left: 71,
          right: 321,
          bottom: 265
        },
        page_number: 1,
        confidence: 0.64,
        confidence_summary: {
          value_formatted: '64%',
          value: 0.64,
          level: 'low',
          color: 'red'
        },
        semantic_page_uuid: '78b20895-6091-43ac-a7b2-dbbb0a4548f9'
      }
    ],
    image:
      'https://minio.hypo.duckdns.org/dms-default-bucket/254e93ec-c0f2-4133-be04-24170c60e650/files/b0b7cfbc-b43b-4a9b-8ec4-feefc7eb01af/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20240617%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20240617T120000Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=7e948075369ab4a024f774b545db790bed36c5ab4a9d0c9d1de8beb8723a6c43',
    dossierPage: 1,
    original_file_path: 'sales_pitch_mix_with_errors.zip',
    semanticNumber: 1,
    statusDeletedForSemanticPage: false,
    rotation_angle: 0,
    semantic_page_uuid: '78b20895-6091-43ac-a7b2-dbbb0a4548f9',
    semantic_document_uuid: 'b0855f5e-a46f-4edb-8894-2c605dd158d5',
    filename: '424 Freizügigkeitskonto Manuel Thiemann ZKB 2019-12-31.pdf'
  },
  {
    extracted_file_uuid: '31d21807-abdd-498e-96c1-7adc70c10f24',
    semantic_page: {
      lang: 'de',
      uuid: 'aae5c67a-d11e-4a0a-8d77-2103e9f09114',
      status_deleted: false,
      source_file_uuid: '26a935e2-f5a2-4a05-916e-d9641224a1d3',
      source_page_number: 2,
      rotation_angle: 0,
      number: 2,
      page_category: {
        id: '100',
        name: 'VESTED_BENEFITS_ACCOUNT',
        de: null,
        en: null,
        fr: null,
        it: null,
        de_external: null,
        en_external: null,
        fr_external: null,
        it_external: null,
        additional_search_terms_de: null,
        additional_search_terms_en: null,
        additional_search_terms_fr: null,
        additional_search_terms_it: null
      },
      document_category: {
        id: '424',
        name: 'VESTED_BENEFITS_ACCOUNT',
        de: null,
        en: null,
        fr: null,
        it: null,
        de_external: null,
        en_external: null,
        fr_external: null,
        it_external: null,
        additional_search_terms_de: null,
        additional_search_terms_en: null,
        additional_search_terms_fr: null,
        additional_search_terms_it: null
      },
      confidence_summary: {
        value_formatted: '99%',
        value: 0.*********,
        level: 'high',
        color: 'yellow'
      },
      confidence: 0.*********,
      confidence_info: null,
      searchable_pdf: null,
      searchable_txt: null,
      page_objects: [
        {
          uuid: '5cb3673e-6d5d-4e1d-aec0-999c21625bee',
          key: 'company',
          title: 'Firma',
          titles: {
            de: 'Firma',
            en: 'Company',
            fr: '[Company]',
            it: '[Company]'
          },
          visible: true,
          value: 'ZKB',
          type: 'STRING',
          bbox: {
            ref_width: 2975,
            ref_height: 4210,
            top: 225,
            left: 253,
            right: 2753,
            bottom: 4093
          },
          page_number: 2,
          confidence: 0.95,
          confidence_summary: {
            value_formatted: '95%',
            value: 0.95,
            level: 'high',
            color: 'yellow'
          },
          semantic_page_uuid: 'aae5c67a-d11e-4a0a-8d77-2103e9f09114'
        }
      ]
    },
    page_objects: [
      {
        uuid: '5cb3673e-6d5d-4e1d-aec0-999c21625bee',
        key: 'company',
        title: 'Firma',
        titles: {
          de: 'Firma',
          en: 'Company',
          fr: '[Company]',
          it: '[Company]'
        },
        visible: true,
        value: 'ZKB',
        type: 'STRING',
        bbox: {
          ref_width: 2975,
          ref_height: 4210,
          top: 225,
          left: 253,
          right: 2753,
          bottom: 4093
        },
        page_number: 2,
        confidence: 0.95,
        confidence_summary: {
          value_formatted: '95%',
          value: 0.95,
          level: 'high',
          color: 'yellow'
        },
        semantic_page_uuid: 'aae5c67a-d11e-4a0a-8d77-2103e9f09114'
      }
    ],
    image:
      'https://minio.hypo.duckdns.org/dms-default-bucket/254e93ec-c0f2-4133-be04-24170c60e650/files/94cbf891-b579-4d5a-8fd2-6741a45631e7/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20240617%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20240617T120000Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=53cca4611767d7ee50b53a49d8fe4b0a9b92a003a2a57fa17c26dbc76e7eb040',
    dossierPage: 2,
    original_file_path: 'sales_pitch_mix_with_errors.zip',
    semanticNumber: 2,
    statusDeletedForSemanticPage: false,
    rotation_angle: 0,
    semantic_page_uuid: 'aae5c67a-d11e-4a0a-8d77-2103e9f09114',
    semantic_document_uuid: 'b0855f5e-a46f-4edb-8894-2c605dd158d5',
    filename: '424 Freizügigkeitskonto Manuel Thiemann ZKB 2019-12-31.pdf'
  }
];
