// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`should render DocumentsListMaxView/Documents component with empty props > should render component and match previous Snapshot with dossierData prop > empty dossierData prop 1`] = `null`;

exports[`should render DocumentsListMaxView/Documents component with mock dossierData prop (1 page) > should render component and match previous Snapshot with mock dossierData prop > 1 page dossierData prop 1`] = `
[
  <div
    className="sc-dprtRQ gJAxsb hd-override-max-image-wrapper"
    style={
      {
        "minHeight": 0,
      }
    }
  >
    <div
      className="sc-bMTdWJ ctGsZh"
      isLandscape={false}
    >
      <div
        style={
          {
            "alignItems": "center",
            "display": "flex",
            "height": "100%",
            "justifyContent": "center",
            "width": "100%",
          }
        }
      >
        <img
          alt="Document"
          className="sc-gQaihK dvpgjw hd-override-document-image"
          onLoad={[Function]}
          src="https://minio.hypo.duckdns.org/dms-default-bucket/254e93ec-c0f2-4133-be04-24170c60e650/files/55aeb2ed-f650-4824-8dc7-4179d16abb72/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20240617%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20240617T120000Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=18623e89f8c2af3cfb8574cbf4b29ba311d0dd83d62a7c2af69254d37a4b1791"
          style={
            {
              "aspectRatio": "undefined/undefined",
              "maxHeight": "80vh",
              "objectFit": "contain",
            }
          }
        />
      </div>
    </div>
  </div>,
  <div
    className="sc-eUlrpB vmqex"
  >
    01
  </div>,
]
`;

exports[`should render DocumentsListMaxView/Documents component with mock dossierData prop (3 pages) > should render component and match previous Snapshot with mock dossierData prop > 3 pages dossierData prop 1`] = `
[
  <div
    className="sc-dprtRQ gJAxsb hd-override-max-image-wrapper"
    style={
      {
        "minHeight": 0,
      }
    }
  >
    <div
      className="sc-bMTdWJ ctGsZh"
      isLandscape={false}
    >
      <div
        style={
          {
            "alignItems": "center",
            "display": "flex",
            "height": "100%",
            "justifyContent": "center",
            "width": "100%",
          }
        }
      >
        <img
          alt="Document"
          className="sc-gQaihK dvpgjw hd-override-document-image"
          onLoad={[Function]}
          src="https://minio.hypo.duckdns.org/dms-default-bucket/254e93ec-c0f2-4133-be04-24170c60e650/files/af927912-9373-42f8-920d-964e956b37d1/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20240617%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20240617T120000Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=cfe8843727cf9ecaa70625fd3169c6d317789e81b857a7fef64bb4809f4a4464"
          style={
            {
              "aspectRatio": "undefined/undefined",
              "maxHeight": "80vh",
              "objectFit": "contain",
            }
          }
        />
      </div>
    </div>
  </div>,
  <div
    className="sc-eUlrpB vmqex"
  >
    01
  </div>,
  <div
    className="sc-dprtRQ gJAxsb hd-override-max-image-wrapper"
    style={
      {
        "minHeight": 0,
      }
    }
  >
    <div
      className="sc-bMTdWJ ctGsZh"
      isLandscape={false}
    >
      <div
        style={
          {
            "alignItems": "center",
            "display": "flex",
            "height": "100%",
            "justifyContent": "center",
            "width": "100%",
          }
        }
      >
        <img
          alt="Document"
          className="sc-gQaihK dvpgjw hd-override-document-image"
          onLoad={[Function]}
          src="https://minio.hypo.duckdns.org/dms-default-bucket/254e93ec-c0f2-4133-be04-24170c60e650/files/b0b7cfbc-b43b-4a9b-8ec4-feefc7eb01af/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20240617%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20240617T120000Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=7e948075369ab4a024f774b545db790bed36c5ab4a9d0c9d1de8beb8723a6c43"
          style={
            {
              "aspectRatio": "undefined/undefined",
              "maxHeight": "80vh",
              "objectFit": "contain",
            }
          }
        />
      </div>
    </div>
  </div>,
  <div
    className="sc-eUlrpB vmqex"
  >
    02
  </div>,
  <div
    className="sc-dprtRQ gJAxsb hd-override-max-image-wrapper"
    style={
      {
        "minHeight": 0,
      }
    }
  >
    <div
      className="sc-bMTdWJ ctGsZh"
      isLandscape={false}
    >
      <div
        style={
          {
            "alignItems": "center",
            "display": "flex",
            "height": "100%",
            "justifyContent": "center",
            "width": "100%",
          }
        }
      >
        <img
          alt="Document"
          className="sc-gQaihK dvpgjw hd-override-document-image"
          onLoad={[Function]}
          src="https://minio.hypo.duckdns.org/dms-default-bucket/254e93ec-c0f2-4133-be04-24170c60e650/files/94cbf891-b579-4d5a-8fd2-6741a45631e7/0.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=S3_ACCESS_KEY%2F20240617%2Fch-dk-2%2Fs3%2Faws4_request&X-Amz-Date=20240617T120000Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=53cca4611767d7ee50b53a49d8fe4b0a9b92a003a2a57fa17c26dbc76e7eb040"
          style={
            {
              "aspectRatio": "undefined/undefined",
              "maxHeight": "80vh",
              "objectFit": "contain",
            }
          }
        />
      </div>
    </div>
  </div>,
  <div
    className="sc-eUlrpB vmqex"
  >
    03
  </div>,
]
`;
