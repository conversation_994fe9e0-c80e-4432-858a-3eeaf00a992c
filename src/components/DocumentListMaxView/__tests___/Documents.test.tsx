import { render, screen, cleanup } from '@testing-library/react';
import { create as rendererCreate } from 'react-test-renderer';
import { Documents } from '../Documents';

// mocks
import { mockDossier } from './mockDossier';
import { mockDossierData } from './mockDossierData';
import { mockDossierData3pages } from './mockDossierData3pages';
import { IListPagesOfDossiersAndNumberOfDossier } from '../../DocumentListPreview/types';
import { getThemeConfig } from '../../../constants/theme';
import {
  QueryClient,
  QueryClientProvider,
  useQuery
} from '@tanstack/react-query';
import { AccountContextProvider } from '../../Account/AccountContextProvider.tsx';
import type { Mock } from 'vitest';

const mockState = {
  dossierManager: { activeThemeConfig: getThemeConfig() },
  dossiers: {
    dossiersData: {
      semantic_documents: mockDossier.semantic_documents,
      uuid: mockDossier.uuid
    }
  },
  dashboard: {
    structureDetailsSelectedDocument: mockDossier.semantic_documents[0]
  },
  semanticDocument: {
    semanticDocumentUUID: mockDossier.semantic_documents[0].uuid
  },
  dossierPageTabView: {
    selectedView: 'data'
  },
  header: {
    imageSize: 'small'
  },
  documents: {
    isHighlight: true, // global setting to highlight page object
    highlightPageObjectUUID: null,
    highlightSemanticPageUUID: null,
    scroll: {
      action: 'scroll',
      selectedSemanticDocumentUUID: null,
      selectedSemanticPageUUID: null
    },
    loadedImg: {
      '123': true
    },
    visibleDocs: {
      '4b2299ee-6f31-465c-b2f5-68631bca45de____993e3f85-5562-4869-82d0-f510173c818b':
        {
          isVisible: true,
          visibleValue: 1
        }
    }
  },
  swissFexData: {
    eventHandlers: {
      detailPageNavigationEventCallback: null
    }
  }
};

const mockedUsedNavigate = vi.fn();
const mockedUsedLocation = vi.fn();
const mockedUsedDispatch = vi.fn();

vi.mock('react-router-dom', () => ({
  ...(vi.importActual('react-router-dom') as any),
  useNavigate: () => mockedUsedNavigate,
  useLocation: () => mockedUsedLocation
}));

vi.mock('react-redux', async () => {
  const actualRedux: any = (await vi.importActual('react-redux')) as object;
  return {
    ...actualRedux,
    useSelector: vi.fn().mockImplementation((selector) => selector(mockState)),
    useDispatch: () => mockedUsedDispatch
  };
});

const intersectionObserverMock = () => ({
  observe: () => null,
  unobserve: () => null,
  disconnect: () => null
});
window.IntersectionObserver = vi
  .fn()
  .mockImplementation(intersectionObserverMock);

afterEach(cleanup);

// React Query
const queryClient = new QueryClient();
vi.mock('@tanstack/react-query', async (importOriginal) => {
  const actual: any = (await importOriginal()) as object;
  return {
    ...actual,
    useQuery: vi.fn()
  };
});

describe('should render DocumentsListMaxView/Documents component with empty props', () => {
  const mockQueryData = { enable_semantic_page_image_lazy_loading: true };
  (useQuery as Mock).mockImplementation(() => ({
    data: mockQueryData,
    isLoading: false,
    isError: false
  }));

  test('should render for null dossierData prop', () => {
    const dossierData: IListPagesOfDossiersAndNumberOfDossier[] = null;
    const pageNumber = 0; // 0-indexed
    render(
      <QueryClientProvider client={queryClient}>
        <AccountContextProvider>
          <Documents
            dossiersData={dossierData}
            pageNumber={pageNumber}
            currentPageAnnotationMode={''}
            setCurrentPageAnnotationMode={() => {}}
          />
        </AccountContextProvider>
      </QueryClientProvider>
    );
    const element = screen.queryAllByRole('img');
    expect(element).to.exist;
  });

  test('should render for empty array ([]) dossierData prop', () => {
    const dossierData = [] as IListPagesOfDossiersAndNumberOfDossier[];
    const pageNumber = 0; // 0-indexed
    render(
      <QueryClientProvider client={queryClient}>
        <AccountContextProvider>
          <Documents
            dossiersData={dossierData}
            pageNumber={pageNumber}
            currentPageAnnotationMode={''}
            setCurrentPageAnnotationMode={() => {}}
          />
        </AccountContextProvider>
      </QueryClientProvider>
    );
    const element = screen.queryAllByRole('img');
    expect(element).to.exist;
  });

  test('should render component and match previous Snapshot with dossierData prop', () => {
    const dossierData = [] as IListPagesOfDossiersAndNumberOfDossier[];
    const pageNumber = 0; // 0-indexed
    const tree = rendererCreate(
      <QueryClientProvider client={queryClient}>
        <AccountContextProvider>
          <Documents
            dossiersData={dossierData}
            pageNumber={pageNumber}
            currentPageAnnotationMode={''}
            setCurrentPageAnnotationMode={() => {}}
          />
        </AccountContextProvider>
      </QueryClientProvider>
    ).toJSON();
    expect(tree).toMatchSnapshot('empty dossierData prop');
  });
});

describe('should render DocumentsListMaxView/Documents component with mock dossierData prop (1 page)', () => {
  const mockQueryData = { enable_semantic_page_image_lazy_loading: true };
  (useQuery as Mock).mockImplementation(() => ({
    data: mockQueryData,
    isLoading: false,
    isError: false
  }));

  test('should render for mock dossierData prop', () => {
    const dossierData =
      mockDossierData as IListPagesOfDossiersAndNumberOfDossier[];
    const pageNumber = 0; // 0-indexed
    render(
      <QueryClientProvider client={queryClient}>
        <AccountContextProvider>
          <Documents
            dossiersData={dossierData}
            pageNumber={pageNumber}
            currentPageAnnotationMode={''}
            setCurrentPageAnnotationMode={() => {}}
          />
        </AccountContextProvider>
      </QueryClientProvider>
    );
    const element = screen.queryAllByRole('img');
    expect(element).to.exist;
  });

  test('should render component and match previous Snapshot with mock dossierData prop', () => {
    const dossierData =
      mockDossierData as IListPagesOfDossiersAndNumberOfDossier[];
    const pageNumber = 0; // 0-indexed
    const tree = rendererCreate(
      <QueryClientProvider client={queryClient}>
        <AccountContextProvider>
          <Documents
            dossiersData={dossierData}
            pageNumber={pageNumber}
            currentPageAnnotationMode={''}
            setCurrentPageAnnotationMode={() => {}}
          />
        </AccountContextProvider>
      </QueryClientProvider>
    ).toJSON();
    expect(tree).toMatchSnapshot('1 page dossierData prop');
  });
});

describe('should render DocumentsListMaxView/Documents component with mock dossierData prop (3 pages)', () => {
  const mockQueryData = { enable_semantic_page_image_lazy_loading: true };
  (useQuery as Mock).mockImplementation(() => ({
    data: mockQueryData,
    isLoading: false,
    isError: false
  }));
  test('should render for mock dossierData prop', () => {
    const dossierData = mockDossierData3pages;
    const pageNumber = 0; // 0-indexed
    render(
      <QueryClientProvider client={queryClient}>
        <AccountContextProvider>
          <Documents
            dossiersData={dossierData}
            pageNumber={pageNumber}
            currentPageAnnotationMode={''}
            setCurrentPageAnnotationMode={() => {}}
          />
        </AccountContextProvider>
      </QueryClientProvider>
    );
    const element = screen.queryAllByRole('img');
    expect(element).to.exist;
  });

  test('should render component and match previous Snapshot with mock dossierData prop', () => {
    const dossierData =
      mockDossierData3pages as IListPagesOfDossiersAndNumberOfDossier[];
    const pageNumber = 0; // 0-indexed
    const tree = rendererCreate(
      <QueryClientProvider client={queryClient}>
        <AccountContextProvider>
          <Documents
            dossiersData={dossierData}
            pageNumber={pageNumber}
            currentPageAnnotationMode={''}
            setCurrentPageAnnotationMode={() => {}}
          />
        </AccountContextProvider>
      </QueryClientProvider>
    ).toJSON();
    expect(tree).toMatchSnapshot('3 pages dossierData prop');
  });
});
