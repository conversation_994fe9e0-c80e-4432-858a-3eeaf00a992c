import { render, screen, cleanup } from '@testing-library/react';
import { create as rendererCreate } from 'react-test-renderer';
import { HighlightPageObject } from '../HighlightPageObject';
import type { IHighlightPageObjectProps } from '../HighlightPageObject';

// mocks
import { mockDossier } from './mockDossier';
import { getThemeConfig } from '../../../constants/theme';

const mockState = {
  dossierManager: { activeThemeConfig: getThemeConfig() },
  dossiers: {
    dossiersData: {
      semantic_documents: mockDossier.semantic_documents,
      uuid: mockDossier.uuid
    }
  },
  dossierPageTabView: {
    selectedView: 'data'
  },
  header: {
    imageSize: 'small'
  },
  documents: {
    isHighlight: true, // global setting to highlight page object
    highlightPageObjectUUID: null,
    highlightSemanticPageUUID: null,
    scroll: {
      action: 'scroll',
      selectedSemanticDocumentUUID: null,
      selectedSemanticPageUUID: null
    },
    loadedImg: {
      '123': true
    }
  }
};

const mockedUsedNavigate = vi.fn();
const mockedUsedLocation = vi.fn();
const mockedUsedDispatch = vi.fn();

vi.mock('react-router-dom', () => ({
  ...(vi.importActual('react-router-dom') as any),
  useNavigate: () => mockedUsedNavigate,
  useLocation: () => mockedUsedLocation
}));

vi.mock('react-redux', async () => {
  const actualRedux: any = (await vi.importActual('react-redux')) as object;
  return {
    ...actualRedux,
    useSelector: vi.fn().mockImplementation((selector) => selector(mockState)),
    useDispatch: () => mockedUsedDispatch
  };
});

afterEach(cleanup);

describe('should render HighlightPageObject component with empty imageRef', () => {
  test('should render for empty Bbox', () => {
    const props: IHighlightPageObjectProps = {
      bbox: null,
      marginTop: 0,
      imgLoaded: true,
      imageRef: { current: null },
      rotation_angle: undefined,
      zoomStyles: {},
      isFinHurdle: false
    };
    render(
      <HighlightPageObject
        bbox={props.bbox}
        marginTop={props.marginTop}
        imgLoaded={props.imgLoaded}
        imageRef={props.imageRef}
        rotation_angle={props.rotation_angle}
        zoomStyles={props.zoomStyles}
        isFinHurdle={props.isFinHurdle}
      />
    );
    const element = screen.queryAllByRole('img');
    expect(element).to.exist;
  });

  test('should render for non-empty Bbox', () => {
    const props: IHighlightPageObjectProps = {
      bbox: {
        ref_width: 200,
        ref_height: 100,
        top: 300,
        left: 300,
        right: 400,
        bottom: 400
      },
      marginTop: 0,
      imgLoaded: true,
      imageRef: { current: null },
      rotation_angle: undefined,
      zoomStyles: {},
      isFinHurdle: false
    };
    render(
      <HighlightPageObject
        bbox={props.bbox}
        marginTop={props.marginTop}
        imgLoaded={props.imgLoaded}
        imageRef={props.imageRef}
        rotation_angle={props.rotation_angle}
        zoomStyles={props.zoomStyles}
        isFinHurdle={props.isFinHurdle}
      />
    );
    const element = screen.queryAllByRole('img');
    expect(element).to.exist;
  });

  test('should render component and match previous Snapshot with empty Bbox', () => {
    const props: IHighlightPageObjectProps = {
      bbox: null,
      marginTop: 0,
      imgLoaded: true,
      imageRef: { current: null },
      rotation_angle: undefined,
      zoomStyles: {},
      isFinHurdle: false
    };

    const tree = rendererCreate(
      <HighlightPageObject
        bbox={props.bbox}
        marginTop={props.marginTop}
        imgLoaded={props.imgLoaded}
        imageRef={props.imageRef}
        rotation_angle={props.rotation_angle}
        zoomStyles={props.zoomStyles}
        isFinHurdle={props.isFinHurdle}
      />
    ).toJSON();
    expect(tree).toMatchSnapshot('empty Bbox');
  });

  test('should render component and match previous Snapshot with non-empty Bbox', () => {
    const props: IHighlightPageObjectProps = {
      bbox: {
        ref_width: 200,
        ref_height: 100,
        top: 300,
        left: 300,
        right: 400,
        bottom: 400
      },
      marginTop: 0,
      imgLoaded: true,
      imageRef: { current: null },
      rotation_angle: undefined,
      zoomStyles: {},
      isFinHurdle: false
    };

    const tree = rendererCreate(
      <HighlightPageObject
        bbox={props.bbox}
        marginTop={props.marginTop}
        imgLoaded={props.imgLoaded}
        imageRef={props.imageRef}
        rotation_angle={props.rotation_angle}
        zoomStyles={props.zoomStyles}
        isFinHurdle={props.isFinHurdle}
      />
    ).toJSON();
    expect(tree).toMatchSnapshot('non-empty Bbox');
  });
});

describe('should render HighlightPageObject component with non-empty imageRef', () => {
  test('should render for empty Bbox', () => {
    const props: IHighlightPageObjectProps = {
      bbox: null,
      marginTop: 0,
      imgLoaded: true,
      imageRef: {
        current: {
          getBoundingClientRect: () => ({
            x: 0,
            y: 0,
            height: 100,
            width: 200,
            top: 0,
            right: 200,
            bottom: 100,
            left: 0,
            toJSON: () => ({})
          })
        } as HTMLImageElement
      },
      rotation_angle: undefined,
      zoomStyles: {},
      isFinHurdle: false
    };
    render(
      <HighlightPageObject
        bbox={props.bbox}
        marginTop={props.marginTop}
        imgLoaded={props.imgLoaded}
        imageRef={props.imageRef}
        rotation_angle={props.rotation_angle}
        zoomStyles={props.zoomStyles}
        isFinHurdle={props.isFinHurdle}
      />
    );
    const element = screen.queryAllByRole('img');
    expect(element).to.exist;
  });

  test('should render for non-empty Bbox', () => {
    const props: IHighlightPageObjectProps = {
      bbox: {
        ref_width: 200,
        ref_height: 100,
        top: 300,
        left: 300,
        right: 400,
        bottom: 400
      },
      marginTop: 0,
      imgLoaded: true,
      imageRef: {
        current: {
          getBoundingClientRect: () => ({
            x: 0,
            y: 0,
            height: 100,
            width: 200,
            top: 0,
            right: 200,
            bottom: 100,
            left: 0,
            toJSON: () => ({})
          })
        } as HTMLImageElement
      },
      rotation_angle: undefined,
      zoomStyles: {},
      isFinHurdle: false
    };
    render(
      <HighlightPageObject
        bbox={props.bbox}
        marginTop={props.marginTop}
        imgLoaded={props.imgLoaded}
        imageRef={props.imageRef}
        rotation_angle={props.rotation_angle}
        zoomStyles={props.zoomStyles}
        isFinHurdle={props.isFinHurdle}
      />
    );
    const element = screen.queryAllByRole('img');
    expect(element).to.exist;
  });

  test('should render component and match previous Snapshot with empty Bbox', () => {
    const props: IHighlightPageObjectProps = {
      bbox: null,
      marginTop: 0,
      imgLoaded: true,
      imageRef: {
        current: {
          getBoundingClientRect: () => ({
            x: 0,
            y: 0,
            height: 100,
            width: 200,
            top: 0,
            right: 200,
            bottom: 100,
            left: 0,
            toJSON: () => ({})
          })
        } as HTMLImageElement
      },
      rotation_angle: undefined,
      zoomStyles: {},
      isFinHurdle: false
    };

    const tree = rendererCreate(
      <HighlightPageObject
        bbox={props.bbox}
        marginTop={props.marginTop}
        imgLoaded={props.imgLoaded}
        imageRef={props.imageRef}
        rotation_angle={props.rotation_angle}
        zoomStyles={props.zoomStyles}
        isFinHurdle={props.isFinHurdle}
      />
    ).toJSON();
    expect(tree).toMatchSnapshot('empty Bbox with imageRef');
  });

  test('should render component and match previous Snapshot with non-empty Bbox', () => {
    const props: IHighlightPageObjectProps = {
      bbox: {
        ref_width: 60,
        ref_height: 30,
        top: 50,
        left: 100,
        right: 160, // derive from left + width
        bottom: 80 // derive from top + height
      },
      marginTop: 0,
      imgLoaded: true,
      imageRef: {
        current: {
          getBoundingClientRect: () => ({
            x: 0,
            y: 0,
            height: 100,
            width: 200,
            top: 0,
            right: 200,
            bottom: 100,
            left: 0,
            toJSON: () => ({})
          })
        } as HTMLImageElement
      },
      rotation_angle: undefined,
      zoomStyles: {},
      isFinHurdle: false
    };

    const tree = rendererCreate(
      <HighlightPageObject
        bbox={props.bbox}
        marginTop={props.marginTop}
        imgLoaded={props.imgLoaded}
        imageRef={props.imageRef}
        rotation_angle={props.rotation_angle}
        zoomStyles={props.zoomStyles}
        isFinHurdle={props.isFinHurdle}
      />
    ).toJSON();
    expect(tree).toMatchSnapshot('non-empty Bbox with imageRef');
  });
});
