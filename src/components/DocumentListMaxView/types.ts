import React, { RefObject } from 'react';
import { IDossiersData } from '../../pages/Documents/types';
import { DossierImageProps } from '../DossierImage/types';
import { Bbox } from '../Dossiers/types';
import { IDimensions } from '../../utils/hooks/types';
import { IListPagesOfDossiersAndNumberOfDossier } from '../DocumentListPreview/types';

export interface IInitialPageNumber {
  pageNumber: number;
}

export interface IDocumentListMaxViewProps
  extends IDossiersData,
    IInitialPageNumber {
  currentPageAnnotationMode: string;
  setCurrentPageAnnotationMode: (type: string) => void;
}

export type IDocumentsProps = IDossiersData &
  IInitialPageNumber & {
    currentPageAnnotationMode: string;
    setCurrentPageAnnotationMode: (type: string) => void;
  };

export interface IDocumentProps extends DossierImageProps {
  page?: string;
  index: number;
  semanticPageUUID?: string;
  semanticDocumentUUID?: string;
  isLoadPage: boolean;
  bbox?: Bbox | null;
  rotation_angle?: number | undefined;
  isFinHurdle?: boolean;
  pageNumber?: number;
  isListScrolling?: boolean;
  isItemVisible?: boolean;
  dossierPage: IListPagesOfDossiersAndNumberOfDossier;
  currentPageAnnotationMode: string;
  isThumbnail?: boolean;
  setCurrentPageAnnotationMode: (type: string) => void;
}

export interface IRotateSectionProps {
  dimensions: IDimensions;
  imageRef: RefObject<HTMLImageElement>;
  imgLoaded: boolean;
  semanticPageUUID: string;
  marginTop: number;
  wrapperRef: RefObject<HTMLDivElement>;
  rotatedStyles?: React.CSSProperties;
}

export interface IDefaultRotateData {
  key: number;
  value: number[];
}
