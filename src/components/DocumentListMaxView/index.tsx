import React, { useEffect, useRef } from 'react';

import { WidthRow } from './style';
import { Documents } from './Documents';
import { setZoomOnDocumentDetailPage } from '../../pages/Documents/actions';

import type { IDocumentListMaxViewProps } from './types';
import { useAppDispatch } from '../../store/app/hooks';

const DocumentListMaxView: React.FC<IDocumentListMaxViewProps> = (props) => {
  const documentScrollRef = useRef<HTMLDivElement>(null);

  const dispatch = useAppDispatch();

  useEffect(() => {
    return () => {
      if (documentScrollRef.current) {
        documentScrollRef.current.scrollTo(0, 0);
      }
    };
  }, []);

  // FIXME: Encode Zoom option not as boolean, but as string
  // constants to give the idea of what option is selected
  useEffect(() => {
    dispatch(setZoomOnDocumentDetailPage(false));
  }, []);

  return (
    <WidthRow
      ref={documentScrollRef}
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: 'fit-content'
      }}
      className="hd-override-detail-max-view-inner-wrapper"
    >
      <Documents
        dossiersData={props.dossiersData}
        pageNumber={props.pageNumber}
        currentPageAnnotationMode={props.currentPageAnnotationMode}
        setCurrentPageAnnotationMode={props.setCurrentPageAnnotationMode}
      />
    </WidthRow>
  );
};
export { DocumentListMaxView };
