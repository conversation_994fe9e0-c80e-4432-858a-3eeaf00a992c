import React from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

import pdfWorker from 'pdfjs-dist/build/pdf.worker.min.mjs?url';

// Explicitly tell React-PDF where to load the worker from
pdfjs.GlobalWorkerOptions.workerSrc = pdfWorker;

interface ISearchablePageProps {
  fileUrl: string;
  scale?: number;
  onPdfRenderSuccess?: () => void;
}

const Loading = () => (
  <div className="hd-override-loading-wrapper-Page-PdfJs">Loading...</div>
);
const SearchablePagePdfJs: React.FC<ISearchablePageProps> = ({
  fileUrl,
  scale,
  onPdfRenderSuccess
}) => {
  return (
    <Document file={fileUrl} loading={Loading}>
      <Page pageNumber={1} scale={scale} onRenderSuccess={onPdfRenderSuccess} />
    </Document>
  );
};

export default SearchablePagePdfJs;
