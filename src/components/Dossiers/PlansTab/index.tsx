import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  extractDossiersAggregatedKeys,
  getTopicForSemanticDocument,
  useDossierData
} from '../utils';
import {
  UploadFilesWrapper,
  DescriptionTabPhotos,
  TitleOfTabPhotos
} from '../../Header/UploadFilesSection/style';
import { DossierItem } from '../../DossierItem';
import { ZoomFeature } from '../../Header/ZoomFeature';
import { StyledRow, ZoomFeatureWrapper } from '../style';
import { TopicBarWithDocuments } from '../TopicBarWithDocuments';
import { aggregatedPlanKeys, documentTopics } from '../constant';
import { useSelectedForSemanticPage } from '../../../utils/hooks/useSelectedForSemanticPage';

import {
  ISemanticDocumentWithIndex,
  ITopicWiseSemanticDocuments,
  SemanticDocument
} from '../types';
import type { RootState } from '../../../store/redux/types';
import { useAppSelector } from '../../../store/app/hooks';

const PlansTab: React.FC = () => {
  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );
  const semantic_documents = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.semantic_documents
  );
  const selectedView = useAppSelector(
    (state: RootState) => state.dossierPageTabView.selectedView
  );

  const { t } = useTranslation();

  const { getDossiers } = useDossierData();

  const selectSemanticPage = useSelectedForSemanticPage();

  if (!semantic_documents || !uuid) return <></>;

  const renderDossierItem = (item: ISemanticDocumentWithIndex) => {
    const dossiers = getDossiers(item.document, item.index);
    const dossiersPlans = extractDossiersAggregatedKeys(
      dossiers,
      aggregatedPlanKeys
    );

    if (!dossiersPlans) {
      return <></>; // Skip rendering if no plans in dossiers
    }

    return (
      <StyledRow
        view={selectedView}
        id={item.document.uuid}
        key={item.document.uuid}
      >
        <DossierItem
          index={item.index}
          sectionTitle={item.document.formatted_title}
          dataIsLoad={true}
          isDeleted={item.document.status_deleted}
          semanticDocumentUUID={item.document.uuid}
          loadDocument={() => {}}
          dossiers={dossiersPlans}
          confidence={item.document.confidence_summary}
          selectSemanticPage={selectSemanticPage}
          sectionFilenameData={item.document.title_elements}
          semantic_document={item.document}
          semanticDocumentTitle={item.document.formatted_title}
          access_mode={item.document.access_mode}
        />
      </StyledRow>
    );
  };

  const renderTopicDocuments = (
    topicDocuments: ISemanticDocumentWithIndex[]
  ) => {
    return topicDocuments.map((item) => renderDossierItem(item));
  };

  const combineCategory = (
    semanticDocuments: SemanticDocument[],
    docCategoryName?: string[]
  ) => {
    let filterSemanticDocuments = [] as SemanticDocument[];

    semanticDocuments.forEach((currentValue) => {
      if (docCategoryName?.includes(currentValue.document_category.name)) {
        const isDuplicate = filterSemanticDocuments.some(
          (doc) =>
            doc.document_category.id === currentValue.document_category.id
        );

        if (!isDuplicate) {
          filterSemanticDocuments.push({
            ...currentValue,
            formatted_title: `${t(
              `EDITOR.PLANS.${currentValue.document_category.name}`
            )}`
          });
        } else {
          filterSemanticDocuments = filterSemanticDocuments.map(
            (filterSemanticDocument) => {
              if (
                filterSemanticDocument.document_category.name ===
                currentValue.document_category.name
              ) {
                return {
                  ...filterSemanticDocument,
                  aggregated_objects:
                    filterSemanticDocument.aggregated_objects.concat(
                      currentValue.aggregated_objects
                    ),
                  semantic_pages: filterSemanticDocument.semantic_pages.concat(
                    currentValue.semantic_pages
                  )
                };
              } else {
                return filterSemanticDocument;
              }
            }
          );
        }
      }
    });
    return filterSemanticDocuments;
  };

  const semanticDocuments = useMemo(() => {
    //combine plan categories
    const returnSemanticDocuments = combineCategory(semantic_documents, [
      'PLAN_ANY',
      'PLAN_FLOOR',
      'PLAN_SITUATION',
      'PROPERTY_DOCUMENTATION',
      'SALES_DOCUMENTATION',
      'PROPERTY_VALUATION'
    ]);

    // Initialize the topic wise formatted semantic documents
    const topicWiseDocuments: ITopicWiseSemanticDocuments = {};
    documentTopics.map((topic) => {
      topicWiseDocuments[topic] = [];
    });

    // Put all semantic documents in the topic wise formatted semantic
    // documents data structure
    returnSemanticDocuments.map((item, index) => {
      const currentTopic = getTopicForSemanticDocument(item);
      topicWiseDocuments[currentTopic].push({ document: item, index: index });
    });

    return (
      <>
        {documentTopics.map((topic) => {
          return (
            semantic_documents.length > 0 && (
              <React.Fragment key={topic}>
                <TopicBarWithDocuments
                  selectedView={selectedView}
                  topic={topic}
                  topicWiseDocuments={topicWiseDocuments[topic]}
                  renderTopicDocuments={renderTopicDocuments}
                  dossierUUID={uuid}
                />
              </React.Fragment>
            )
          );
        })}
      </>
    );
  }, [t, semantic_documents, renderTopicDocuments]);

  return (
    <>
      <UploadFilesWrapper>
        <DescriptionTabPhotos>
          <TitleOfTabPhotos>{t('EDITOR.PLANS.TAB_OVERVIEW')}</TitleOfTabPhotos>
          <ZoomFeatureWrapper>
            <ZoomFeature />
          </ZoomFeatureWrapper>
        </DescriptionTabPhotos>
      </UploadFilesWrapper>
      {semanticDocuments}
    </>
  );
};
export { PlansTab };
