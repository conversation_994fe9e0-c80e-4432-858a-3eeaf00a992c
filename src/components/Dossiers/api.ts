import { AxiosPromise, AxiosRequestConfig } from 'axios';

import { api } from '../../api';

import type {
  DocCheckCaseFieldStructure,
  DocCheckDocumentAssignmentDataStructure,
  DocCheckPersons,
  DocCheckProperty,
  IApiChangeDossierWorkStatusResponse,
  IDossierStatus,
  UpadateDocCheckAttributeRequestBody,
  UpdateDocCheckBusinessCaseResponse,
  UpdateDocCheckDocumentAssignmentRequestBody,
  IDocumentProps,
  UpdateDocCheckDocumentAssignmentResponse,
  RecommendedCategoryTranslation
} from './types';
import type { ICreateNewSemanticDocumentData } from './types';
import type { DocCheckCaseStructure, DossierDataStructure } from './types';
import type { IMoveSemanticPagesToNewDocRequestData } from '../DossierSectionIcons/types';
import { dmsClient } from '../../apis/dmsAccountClient.ts';
import {
  CancelablePromise,
  CombinePagesRealEstateRequestBody,
  CreatedSemanticDocument
} from '../../gen/dms';

export const apiFetchDossierData = (
  uuid: string,
  config?: AxiosRequestConfig
): AxiosPromise<DossierDataStructure> =>
  api.get(`/api/dossier/${uuid}/data_v2`, config);

export const apiAddOriginalFileToDossier = (
  uuid: string,
  formData: FormData,
  config?: AxiosRequestConfig,
  isDuplicate?: boolean
): AxiosPromise<DossierDataStructure> =>
  api.post(
    `/api/dossier/${uuid}/original_files${
      isDuplicate ? '?is_duplicate=true' : ''
    }`,
    formData,
    config
  );

export const apiCreateSemanticDocument = (
  dossier_uuid: string,
  data: ICreateNewSemanticDocumentData,
  config?: AxiosRequestConfig
): AxiosPromise<DossierDataStructure> =>
  api.post(`/api/semantic_documents/${dossier_uuid}`, data, config);

export const apiChangeDossierProperties = (
  uuid: string,
  name: string,
  dossierExpireDate?: string,
  config?: AxiosRequestConfig,
  ownerUsername?: string,
  selectedBusinessCaseTypeUuid?: string,
  dossierRole?: string[]
): AxiosPromise<DossierDataStructure> =>
  api.patch(
    `/api/dossier/${uuid}/change_dossier_properties`,
    {
      name,
      expiry_date: dossierExpireDate,
      owner_username: ownerUsername,
      businesscase_type_id: selectedBusinessCaseTypeUuid,
      dossier_role: dossierRole.toString()
    },
    config
  );

export const apiCombinePropertyPhotos = (
  dossier_uuid: string,
  applicable_pages: IMoveSemanticPagesToNewDocRequestData[],
  document_category_id: string,
  document_category_name: string,
  photosSorting: boolean,
  config?: AxiosRequestConfig
): AxiosPromise<DossierDataStructure> =>
  api.post(
    `/api/semantic_documents/${dossier_uuid}/semantic-pages/combine?photos_sorting=${photosSorting}`,
    { document_category_id, document_category_name, applicable_pages },
    config
  );

export const apiFetchStateTransitions = (
  dossier_uuid: string,
  config?: AxiosRequestConfig
): AxiosPromise<IDossierStatus[]> =>
  api.get(`/api/dossier/${dossier_uuid}/next-possible-states`, config);

export const apiChangeDossierWorkStatus = (
  dossier_uuid: string,
  workStatusId: string,
  config?: AxiosRequestConfig
): AxiosPromise<IApiChangeDossierWorkStatusResponse> =>
  api.patch(
    `/api/dossier/${dossier_uuid}/change_dossier_work_status`,
    {
      work_status_id: workStatusId
    },
    config
  );

export const apiLoadUpdateDossierBekbBusinessCase = (
  uuid: string,
  business_number: string,
  config?: AxiosRequestConfig
) =>
  api.patch(
    `/api/dossier/${uuid}/update_bekb_business_case`,
    { business_number },
    config
  );

export const apiFetchAssignCollateral = (
  dossier_uuid: string,
  document: IDocumentProps[],
  config?: AxiosRequestConfig
) =>
  api.patch(
    `/api/semantic_documents/${dossier_uuid}/assign_collateral`,
    { document },
    config
  );

export const apiFetchDocCheckCase = (
  case_uuid: string,
  config?: AxiosRequestConfig
): AxiosPromise<DocCheckCaseStructure> =>
  api.get(`/api/doccheck/case/${case_uuid}`, config);

export const apiFetchDocCheckCaseFields = (
  case_uuid: string,
  config?: AxiosRequestConfig
): AxiosPromise<DocCheckCaseFieldStructure[]> =>
  api.get(`/api/doccheck/case/${case_uuid}/fields`, config);

export const apiUpdateDocCheckBusinessCase = (
  case_uuid: string,
  business_case_type: string,
  config?: AxiosRequestConfig
): AxiosPromise<UpdateDocCheckBusinessCaseResponse> =>
  api.put(`/api/doccheck/case/${case_uuid}`, { business_case_type }, config);

export const apiUpdateDocCheckBorrowerData = (
  case_uuid: string,
  person_uuid: string,
  newBorrowerFieldsData: UpadateDocCheckAttributeRequestBody,
  config?: AxiosRequestConfig
): AxiosPromise<DocCheckPersons> =>
  api.put(
    `/api/doccheck/case/${case_uuid}/persons/${person_uuid}`,
    `${JSON.stringify(newBorrowerFieldsData)}`,
    config
  );

export const apiUpdateDocCheckPropertyData = (
  case_uuid: string,
  property_uuid: string,
  newPropertyFieldsData: UpadateDocCheckAttributeRequestBody,
  config?: AxiosRequestConfig
): AxiosPromise<DocCheckProperty> =>
  api.put(
    `/api/doccheck/case/${case_uuid}/property/${property_uuid}`,
    `${JSON.stringify(newPropertyFieldsData)}`,
    config
  );

export const apiAddNewBorrower = (
  case_uuid: string,
  config?: AxiosRequestConfig
): AxiosPromise<DocCheckPersons> =>
  api.post(`/api/doccheck/case/${case_uuid}/persons`, {}, config);

export const apiDeleteDocCheckBorrowerData = (
  case_uuid: string,
  person_uuid: string,
  config?: AxiosRequestConfig
): AxiosPromise<DocCheckPersons> =>
  api.delete(`/api/doccheck/case/${case_uuid}/persons/${person_uuid}`, config);

export const apiFetchDocumentAssignments = (
  case_uuid: string,
  config?: AxiosRequestConfig
): AxiosPromise<DocCheckDocumentAssignmentDataStructure[]> =>
  api.get(`/api/doccheck/case/${case_uuid}/doccheck_assignment`, config);

export const apiUpdateDocumentAssignments = (
  case_uuid: string,
  newDocAssignment: UpdateDocCheckDocumentAssignmentRequestBody,
  config?: AxiosRequestConfig
): AxiosPromise<UpdateDocCheckDocumentAssignmentResponse> =>
  api.post(
    `/api/doccheck/case/${case_uuid}/doccheck_assignment`,
    `${JSON.stringify(newDocAssignment)}`,
    config
  );

export const apiFetchDocCheckAutomaticDocumentAssignments = (
  case_uuid: string,
  config?: AxiosRequestConfig
): AxiosPromise<DocCheckDocumentAssignmentDataStructure[]> =>
  api.get(
    `/api/doccheck/case/${case_uuid}/doccheck_assignment_automatically`,
    config
  );

export const apiDocCheckAssignDocumentsAutomatically = (
  case_uuid: string,
  config?: AxiosRequestConfig
): AxiosPromise<DocCheckDocumentAssignmentDataStructure[]> =>
  api.post(
    `/api/doccheck/case/${case_uuid}/doccheck_assignment_automatically`,
    {},
    config
  );

export const apiFetchRecommendedCategoryTranslations = (
  dossier_uuid: string,
  filter_names?: string[],
  filter_recommendation?: boolean,
  config?: AxiosRequestConfig
): AxiosPromise<RecommendedCategoryTranslation[]> => {
  return filter_names?.length > 0
    ? api.get(
        `/api/dossier/${dossier_uuid}/document-categories?filter_names=${filter_names.toString()}&filter_recommendation=${filter_recommendation}`,
        config
      )
    : api.get(`/api/dossier/${dossier_uuid}/document-categories`, config);
};

export const apiApiCombinePagesRealEstateProperty = (
  dossier_uuid: string,
  payload: CombinePagesRealEstateRequestBody
): CancelablePromise<Array<CreatedSemanticDocument>> => {
  return dmsClient.default.semanticDocumentApiCombinePagesRealEstateProperty(
    dossier_uuid,
    payload
  );
};
