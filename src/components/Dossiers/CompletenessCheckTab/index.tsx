import { Col, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import React, { useEffect, useMemo, useState } from 'react';

import {
  fetchDocCheckCaseAction,
  addNewDocCheckBorrowerAction,
  fetchDocCheckCaseFieldsAction,
  updateDocCheckBusinessCaseAction,
  fetchDocCheckDocumentAssignmentsAction,
  fetchAutomaticDocumentAssignmentsAction
} from '../actions';
import {
  BorrowerEntity,
  PropertyEntity,
  BusinessCaseEntity
} from './constants';
import {
  CompletenessBody,
  TransparentButton,
  CompletenessTopBar,
  AddBorrowerButtonContainer,
  AssignDocumentButtonContainer
} from './styles';
import { Property } from './Property';
import { Borrowers } from './Borrowers';
import { AddBorrowerButtonIcon } from '../../icons/AddBorrowerButtonIcon';
import { AssignDocumentsButtonIcon } from '../../icons/AssignDocumentsButtonIcon';
import { AutomaticDocumentAssignmentModal } from './AutomaticDocumentAssignmentModal';
import { BusinessCaseTypeDropdownComponent } from './BusinessCaseTypeDropdownComponent';

import type {
  DocChechCaseFieldsChoices,
  DocCheckCaseFieldStructure,
  DocCheckPersons
} from '../types';
import type { RootState } from '../../../store/redux/types';
import { ChangeDossierStatusButton } from '../../Header/Buttons/ChangeDossierStatus';
import { IRequestDataToCheckUpdatesForFileSection } from '../../DossierFileStatus/types';
import { checkUpdateFileSectionAction } from '../../DossierFileStatus/actions';
import { useAppDispatch, useAppSelector } from '../../../store/app/hooks';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUserPlus } from '@fortawesome/pro-solid-svg-icons';
import { faLinkHorizontal } from '@fortawesome/pro-light-svg-icons';
import { useAccountContext } from '@components/Account/AccountContextProvider';

const CompletenessCheckTab: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const dossiersData = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData
  );

  const docCheckCaseData = useAppSelector(
    (state: RootState) => state.dossiers.docCheckCaseData
  );

  const docCheckCaseFieldsData = useAppSelector(
    (state: RootState) => state.dossiers.docCheckCaseFieldsData
  );

  const docCheckDocumentAssignmentData = useAppSelector(
    (state: RootState) => state.dossiers.docCheckDocumentAssignmentData
  );

  const { frontend_theme } = useAccountContext();

  const [addBorrowerHover, setAddBorrowerHover] = useState(false);
  const [assignDocumentsHover, setAssignDocumentsHover] = useState(false);
  const [selectedBusinessCaseType, setSelectedBusinessCaseType] =
    useState<DocChechCaseFieldsChoices>(null);
  const [borrowerFieldsData, setBorrowerFieldsData] = useState<
    DocCheckCaseFieldStructure[]
  >([]);
  const [propertyFieldsData, setPropertyFieldsData] = useState<
    DocCheckCaseFieldStructure[]
  >([]);
  const [businessCaseFieldsData, setBusinessCaseFieldsData] = useState<
    DocCheckCaseFieldStructure[]
  >([]);
  const [showAutomaticDocAssignmentModal, setShowAutomaticDocAssignmentModal] =
    useState(false);

  const handleAddBorrowerClick = () => {
    if (docCheckCaseData.persons.length < 5) {
      dispatch(addNewDocCheckBorrowerAction(docCheckCaseData.uuid));
    }
  };

  const handleAssignDocumentsClick = () => {
    setShowAutomaticDocAssignmentModal(true);
  };

  const closeDocAssignModal = () => {
    setShowAutomaticDocAssignmentModal(false);
  };

  const handleBusinesscaseChangeCallback = () => {
    const dataForRequest: IRequestDataToCheckUpdatesForFileSection = {
      original_files: [],
      extracted_files: [],
      exceptions: [],
      note: dossiersData.note,
      name: dossiersData.name,
      businesscase_type_id: selectedBusinessCaseType.uuid
    };

    dispatch(
      checkUpdateFileSectionAction(
        dossiersData.uuid,
        dataForRequest,
        false,
        () => {}
      )
    );
  };

  const handleOnChangeBusinessCaseType = (value: string) => {
    const businessCaseType = businessCaseFieldsData[0].choices.find(
      (item) => item.uuid === value
    );
    setSelectedBusinessCaseType(businessCaseType || null);
    dispatch(
      updateDocCheckBusinessCaseAction(
        docCheckCaseData.uuid,
        businessCaseType.key,
        handleBusinesscaseChangeCallback
      )
    );
  };

  useEffect(() => {
    dispatch(fetchDocCheckCaseAction(dossiersData.doccheck_case_id));
    dispatch(fetchDocCheckCaseFieldsAction(dossiersData.doccheck_case_id));
    dispatch(
      fetchDocCheckDocumentAssignmentsAction(dossiersData.doccheck_case_id)
    );
    dispatch(
      fetchAutomaticDocumentAssignmentsAction(dossiersData.doccheck_case_id)
    );
  }, [dossiersData, dossiersData.businesscase_type_id]);

  useEffect(() => {
    if (businessCaseFieldsData[0] && businessCaseFieldsData[0].choices) {
      setSelectedBusinessCaseType(
        businessCaseFieldsData[0].choices.find(
          (item) => item.key === docCheckCaseData.business_case_type
        )
      );
    }
  }, [docCheckCaseData, businessCaseFieldsData]);

  useEffect(() => {
    if (docCheckCaseData && docCheckCaseFieldsData.length) {
      const borrowerFields: DocCheckCaseFieldStructure[] = [];
      const propertyFields: DocCheckCaseFieldStructure[] = [];
      const businessCaseFields: DocCheckCaseFieldStructure[] = [];

      docCheckCaseFieldsData.map((item) => {
        if (item.entity === BusinessCaseEntity) businessCaseFields.push(item);
        else if (item.entity === BorrowerEntity) borrowerFields.push(item);
        else if (item.entity === PropertyEntity) propertyFields.push(item);
      });

      setBorrowerFieldsData(borrowerFields);
      setPropertyFieldsData(propertyFields);
      setBusinessCaseFieldsData(businessCaseFields);
    }
  }, [docCheckCaseData, docCheckCaseFieldsData]);

  const CompletenessNavBarContent = useMemo(() => {
    if (
      !(
        docCheckCaseData &&
        docCheckCaseData.persons &&
        docCheckCaseData.persons.length < 5
      )
    ) {
      setAddBorrowerHover(false);
    }
    return (
      <>
        <ChangeDossierStatusButton />
        {docCheckCaseData.business_case_type !== 'UNKNOWN' && (
          <>
            {docCheckCaseData &&
              docCheckCaseData.persons &&
              docCheckCaseData.persons.length < 5 && (
                <AddBorrowerButtonContainer>
                  <TransparentButton
                    icon={
                      frontend_theme !== 'LEGACY' ? (
                        <FontAwesomeIcon
                          className=""
                          icon={faUserPlus}
                          style={{
                            height: '18px'
                          }}
                        />
                      ) : (
                        <AddBorrowerButtonIcon hover={addBorrowerHover} />
                      )
                    }
                    onMouseEnter={() => setAddBorrowerHover(true)}
                    onMouseLeave={() => setAddBorrowerHover(false)}
                    onClick={(e) => {
                      e.currentTarget.blur();
                      handleAddBorrowerClick();
                    }}
                  >
                    <span style={{ marginLeft: '2px' }}>
                      {t('COMPLETENESS_CHECK_TEXTS.BORROWER.ADD_BORROWER')}
                    </span>
                  </TransparentButton>
                </AddBorrowerButtonContainer>
              )}
            <AssignDocumentButtonContainer>
              <TransparentButton
                icon={
                  frontend_theme !== 'LEGACY' ? (
                    <FontAwesomeIcon
                      className=""
                      icon={faLinkHorizontal}
                      style={{
                        height: '18px'
                      }}
                    />
                  ) : (
                    <AssignDocumentsButtonIcon hover={assignDocumentsHover} />
                  )
                }
                onMouseEnter={() => setAssignDocumentsHover(true)}
                onMouseLeave={() => setAssignDocumentsHover(false)}
                onClick={(e) => {
                  e.currentTarget.blur();
                  handleAssignDocumentsClick();
                }}
              >
                <span style={{ marginLeft: '2px' }}>
                  {t('COMPLETENESS_CHECK_TEXTS.ASSIGN_DOCUMENTS_AUTO')}
                </span>
              </TransparentButton>
            </AssignDocumentButtonContainer>
          </>
        )}
      </>
    );
  }, [t, docCheckCaseData, addBorrowerHover, assignDocumentsHover]);

  const BusinessCaseDropdown = useMemo(() => {
    return (
      <>
        <BusinessCaseTypeDropdownComponent
          businessCaseFields={businessCaseFieldsData}
          borrowerFieldsData={borrowerFieldsData}
          propertyFieldsData={propertyFieldsData}
          selectedBusinessCaseType={selectedBusinessCaseType}
          docCheckCaseData={docCheckCaseData}
          onChange={handleOnChangeBusinessCaseType}
          access_mode={dossiersData.access_mode}
        />
      </>
    );
  }, [docCheckCaseData, businessCaseFieldsData, selectedBusinessCaseType]);

  const BorrowersDiv = useMemo(() => {
    if (docCheckCaseData && docCheckCaseData.persons) {
      return docCheckCaseData.persons.map((borrower: DocCheckPersons) => {
        return (
          <div key={borrower.uuid}>
            <Borrowers
              borrower={borrower}
              borrowerFields={borrowerFieldsData}
            />
          </div>
        );
      });
    }
  }, [docCheckCaseData, borrowerFieldsData, docCheckDocumentAssignmentData]);

  const PropertyDiv = useMemo(() => {
    return (
      <>
        <Property propertyFields={propertyFieldsData} />
      </>
    );
  }, [docCheckCaseData, propertyFieldsData, docCheckDocumentAssignmentData]);

  return (
    <>
      <Row>
        <Col xs={24}>
          <CompletenessTopBar>{CompletenessNavBarContent}</CompletenessTopBar>
        </Col>
      </Row>

      <Row>
        <Col xs={24}>
          <CompletenessBody>
            {BusinessCaseDropdown}
            {docCheckCaseData.business_case_type !== 'UNKNOWN' && (
              <>
                {BorrowersDiv}
                {PropertyDiv}
              </>
            )}
          </CompletenessBody>
        </Col>
      </Row>

      {showAutomaticDocAssignmentModal && (
        <AutomaticDocumentAssignmentModal
          isModalVisible={showAutomaticDocAssignmentModal}
          closeDocAssignModal={closeDocAssignModal}
        />
      )}
    </>
  );
};

export { CompletenessCheckTab };
