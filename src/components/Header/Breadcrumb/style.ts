import styled from 'styled-components';
import { HYPO_DOSSIER_GREY_PALETTE } from '../../../constants/theme';
import { MIN_WIDTH_TO_DISPLAY_BREADCRUMB_LOGO_AND_MANAGER } from '../../../constants';

const BreadcrumbWrapper = styled.div`
  display: flex;
  align-items: center;
  color: ${HYPO_DOSSIER_GREY_PALETTE.DARK_GREY};

  .animate {
    transition: color 0.15s;
  }

  .wrapper {
    display: flex;
    align-items: center;
    word-break: normal;
  }

  .icon {
    cursor: pointer;
    margin-left: 10px;
  }
  .not-current {
    cursor: pointer;
    font-weight: normal;
    &:after {
      margin: 0 10px;
      content: ' > ';
      text-decoration: none;
      cursor: default;
    }
  }

  .not-current-text {
    cursor: pointer;

    &:hover {
      color: ${(props) => props.theme.colorPrimary};

      &:after {
        color: ${HYPO_DOSSIER_GREY_PALETTE.DARK_GREY};
      }
    }
  }

  .breadcrumb {
    display: flex;
    align-items: center;
  }

  .mr-10 {
    margin-right: 10px;
  }
`;

const BreadcrumbTextWrapper = styled.div`
  font-size: 14px;
`;

const HypoDossierManagerWrapper = styled.div`
  @media (max-width: ${MIN_WIDTH_TO_DISPLAY_BREADCRUMB_LOGO_AND_MANAGER}) {
    display: none; /* Hide on smaller screens */
  }
`;

export { BreadcrumbWrapper, BreadcrumbTextWrapper, HypoDossierManagerWrapper };
