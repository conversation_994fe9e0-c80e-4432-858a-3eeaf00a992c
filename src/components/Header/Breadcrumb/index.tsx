import React, { useCallback, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import {
  DOSSIER_VIEWS_URL,
  MANAGER_URL,
  queryParamPageNumber,
  ROUTES,
  SELECTED_VIEW_TO_ROUTES_KEYS
} from '../../../constants/routes';
import { parseGetQueryParam } from '../../../utils';
import { useRedirectToDossierManager } from '../../../utils/hooks/useRedirectToDossierManager';
import { DossierItemPropertiesModal } from '../../ListOfDossier/DossierItemProperties/DossierItemPropertiesModal';
import { BreadcrumbDetailDocumentPage } from './BreadcrumbDetailDocumentPage';
import { BreadcrumbItem } from './BreadcrumbItem';
import {
  BreadcrumbTextWrapper,
  <PERSON><PERSON><PERSON><PERSON>bWrapper,
  HypoDossierManagerWrapper
} from './style';

import { useTranslation } from 'react-i18next';
import {
  BASE_URL,
  KEYCLOAK_DOSSIER_MANAGER_ROLE_KEY
} from '../../../constants';
import { useAppSelector } from '../../../store/app/hooks';
import type { RootState } from '../../../store/redux/types';
import { useAccountContext } from '../../Account/AccountContextProvider.tsx';
import {
  convertToIDossierRole,
  useAvailableRoles
} from '../../Account/queries';
import { DossierRoleAssigneeKey } from '../../DossierListFilter/constants';
import type { IDossierRole } from '../../Dossiers/types';
import type { PropertiesModalData } from '../../ListOfDossier/types';
import { IBreadcrumbProps, NavigationStrategy } from '../types';
import { BreadcrumbLockIcon } from './BreadcrumbLockIcon';
import type { ICheckActiveURL } from './types';
const Breadcrumb: React.FC<IBreadcrumbProps> = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { i18n } = useTranslation();
  const { onClickRedirectToDossierManager } = useRedirectToDossierManager();

  const dossiersData = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData
  );
  const prevView = useAppSelector(
    (state: RootState) => state.dossierEditor.prevView
  );
  const semanticDocumentUUID = useAppSelector(
    (state: RootState) => state.semanticDocument.semanticDocumentUUID
  );
  const dossierAccessMode = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.access_mode
  );

  const { navigation_strategy } = useAccountContext();

  const [currentDossierRoles, setCurrentDossierRoles] = useState<
    IDossierRole[]
  >([]);
  // Info: Following boolean represents the Dossier Manager role form Keycloak
  const [isDossierManagerRole, setDossierManagerRole] = useState(false);

  const availableRoles = useAvailableRoles();
  const dossierRoles = (availableRoles.data?.available_dossier_roles || []).map(
    convertToIDossierRole
  );

  useEffect(() => {
    if (dossiersData && dossiersData.role && dossierRoles?.length) {
      const roles: IDossierRole[] = [];
      dossiersData?.role?.map((dossierRole) => {
        dossierRoles?.map((role) => {
          if (role.uuid === dossierRole) {
            roles.push(role);
          }
        });
      });
      setCurrentDossierRoles(roles);
    }
  }, [dossiersData]);

  const queryPageNumber = parseGetQueryParam(queryParamPageNumber);

  const [propertiesData, setPropertiesData] = useState<PropertiesModalData>({
    name: dossiersData.name || '',
    lang: dossiersData.lang || '',
    created: dossiersData.creation || '',
    expirationDate: dossiersData.expiry_date,
    maxExpirationDate: dossiersData.max_expiry_date,
    businesscase_type_id: dossiersData.businesscase_type_id,
    uuid: dossiersData.uuid || '',
    access_mode: dossiersData.access_mode,
    dossier_roles: currentDossierRoles,
    dossier_owner: ''
  });
  const [showPropertiesModal, setShowPropertiesModal] = useState(false);

  const isDefaultNavigation =
    navigation_strategy === NavigationStrategy.default;

  useEffect(() => {
    // @ts-ignore
    const keycloakUserRoles = window.keycloak?.tokenParsed?.user_roles;

    if (
      keycloakUserRoles &&
      keycloakUserRoles.length > 0 &&
      keycloakUserRoles.includes(KEYCLOAK_DOSSIER_MANAGER_ROLE_KEY)
    ) {
      setDossierManagerRole(true);
    }
  }, []);

  useEffect(() => {
    if (dossiersData) {
      const getIndex = dossiersData.role_keys?.findIndex(
        (item) => item === DossierRoleAssigneeKey
      );
      const currOwner =
        getIndex !== undefined &&
        dossiersData.role_username &&
        dossiersData.role_username[getIndex]
          ? JSON.parse(JSON.stringify(dossiersData.role_username[getIndex]))
              .username
          : '';

      setPropertiesData({
        name: dossiersData.name || '',
        lang: dossiersData.lang || '',
        created: dossiersData.creation || '',
        expirationDate: dossiersData.expiry_date,
        maxExpirationDate: dossiersData.max_expiry_date,
        businesscase_type_id: dossiersData.businesscase_type_id,
        uuid: dossiersData.uuid || '',
        access_mode: dossiersData.access_mode,
        dossier_roles: currentDossierRoles,
        dossier_owner: currOwner
      });
    }
  }, [dossiersData]);

  const togglePropertiesModal = () => {
    setShowPropertiesModal((prev) => !prev);
  };

  // FIXME: useCallback as it then used in component's props
  const updatePropertiesLocalData = (
    newPropertiesData: PropertiesModalData
  ) => {
    const properties = propertiesData;
    if (propertiesData.uuid === newPropertiesData.uuid) {
      propertiesData.name = newPropertiesData.name;
      propertiesData.lang = newPropertiesData.lang;
      propertiesData.created = newPropertiesData.created;
      propertiesData.expirationDate = newPropertiesData.expirationDate;
      propertiesData.maxExpirationDate = newPropertiesData.maxExpirationDate;
      propertiesData.businesscase_type_id =
        newPropertiesData.businesscase_type_id;
      propertiesData.dossier_roles = newPropertiesData.dossier_roles;
      propertiesData.dossier_owner = newPropertiesData.dossier_owner;
    }
    setPropertiesData(properties);
  };

  const checkActive = useCallback(
    (urls: ICheckActiveURL, splitId = false) => {
      if (splitId) {
        const newPath = pathname?.split('/').splice(3, 3).join('/');
        return Object.keys(urls).some((item) => newPath?.includes(item));
      }

      return urls[pathname] !== undefined;
    },
    [pathname]
  );

  const handleClickUUID = (event: React.MouseEvent<HTMLElement>) => {
    if (event.ctrlKey || event.button === 1) {
      event.preventDefault();
      event.stopPropagation();
      if (prevView !== null) {
        const url =
          SELECTED_VIEW_TO_ROUTES_KEYS[
            prevView as keyof typeof SELECTED_VIEW_TO_ROUTES_KEYS
          ];
        openInNewTab(ROUTES(i18n.language).dynamic[url](dossiersData.uuid));
      } else {
        openInNewTab(ROUTES(i18n.language).dynamic.pageView(dossiersData.uuid));
      }
    } else {
      if (prevView !== null) {
        const url =
          SELECTED_VIEW_TO_ROUTES_KEYS[
            prevView as keyof typeof SELECTED_VIEW_TO_ROUTES_KEYS
          ];
        navigate(ROUTES(i18n.language).dynamic[url](dossiersData.uuid), {
          replace: true
        });
      } else {
        navigate(ROUTES(i18n.language).dynamic.pageView(dossiersData.uuid), {
          replace: true
        });
      }
    }
  };

  // TODO: move to utils to reuse and reduce code duplication
  const openInNewTab = (url: string) => {
    const win = window.open(url, '_blank');
    if (win != null) {
      win.focus();
    }
  };

  const handleClickLinkDossierManager = (
    event: React.MouseEvent<HTMLElement>
  ) => {
    event.preventDefault();
    event.stopPropagation();
    if (event.ctrlKey || event.button === 1) {
      const queryParams = new URLSearchParams({
        lang: i18n.language
      }).toString();
      openInNewTab(`${BASE_URL}?${queryParams}`);
    } else {
      onClickRedirectToDossierManager();
    }
  };

  const handleCheckActive = useCallback(
    () => checkActive(MANAGER_URL),
    [MANAGER_URL]
  );

  // FIXME: make a function that checks the route to be MANAGER_URL or some other
  const showDossierNameBreadcrumb =
    MANAGER_URL[pathname as keyof typeof MANAGER_URL] === undefined;

  const isReadOnlyMode =
    queryPageNumber === null && dossierAccessMode === 'read_only';

  return (
    <>
      <BreadcrumbWrapper>
        {isDefaultNavigation && (
          <HypoDossierManagerWrapper>
            <BreadcrumbItem checkFunc={handleCheckActive} forceHideIcon={true}>
              <BreadcrumbTextWrapper
                onClick={handleClickLinkDossierManager}
                className={`animate ${
                  checkActive(MANAGER_URL) ? '' : 'not-current-text'
                }`}
                style={{ width: 'max-content' }}
              >
                HypoDossier Manager
              </BreadcrumbTextWrapper>
            </BreadcrumbItem>
          </HypoDossierManagerWrapper>
        )}
        {showDossierNameBreadcrumb && (
          <BreadcrumbItem
            checkFunc={() => checkActive(DOSSIER_VIEWS_URL, true)}
            handleClickIcon={togglePropertiesModal}
            additionalClassName={'wrapper'}
          >
            <BreadcrumbTextWrapper
              className={
                checkActive(DOSSIER_VIEWS_URL, true) ? '' : 'not-current-text'
              }
              onClick={handleClickUUID}
            >
              {dossiersData.name}
            </BreadcrumbTextWrapper>
            {
              isReadOnlyMode && <BreadcrumbLockIcon /> // Lock icon is shown only if the dossier is in read-only mode
            }
          </BreadcrumbItem>
        )}
        {queryPageNumber !== null && semanticDocumentUUID && (
          <BreadcrumbDetailDocumentPage checkActive={checkActive} />
        )}
      </BreadcrumbWrapper>
      {propertiesData && showPropertiesModal && (
        <DossierItemPropertiesModal
          dataToRender={propertiesData}
          isModalVisible={showPropertiesModal}
          handleClose={togglePropertiesModal}
          isDossiersDashboard={false}
          updatePropertiesLocalData={updatePropertiesLocalData}
        />
      )}
    </>
  );
};

export { Breadcrumb };
