import { countCombinationsAndPages } from '../utils';
import { RealEstatePropertyAssignmentCombinePages } from '../../../../../gen/dms';

describe('countCombinationsAndPages', () => {
  test('should count combinations and semantic pages', () => {
    const data: Record<
      string,
      Array<RealEstatePropertyAssignmentCombinePages>
    > = {
      unassigned: [
        {
          real_estate_property_key: 'unassigned',
          semantic_document_uuid: '4a2c4d9d-f446-4997-9053-dfebf1aba995',
          semantic_pages_uuid: ['fc4554ef-e139-4fa8-a697-13219c9f4ad0']
        },
        {
          real_estate_property_key: 'unassigned',
          semantic_document_uuid: 'cc36f234-e5f4-4617-8d41-cc99a2c536b8',
          semantic_pages_uuid: ['4aa9ec50-e979-4bbd-b3c7-13f2ce9f1f33']
        }
      ]
    };

    const result = countCombinationsAndPages(data);
    expect(result).toEqual({ combinationCount: 2, semanticPageCount: 2 });
  });

  test('should handle empty input', () => {
    const result = countCombinationsAndPages({});
    expect(result).toEqual({ combinationCount: 0, semanticPageCount: 0 });
  });

  test('should handle multiple categories', () => {
    const data: Record<
      string,
      Array<RealEstatePropertyAssignmentCombinePages>
    > = {
      assigned: [
        {
          real_estate_property_key: 'assigned',
          semantic_document_uuid: 'abcd',
          semantic_pages_uuid: []
        },
        {
          real_estate_property_key: 'assigned',
          semantic_document_uuid: 'efgh',
          semantic_pages_uuid: ['ijkl', 'mnop']
        }
      ],
      unassigned: [
        {
          real_estate_property_key: 'unassigned',
          semantic_document_uuid: 'qrst',
          semantic_pages_uuid: []
        }
      ]
    };

    const result = countCombinationsAndPages(data);
    expect(result).toEqual({ combinationCount: 3, semanticPageCount: 2 });
  });

  test('should handle single object in category', () => {
    const data: Record<
      string,
      Array<RealEstatePropertyAssignmentCombinePages>
    > = {
      singleton: [
        {
          real_estate_property_key: 'singleton',
          semantic_document_uuid: 'abcd',
          semantic_pages_uuid: ['abcd', 'efgh', 'ijkl']
        }
      ]
    };

    const result = countCombinationsAndPages(data);
    expect(result).toEqual({ combinationCount: 1, semanticPageCount: 3 });
  });

  test('should return zero counts for empty category', () => {
    const data: Record<
      string,
      Array<RealEstatePropertyAssignmentCombinePages>
    > = {
      empty: []
    };

    const result = countCombinationsAndPages(data);
    expect(result).toEqual({ combinationCount: 0, semanticPageCount: 0 });
  });
});
