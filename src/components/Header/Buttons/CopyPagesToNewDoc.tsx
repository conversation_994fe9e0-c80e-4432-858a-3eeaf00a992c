import React, { useCallback, useState } from 'react';

import { NewDocumentModal } from './NewDocumentModal';
import { useGeneratePayload } from '../../DossierSectionIcons/hooks';
import { copySemanticPagesToNewDoc } from '../../DossierSectionIcons/actions';
import { CopyButton } from './CopyButton';
import {
  clearSelectedDocumentsState,
  setInitDocs
} from '../../DossierImages/DossierPageViewImage/actions';
import { useFilterSelectedDocuments } from '../../../utils/hooks/useFilterSelectedDocuments';

import type { OnDataValidType } from './types';
import type { RootState } from '../../../store/redux/types';
import type { ICopyPagesToNewDocProps } from '../../DossierSectionIcons/types';
import { useAppDispatch, useAppSelector } from '../../../store/app/hooks';
import { useSwissfexDocumentActionPerformedData } from '../../../SwissFex/hooks';

const CopyPagesToNewDoc: React.FC<ICopyPagesToNewDocProps> = (props) => {
  const dispatch = useAppDispatch();
  const { generatePayload } = useGeneratePayload();
  const [showModal, setShowModal] = useState<boolean>(false);

  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );
  const access_mode = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.access_mode
  );
  const selectedDocuments = useAppSelector(
    (state: RootState) => state.pageView.selectedDocuments
  );

  const { setSwissfexRefreshData } = useSwissfexDocumentActionPerformedData();

  const {
    filteredInitDocuments,
    filteredSelectedDocuments,
    currentDossierSelectedDocuments
  } = useFilterSelectedDocuments();

  const handleClick = () => {
    if (
      currentDossierSelectedDocuments?.length === 0 ||
      access_mode === 'read_only'
    ) {
      return;
    }
    if (showModal) {
      setShowModal(false);
    } else {
      setShowModal(true);
    }
  };

  const onDataValid: OnDataValidType = useCallback(
    async (data) => {
      if (access_mode === 'read_only') {
        return;
      }
      if (uuid) {
        const countSelected = currentDossierSelectedDocuments?.length;

        setSwissfexRefreshData();

        try {
          const payload = await generatePayload();
  
          dispatch(
            copySemanticPagesToNewDoc(
              uuid,
              payload,
              data,
              countSelected,
              props.successCallback
            )
          );

          dispatch(setInitDocs(filteredInitDocuments));
          dispatch(clearSelectedDocumentsState(filteredSelectedDocuments));
        } catch (err) {
          console.error('Failed to generate payload', err);
        }
      }
    },
    [uuid, currentDossierSelectedDocuments, access_mode]
  );

  return (
    <>
      <CopyButton handleClick={handleClick} isShowModal={showModal} />
      <NewDocumentModal
        showModal={showModal}
        setShowModal={setShowModal}
        onDataValid={onDataValid}
        showProposal={true}
      />
    </>
  );
};

export { CopyPagesToNewDoc };
