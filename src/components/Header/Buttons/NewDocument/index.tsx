import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  fontSizeForUploadFileIcons,
  marginBottomForNewDocumentPlusIcons
} from '../../UploadFilesSection/contants';
import { TransparentButton } from '../style';
import { CustomPopover } from '../../../../styles';
import { NewDocumentModal } from '../NewDocumentModal';
import { createNewSemanticDocument } from '../../../Dossiers/actions';
import { useStatusShowButtonInHeader } from '../../UploadFilesSection/hooks';

import type { OnDataValidType } from '../types';
import type { RootState } from '../../../../store/redux/types';
import { useAppDispatch, useAppSelector } from '../../../../store/app/hooks';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus } from '@fortawesome/pro-solid-svg-icons';
import { useAccountContext } from '@components/Account/AccountContextProvider';

const NewDocumentButton: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );
  const access_mode = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.access_mode
  );
  const { frontend_theme } = useAccountContext();

  if (!uuid) return;

  const statusShowButton = useStatusShowButtonInHeader();

  const [showModal, setShowModal] = useState<boolean>(false);

  const handleClick = () => {
    if (access_mode === 'read_only') {
      return;
    }
    if (showModal) {
      setShowModal(false);
    } else {
      setShowModal(true);
    }
  };

  const onDataValid: OnDataValidType = (dataToDispatch, callback) => {
    if (access_mode === 'read_only') {
      return;
    }
    dispatch(createNewSemanticDocument(uuid, dataToDispatch, callback));
  };

  return statusShowButton ? (
    <>
      <CustomPopover
        overlayClassName="hd-custom-ant-component hd-custom-ant-popover"
        placement="top"
        content={t('EDITOR.HEADER.NEW_DOCUMENT_HELP')}
        mouseEnterDelay={0.5}
        mouseLeaveDelay={0.5}
      >
        <TransparentButton
          icon={
            frontend_theme !== 'LEGACY' ? (
              <FontAwesomeIcon
                className="hd-override-new-document-button-icon"
                icon={faPlus}
                style={{
                  height: '17px'
                }}
              />
            ) : (
              <div
                style={{
                  fontSize: fontSizeForUploadFileIcons,
                  marginBottom: marginBottomForNewDocumentPlusIcons
                }}
                className={'hd-anticon hd-override-new-document-button-icon'}
              >
                +
              </div>
            )
          }
          onClick={handleClick}
        >
          <span style={{ marginLeft: '4px' }}>{t('NEW_DOCUMENT')}</span>
        </TransparentButton>
      </CustomPopover>
      {access_mode !== 'read_only' && (
        <NewDocumentModal
          showModal={showModal}
          setShowModal={setShowModal}
          onDataValid={onDataValid}
        />
      )}
    </>
  ) : null;
};

export { NewDocumentButton };
