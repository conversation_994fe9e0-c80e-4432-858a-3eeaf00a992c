import React, { useCallback, useState } from 'react';

import { MoveButton } from './MoveButton';
import { NewDocumentModal } from './NewDocumentModal';
import { OldDocumentModal } from './OldDocumentModal';
import {
  clearSelectedDocumentsState,
  setInitDocs
} from '../../DossierImages/DossierPageViewImage/actions';
import { useGeneratePayload } from '../../DossierSectionIcons/hooks';
import { moveSemanticPagesToNewDoc } from '../../DossierSectionIcons/actions';
import { useFilterSelectedDocuments } from '../../../utils/hooks/useFilterSelectedDocuments';

import type { OnDataValidType } from './types';
import type { RootState } from '../../../store/redux/types';
import type { IMovePagesToNewDocProps } from '../../DossierSectionIcons/types';
import { useAppDispatch, useAppSelector } from '../../../store/app/hooks';
import { useSwissfexDocumentActionPerformedData } from '../../../SwissFex/hooks';

const MovePagesToNewDoc: React.FC<IMovePagesToNewDocProps> = (props) => {
  const dispatch = useAppDispatch();
  const { generatePayload } = useGeneratePayload();
  const [showMoveToNewDocumentModal, setShowMoveToNewDocumentModal] =
    useState<boolean>(false);
  const [showMoveToOldDocumentModal, setShowMoveToOldDocumentModal] =
    useState<boolean>(false);

  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );
  const access_mode = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.access_mode
  );
  const selectedDocuments = useAppSelector(
    (state: RootState) => state.pageView.selectedDocuments
  );

  const { setSwissfexRefreshData } = useSwissfexDocumentActionPerformedData();

  const handleMoveToNewDocModalVisibility = () => {
    if (selectedDocuments.length === 0 || access_mode === 'read_only') {
      return;
    }
    if (showMoveToNewDocumentModal) {
      setShowMoveToNewDocumentModal(false);
    } else {
      setShowMoveToNewDocumentModal(true);
    }
  };

  const handleMoveToOldDocModalVisibility = () => {
    if (selectedDocuments.length === 0 || access_mode === 'read_only') {
      return;
    }
    if (showMoveToOldDocumentModal) {
      setShowMoveToOldDocumentModal(false);
    } else {
      setShowMoveToOldDocumentModal(true);
    }
  };

  const {
    filteredInitDocuments,
    filteredSelectedDocuments,
    currentDossierSelectedDocuments
  } = useFilterSelectedDocuments();

  const onDataValid: OnDataValidType = useCallback(
    async (data) => {
      if (access_mode === 'read_only') {
        return;
      }
      if (uuid) {
        const countSelected = currentDossierSelectedDocuments?.length;
  
        setSwissfexRefreshData();
  
        const payload = await generatePayload(); // Wait for page_objects 
  
        dispatch(
          moveSemanticPagesToNewDoc(
            uuid,
            payload,
            data,
            countSelected,
            props.successCallback
          )
        );
  
        dispatch(setInitDocs(filteredInitDocuments));
        dispatch(clearSelectedDocumentsState(filteredSelectedDocuments));
      }
    },
    [
      uuid,
      selectedDocuments,
      access_mode
    ]
  );

  return (
    <>
      <MoveButton
        selectedDocuments={props.selectedDocuments}
        handleMoveToNewDocModalVisibility={handleMoveToNewDocModalVisibility}
        handleMoveToOldDocModalVisibility={handleMoveToOldDocModalVisibility}
      />
      {showMoveToNewDocumentModal && (
        <NewDocumentModal
          showModal={showMoveToNewDocumentModal}
          setShowModal={setShowMoveToNewDocumentModal}
          onDataValid={onDataValid}
          showProposal={true}
        />
      )}
      {showMoveToOldDocumentModal && (
        <OldDocumentModal
          showModal={showMoveToOldDocumentModal}
          setShowModal={setShowMoveToOldDocumentModal}
          showProposal={true}
          selectedDocuments={props.selectedDocuments}
        />
      )}
    </>
  );
};

export { MovePagesToNewDoc };
