// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`should render Header/Breadcrumb component for /dashboard > should render component and match previous Snapshot for /dashboard > for /dashboard 1`] = `
<div
  className="sc-fUEImY bVdttG"
>
  <div
    className="sc-DvaoS kugHsr"
  >
    <div
      className="animate breadcrumb  "
    >
      <div
        className="sc-klYcpz cArnuD animate "
        onClick={[Function]}
        style={
          {
            "width": "max-content",
          }
        }
      >
        HypoDossier Manager
      </div>
    </div>
  </div>
</div>
`;

exports[`should render Header/Breadcrumb component for /dossier/254e93ec-c0f2-4133-be04-24170c60e650/view/page?lang=en > should render component and match previous Snapshot for /dossier/254e93ec-c0f2-4133-be04-24170c60e650/view/page > for /dossier/254e93ec-c0f2-4133-be04-24170c60e650/view/page 1`] = `
<div
  className="sc-fUEImY bVdttG"
>
  <div
    className="sc-DvaoS kugHsr"
  >
    <div
      className="animate breadcrumb not-current "
    >
      <div
        className="sc-klYcpz cArnuD animate not-current-text"
        onClick={[Function]}
        style={
          {
            "width": "max-content",
          }
        }
      >
        HypoDossier Manager
      </div>
    </div>
  </div>
  <div
    className="animate breadcrumb  wrapper"
  >
    <div
      className="sc-klYcpz cArnuD"
      onClick={[Function]}
    >
      Detailed Dossier Name Mock
    </div>
  </div>
</div>
`;
