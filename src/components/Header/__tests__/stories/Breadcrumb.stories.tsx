import type { Meta, StoryObj } from '@storybook/react';
import { Breadcrumb } from '../../Breadcrumb/index';
import { withAccountContext, withMemoryRouter, withQueryClient, withReduxStore } from './decorators';
import { accountHandler } from './handlers';


const meta: Meta<typeof Breadcrumb> = {
  title: 'Dmf/Components/Header/Breadcrumb',
  component: Breadcrumb,
  parameters: {
    msw: {
      handlers: [accountHandler]
    },
  }  
};

export default meta;
type Story = StoryObj<typeof Breadcrumb>;

export const ShortTitle: Story = {
  decorators: [
    withAccountContext,
    withMemoryRouter,
    withQueryClient,
    withReduxStore('Short Title')
  ],
};

export const MediumTitle: Story = {
  decorators: [
    withAccountContext,
    withMemoryRouter,
    withQueryClient,
    withReduxStore('Medium Length Document Title That May Start to Overflow')
  ],

};

export const LongRealTitle: Story = {
  decorators: [
     
    withAccountContext,
    withMemoryRouter,
    withQueryClient,
    withReduxStore('240 Betreibungsauskunft Bucher Berta Keine Betreibungen 2024-04-05')
  ],
};

export const LongTitle: Story = {
  decorators: [     
    withAccountContext,
    withMemoryRouter,
    withQueryClient,
    withReduxStore('Very Long Document Title That Will Definitely Overflow and Need Ellipsis Truncation in the Breadcrumb Navigation Component')
  ],
};

export const WithSpecialCharacters: Story = {
  decorators: [
    withAccountContext,
    withMemoryRouter,
    withQueryClient,
    withReduxStore('Document with Special Characters: !@#$%^&*()_+-=[]{}|;:,.<>?')
  ],
}; 

export const WithLockedIcon: Story = {
  decorators: [
    withAccountContext,
    withMemoryRouter,
    withQueryClient,
    withReduxStore('240 Betreibungsauskunft Bucher Berta Keine Betreibungen 2024-04-05', true)
  ],
}; 
