import type { <PERSON>a, StoryObj } from '@storybook/react';
import { SharedHeader } from '../../SharedHeader/index';

import {
  withAccountContext,
  withHypodossierAntdTheme,
  withMemoryRouter,
  withQueryClient,
  withMultiTenancyContext, withReduxStore
} from './decorators';
import { accountHandler, multiTenancyHandlers } from './handlers';


const meta: Meta<typeof SharedHeader> = {
  title: 'Dmf/Components/Header/SharedHeader',
  component: SharedHeader,
  parameters: {
    msw: {
      handlers: [
        ...accountHandler,
        ...multiTenancyHandlers
      ]
    }
  }
};
export default meta;

type Story = StoryObj<typeof SharedHeader>;

const handleLogout = () => {};
const email = '<EMAIL>';

export const NotDetailedShortTitle: Story = {
  render: () => (
    <SharedHeader
      email={email}
      handleLogout={handleLogout}
      isDocumentDetailPage={false}
      isDossierPage={true}
    />
  ),
  decorators: [
    withHypodossierAntdTheme,
    withAccountContext,
    withMultiTenancyContext,
    withReduxStore('Short Title'),
    withMemoryRouter,
    withQueryClient
  ]
};

export const NotDetailedLongTitle: Story = {
  render: () => (
    <SharedHeader
      email={email}
      handleLogout={handleLogout}
      isDocumentDetailPage={false}
      isDossierPage={true}
    />
  ),
  decorators: [
    withHypodossierAntdTheme,
    withAccountContext,
    withMultiTenancyContext,
    withReduxStore('Very Long Document Title That Will Definitely Overflow and Need Ellipsis Truncation in the Breadcrumb Navigation Component'),
    withMemoryRouter,
    withQueryClient
  ]
};

export const NotDetailedLongTitleWithLockIcon: Story = {
  render: () => (
    <SharedHeader
      email={email}
      handleLogout={handleLogout}
      isDocumentDetailPage={false}
      isDossierPage={true}
    />
  ),
  decorators: [
    withHypodossierAntdTheme,
    withAccountContext,
    withMultiTenancyContext,
    withReduxStore('Very Long Document Title That Will Definitely Overflow and Need Ellipsis Truncation in the Breadcrumb Navigation Component', true),
    withMemoryRouter,
    withQueryClient
  ]
};

export const DetailedShortTitle: Story = {
  render: () => (
    <SharedHeader
      email={email}
      handleLogout={handleLogout}
      isDocumentDetailPage={true}
      isDossierPage={true}
    />
  ),
  decorators: [
    withHypodossierAntdTheme,
    withAccountContext,
    withMultiTenancyContext,
    withReduxStore('Short Title'),
    withMemoryRouter,
    withQueryClient
  ]
};

export const DetailedLongTitle: Story = {
  render: () => (
    <SharedHeader
      email={email}
      handleLogout={handleLogout}
      isDocumentDetailPage={true}
      isDossierPage={true}
    />
  ),
  decorators: [
    withHypodossierAntdTheme,
    withAccountContext,
    withMultiTenancyContext,
    withReduxStore('Very Long Document Title That Will Definitely Overflow and Need Ellipsis Truncation in the Breadcrumb Navigation Component'),
    withMemoryRouter,
    withQueryClient
  ]
}; 

export const DetailedLongTitleWithLockIcon: Story = {
  render: () => (
    <SharedHeader
      email={email}
      handleLogout={handleLogout}
      isDocumentDetailPage={true}
      isDossierPage={true}
    />
  ),
  decorators: [
    withHypodossierAntdTheme,
    withAccountContext,
    withMultiTenancyContext,
    withReduxStore('Very Long Document Title That Will Definitely Overflow and Need Ellipsis Truncation in the Breadcrumb Navigation Component', true),
    withMemoryRouter,
    withQueryClient
  ]
}; 
