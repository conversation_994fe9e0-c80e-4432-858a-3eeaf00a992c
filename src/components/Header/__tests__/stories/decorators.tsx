import { IMultiTenancyContext } from '@components/Auth/types';
import { jwtWithBothClaims } from '@components/Header/SharedHeader/InfoButton/__tests__/stories/mockData';
import { configureStore } from '@reduxjs/toolkit';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { createContext } from 'react';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import { AccountContextProvider } from '../../../Account/AccountContextProvider';

import { ThemeConfigurator } from '../../../../ThemeConfigurator.tsx';
import { getThemeConfig } from '../../../../constants/theme';

const withMemoryRouter = (Story: any) => (
  <MemoryRouter initialEntries={['/dossiers/view/123']}>
    <Story />
  </MemoryRouter>
);

const createMockStore = (title: string, readOnly?: boolean) => {
  return configureStore({
    reducer: {
      dossierManager: () => ({
        activeThemeConfig: getThemeConfig()
      }),
      header: () => ({
        authToken: jwtWithBothClaims,
        accountGrantToken: jwtWithBothClaims,
        statusDossierSettings: false,
        showDeletedSemanticDocuments: false,
        logoutURL: /logout/
      }),
      dossiers: () => ({
        dossiersData: {
          name: title,
          uuid: '123',
          access_mode: readOnly ? 'read_only' : 'read_write',
          role: ['role1'],
          role_username: [{ username: 'testUser' }],
          role_keys: ['assignee'],
          semanticDocumentUUID: '123'
        }
      }),
      dossierEditor: () => ({
        prevView: null
      }),
      semanticDocument: () => ({
        semanticDocumentUUID: null
      }),
      accessDelegation: () => ({
        permission: true
      })
    }
  });
};

const withReduxStore = (title: string, readOnly?: boolean) => (Story: any) => (
  <Provider store={createMockStore(title, readOnly)}>
    <Story />
  </Provider>
);

const queryClient = new QueryClient({
  defaultOptions: { queries: { retry: false } }
});

const withQueryClient = (Story: any) => (
  <QueryClientProvider client={queryClient}>
    <Story />
  </QueryClientProvider>
);

const withAccountContext = (Story: any) => (
  <AccountContextProvider>
    <Story />
  </AccountContextProvider>
);

// Mock context for Storybook
const MockMultiTenancyContext = createContext<IMultiTenancyContext>({
  selectedAccountKey: 'mock-account-key',
  selectedAccountName: 'Mock Account',
  instance_access_token: 'mock-token',
  accounts: [
    {
      accountName: 'Mock Account',
      accountKey: 'mock-account-key',
      switchAccountCallback: () => {}
    }
  ]
});

const MockMultiTenancyContextProvider = ({
  children
}: {
  children: React.ReactNode;
}) => (
  <MockMultiTenancyContext.Provider
    value={{
      selectedAccountKey: 'mock-account-key',
      selectedAccountName: 'Mock Account',
      instance_access_token: 'mock-token',
      accounts: [
        {
          accountName: 'Mock Account',
          accountKey: 'mock-account-key',
          switchAccountCallback: () => {}
        }
      ]
    }}
  >
    {children}
  </MockMultiTenancyContext.Provider>
);

const withMultiTenancyContext = (Story: any) => (
  <MockMultiTenancyContextProvider>
    <Story />
  </MockMultiTenancyContextProvider>
);

const withHypodossierAntdTheme = (Story: any) => (
  <ThemeConfigurator>
    <div className="hd-single-page-application">
      <Story />
    </div>
  </ThemeConfigurator>
);

export {
  withAccountContext,
  withHypodossierAntdTheme,
  withMemoryRouter,
  withMultiTenancyContext,
  withQueryClient,
  withReduxStore
};
