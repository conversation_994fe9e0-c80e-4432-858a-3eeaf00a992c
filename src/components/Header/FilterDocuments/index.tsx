import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Menu, Space } from 'antd';

import { CustomButton } from './style';
import { setDocumentFilterAction } from './actions';

import { RootState } from '../../../store/redux/types';
import { MenuItem, Semantic_Document_Filer } from './types';
import { useAppDispatch, useAppSelector } from '../../../store/app/hooks';

const FilterDocuments: React.FC = () => {
  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );
  const documentFilterList = useAppSelector(
    (state: RootState) => state.documentFilter.documentFilterList
  );

  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  const items: MenuItem[] = useMemo(
    () =>
      Semantic_Document_Filer.map((label, index) => ({
        key: `${index + 1}`,
        label: t(`DOCUMENT_FILTER.${label}`)
      })),
    [Semantic_Document_Filer, t]
  );
  const [selectedItemKey, setSelectedItemKey] = useState<string>(items[0].key);

  useEffect(() => {
    // Update selectedItemKey if the current selectedItemKey is not in the updated items
    if (!items.find((item) => item.key === selectedItemKey)) {
      setSelectedItemKey(items[0].key);
    }
  }, [items, selectedItemKey]);

  const selectedFilterFromGlobal = useMemo(
    () => documentFilterList.find((filter) => filter.dossier_uuid === uuid),
    [uuid, documentFilterList]
  );

  const selectedItem = items.find((item) => item.key === selectedItemKey);
  const selectedItemLabel = selectedItem ? selectedItem.label : '';
  const selectedItemLabelFinal = selectedFilterFromGlobal
    ? t(`DOCUMENT_FILTER.${selectedFilterFromGlobal.selected_filter}`)
    : selectedItemLabel;

  const handleMenuItemClick = (key: string) => {
    setSelectedItemKey(key);
    dispatch(
      setDocumentFilterAction({
        dossier_uuid: uuid,
        selected_filter: Semantic_Document_Filer[parseInt(key) - 1]
      })
    );
  };

  const menu = (
    <Menu
      onClick={({ key }) => handleMenuItemClick(key)}
      style={{ borderRadius: '10px' }}
    >
      {items.map((item) => (
        <Menu.Item
          key={item.key}
          style={{
            fontWeight:
              selectedItemLabelFinal === item.label ? 'bold' : 'normal'
          }}
        >
          {item.label}
        </Menu.Item>
      ))}
    </Menu>
  );

  // WARNING_INFO: the overlay attribute is being displayed as depriciated
  // but There is no documentation available online explaining the
  // methods to replace it with the newer implementation. Nor does
  // the documentation states that overlay is depriciated.
  // Warning only appears locally, and not on staging.

  return (
    <div style={{ marginRight: '20px' }}>
      <Space direction="vertical">
        <Dropdown overlay={menu} placement="bottomLeft">
          <Space wrap>
            <CustomButton>
              <span style={{ color: 'rgb(77, 149, 203)' }}>
                {selectedItemLabelFinal}

                <DownOutlined style={{ marginLeft: '10px' }} />
              </span>
            </CustomButton>
          </Space>
        </Dropdown>
      </Space>
    </div>
  );
};

export { FilterDocuments };
