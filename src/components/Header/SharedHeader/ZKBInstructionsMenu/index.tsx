import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  ZKBHandBookUrl,
  ZKBInstructionVideoUrl,
  ZKBIntroVideoUrl
} from './constants';
import { StyledInstructionsMenuRow } from '../StyledInstructionsMenuRow';

import { ZKBInstructionsMenuType } from './types';

const ZKBInstructionsMenu: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div style={{ fontSize: '14px' }}>
      <div style={{ marginBottom: '10px' }}>
        <StyledInstructionsMenuRow
          title={t('INSTRUCTIONS')}
          description={'PDF'}
          targetURL={ZKBHandBookUrl}
          type={ZKBInstructionsMenuType.HANDBOOK}
          isNarrowPopup={false}
        />
      </div>
      <StyledInstructionsMenuRow
        title="Intro / Kurzüberblick"
        description={'1:07'}
        targetURL={ZKBIntroVideoUrl}
        type={ZKBInstructionsMenuType.VIDEO}
        isNarrowPopup={false}
      />
      <StyledInstructionsMenuRow
        title="Gesamter Funktionsumfang"
        description={'8:52'}
        targetURL={ZKBInstructionVideoUrl}
        type={ZKBInstructionsMenuType.VIDEO}
        isNarrowPopup={false}
      />
    </div>
  );
};

export { ZKBInstructionsMenu };
