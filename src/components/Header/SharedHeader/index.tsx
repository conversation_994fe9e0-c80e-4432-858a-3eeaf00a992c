import React from 'react';
import { Col, Row } from 'antd';

import {
  <PERSON>er<PERSON>op<PERSON>rapper,
  HypoDossierIconWrapper,
  <PERSON>rapperFor<PERSON>ogo,
  WrapperForRightSideIcons
} from '../style';
import { InfoButton } from './InfoButton';
import { Breadcrumb } from '../Breadcrumb';
import { CompanyLogo } from '../CompanyLogo';
import { TutorialVideo } from './TutorialVideo';
import { LeftSpan, WrapperForHeaderItems } from '../style';
import { DossierNavigation } from '../../DocumentListHeader/DossierNavigation';
import { UploadFilesDossierButtonsWrapper } from '../UploadFilesSection/DossierButtonsWrapper/UploadFilesDossierButtonsWrapper.tsx';

import type { ISharedHeaderProps } from '../types';

// antd row sizes (24 cols in total)
const sizes = {
  detailPage: {
    breadcrumb: {
      xs: 16,
      lg: 18,
      xl: 20
    },
    icons: {
      xs: 8,
      lg: 6,
      xl: 4
    }
  },
  nonDetailPage: {
    breadcrumb: {
      xs: 12,
      lg: 15,
      xl: 16
    },
    icons: {
      xs: 12,
      lg: 9,
      xl: 8
    }
  }
};

const SharedHeader: React.FC<ISharedHeaderProps> = (props) => {
  // gives wider space for the breadcrumb on detail page
  // and smaller space for the icons on detail page
  const { breadcrumb, icons } = props.isDocumentDetailPage
    ? sizes.detailPage
    : sizes.nonDetailPage;

  return (
    <div>
      <div>
        <HeaderTopWrapper>
          <Row>
            <Col xs={breadcrumb.xs} lg={breadcrumb.lg} xl={breadcrumb.xl}>
              <WrapperForLogo>
                <WrapperForHeaderItems>
                  <HypoDossierIconWrapper>
                    <CompanyLogo />
                  </HypoDossierIconWrapper>
                  <Breadcrumb
                    isDocumentDetailPage={props.isDocumentDetailPage}
                  />
                </WrapperForHeaderItems>
              </WrapperForLogo>
            </Col>
            <Col xs={icons.xs} lg={icons.lg} xl={icons.xl}>
              <WrapperForRightSideIcons
                style={
                  props.isDocumentDetailPage
                    ? { justifyContent: 'space-between' }
                    : {}
                }
              >
                {props.isDossierPage && <UploadFilesDossierButtonsWrapper />}
                {props.isDocumentDetailPage && (
                  <div>
                    <DossierNavigation />
                  </div>
                )}
                <LeftSpan>
                  <InfoButton
                    email={props.email}
                    handleLogout={props.handleLogout}
                  />
                  <TutorialVideo />
                </LeftSpan>
              </WrapperForRightSideIcons>
            </Col>
          </Row>
        </HeaderTopWrapper>
      </div>
    </div>
  );
};

export { SharedHeader };
