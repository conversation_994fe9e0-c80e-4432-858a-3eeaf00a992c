import React from 'react';
import { ISectionFilenameData, SemanticDocument } from '../Dossiers/types';
import { IConfidenceProps, IIsDeletedProps } from '../DossierItem/types';
import { ISemanticDocumentCategoryRecommendationType } from '../DossierSectionIcons/types';
import { IAccessMode } from '../DossierList/types';

export interface ISectionTitle {
  sectionTitle: string;
}

export interface ICountDocumentPages {
  documentPages: number;
}

export interface ISemanticDocumentUUID {
  semanticDocumentUUID: string;
}

export interface ISharedPropsWithStructure {
  showConfidence?: boolean;
  hideEditIcons?: boolean;
}

export interface RecycleBinViewTitleProps
  extends IIsDeletedProps,
    ICountDocumentPages,
    IConfidenceProps,
    ISectionFilenameData,
    ISharedPropsWithStructure {
  semanticDocumentUUID: string;
  semanticDocumentTitle: string;
  isDocumentRowHovered?: boolean;
  recycle_semantic_document?: SemanticDocument;
}

export interface PageViewTitleProps
  extends IIsDeletedProps,
    ISemanticDocumentUUID,
    ICountDocumentPages,
    ISectionFilenameData,
    ISharedPropsWithStructure,
    IConfidenceProps {
  amountOfDataInDocument: number;
  handleClickLink: () => void;
  semanticDocumentUUID: string;
  semanticDocumentTitle: string;
  isDocumentRowHovered?: boolean;
  selectedDocCatFromRecommendations?: ISemanticDocumentCategoryRecommendationType;
  setSelectedDocCatFromRecommendations?: React.Dispatch<
    React.SetStateAction<ISemanticDocumentCategoryRecommendationType>
  >;
  updateStructureDetailDocument?: (newDoc: SemanticDocument) => void;
  access_mode: IAccessMode;
}

export interface DossierCardTitleProps
  extends ISectionFilenameData,
    IIsDeletedProps,
    IConfidenceProps {
  amountOfDataInDocument: number;
  documentPages: number;
  semanticDocumentUUID: string;
  semanticDocumentTitle: string;
  isDocumentRowHovered?: boolean;
  selectedDocCatFromRecommendations?: ISemanticDocumentCategoryRecommendationType;
  setSelectedDocCatFromRecommendations?: React.Dispatch<
    React.SetStateAction<ISemanticDocumentCategoryRecommendationType>
  >;
  updateStructureDetailDocument?: (newDoc: SemanticDocument) => void;
  access_mode: IAccessMode;
  recycle_semantic_document?: SemanticDocument;
  showSplitAndRenameActions?: boolean;
}

export interface DataViewTitleProps
  extends ICountDocumentPages,
    IConfidenceProps,
    IIsDeletedProps,
    ISectionFilenameData,
    ISharedPropsWithStructure {
  semanticDocumentUUID: string;
  semanticDocumentTitle: string;
  isDocumentRowHovered?: boolean;
  access_mode: IAccessMode;
}

export interface CompactViewTitleProps
  extends ISectionTitle,
    IConfidenceProps,
    ISectionFilenameData {
  documentPages: number;
  semanticDocumentUUID: string;
  amountOfDataInDocument: number;
  handleClickLink: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
}

export interface DocumentTitleProps extends ISectionFilenameData {
  onClick?: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  redColorTitle: boolean;
  semanticDocumentUUID: string;
  recycle_semantic_document?: SemanticDocument;
}

export interface PagePhotosTitleProps {
  sectionFilenameData: string[];
  semanticDocumentUUID: string;
  semanticDocumentTitle: string;
}

export interface PageArchivingTitleProps {
  sectionFilenameData: string[];
  semanticDocumentUUID: string;
  semanticDocumentTitle: string;
  isDeleted: boolean;
}
