// custom hooks for Document component

import { useNavigate } from 'react-router-dom';
import React, { useEffect, useMemo, useRef, useCallback } from 'react';
import { DragSourceMonitor, useDrag, useDrop } from 'react-dnd';

import {
  setInitDocument,
  updateIsEndDrag
} from '../DossierImages/DossierPageViewImage/actions';
import { actionClick, ItemTypes } from '../../constants';
import { setScroll } from '../../pages/Documents/actions';
import {
  needRotateAngle,
  useRotatedStyles
} from '../../utils/hooks/useRotatedStyles';
import { useGenerateLinkToDetailDocumentPage } from '../../utils/hooks/useGenerateLinkToDetailDocumentPage';
import { useGetSemanticPageOnDocumentDetailPage } from '../../utils/hooks/useGetSemanticPageOnDocumentDetailPage';

import type { RootState } from '../../store/redux/types';
import type { DragItemDDPNS } from './types';
import { useAppDispatch, useAppSelector } from '../../store/app/hooks';
import { parseGetQueryParam } from '@utils/index';
import { queryParamPageNumber } from '../../constants/routes';

export const useDocument = (props: any) => {
  // TODO: extract to custom hook
  // // --- Hooks ---
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { generateLinkToDetailDocumentPage } =
    useGenerateLinkToDetailDocumentPage();
  const { semanticPage, semanticDocument } =
    useGetSemanticPageOnDocumentDetailPage();

  // --- Refs ---
  const myRef = useRef<HTMLImageElement>(null);
  const wrapperImageRef = useRef<HTMLImageElement>(null);
  const dragAndDropRef = useRef<HTMLDivElement>(null);

  // --- Redux State ---
  const semantic_documents = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.semantic_documents
  );
  const access_mode = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.access_mode
  );
  const imgLoaded: boolean = useAppSelector(
    (state: RootState) => state.documents.loadedImg[props.semanticPageUUID]
  );
  const selectedSemanticPageUUID = useAppSelector(
    (state: RootState) => state.documents.scroll.selectedSemanticPageUUID
  );
  const selectedSemanticDocumentUUID = useAppSelector(
    (state: RootState) => state.documents.scroll.selectedSemanticDocumentUUID
  );
  const showDeletedSemanticDocuments = useAppSelector(
    (state: RootState) => state.header.showDeletedSemanticDocuments
  );
  const initDocument = useAppSelector(
    (state: RootState) => state.pageView.initDocument
  );
  const lastDocuments = useAppSelector(
    (state: RootState) => state.pageView.lastDocuments
  );
  const lastIndex = useAppSelector(
    (state: RootState) => state.pageView.lastIndex
  );
  const isEndDrag = useAppSelector(
    (state: RootState) => state.pageView.isEndDrag
  );
  const semanticDocumentUUID = useAppSelector(
    (state: RootState) => state.semanticDocument.semanticDocumentUUID
  );

  // --- Derived State ---
  const queryId = parseGetQueryParam(queryParamPageNumber);
  const isSortingModalView = props.isSortingModalView;
  const isInitDoc = useMemo(
    () =>
      initDocument && initDocument.semanticPageUUID === props.semanticPageUUID,
    [initDocument, props.semanticPageUUID]
  );

  // --- Styles ---
  const { rotatedStyles, isLandscapeMode } = useRotatedStyles(
    wrapperImageRef,
    myRef,
    props.rotation_angle,
    imgLoaded,
    true,
    false,
    isSortingModalView
  );

  const dossierImageStyle = useMemo(() => {
    const baseStyles =
      selectedSemanticDocumentUUID === semanticDocument?.uuid &&
      selectedSemanticPageUUID === props.semanticPageUUID
        ? { ...props.selectedDocumentStyle }
        : { ...props.styledImage };

    return {
      opacity: isInitDoc ? 0 : 1,
      ...baseStyles,
      ...rotatedStyles
    };
  }, [
    isInitDoc,
    props.semanticPageUUID,
    selectedSemanticDocumentUUID,
    selectedSemanticPageUUID,
    semanticDocument,
    rotatedStyles,
    props.selectedDocumentStyle,
    props.styledImage
  ]);

  const WrapperDivStyles = useMemo(() => {
    if (
      !isLandscapeMode &&
      needRotateAngle.includes(props.rotation_angle as number)
    ) {
      return {
        marginTop: '40px',
        marginBottom: '20px'
      };
    }
    return {};
  }, [isLandscapeMode, props.rotation_angle]);

  const PageDetailsRowStyles = useMemo(() => {
    const styles: React.CSSProperties = {
      width: '100%',
      marginLeft: '0'
    };

    if (
      !isLandscapeMode &&
      needRotateAngle.includes(props.rotation_angle as number)
    ) {
      styles.marginTop = '35px';
    }

    return styles;
  }, [isLandscapeMode, props.rotation_angle]);

  // --- Handlers ---
  const onLoad = useCallback(() => {
    if (!props.isStructureDetailsView) {
      if (
        semanticDocumentUUID &&
        props.semanticPageUUID === semanticPage?.uuid &&
        queryId === props.index.toString()
      ) {
        if (myRef.current) {
          const { bottom, top } = myRef.current.getBoundingClientRect();
          const valueBottom = window.innerHeight - bottom;
          const valueTop = window.innerHeight - top;
          if (!isEndDrag) {
            if (
              valueBottom > window.innerHeight ||
              valueTop > window.innerHeight
            )
              myRef.current.scrollIntoView({
                block: 'center',
                inline: 'center'
              });
            else if (valueBottom < 0 || valueTop < 0) {
              myRef.current.scrollIntoView({
                block: 'end',
                inline: 'center'
              });
            }
          } else {
            myRef.current.scrollIntoView({
              block: 'center',
              inline: 'center'
            });
          }
        }
      }
      if (selectedSemanticPageUUID === null) {
        dispatch(
          setScroll(
            semanticDocument?.uuid || '',
            semanticPage?.uuid || '',
            actionClick
          )
        );
      }
    }
  }, [
    props.isStructureDetailsView,
    semanticDocumentUUID,
    props.semanticPageUUID,
    semanticPage?.uuid,
    queryId,
    props.index,
    isEndDrag,
    selectedSemanticPageUUID,
    dispatch,
    semanticDocument?.uuid
  ]);

  const handleClickImage = useCallback(
    (semanticPageUUID: string, hideExpand = true, pageIndex: number) => {
      props.handleImageClick(
        semanticDocument?.uuid || '',
        semanticPageUUID,
        hideExpand
      );

      // dispatch(
      //   setScroll(semanticDocument?.uuid || '', semanticPageUUID, actionClick)
      // );

      const newurl = generateLinkToDetailDocumentPage(
        semanticDocument?.uuid || '',
        pageIndex
      );
      navigate(newurl, { replace: true });
    },
    [
      props.handleImageClick,
      semanticDocument?.uuid,
      dispatch,
      navigate,
      generateLinkToDetailDocumentPage
    ]
  );

  const updateDragStatus = useCallback(
    (status: boolean) => {
      if (status !== isEndDrag) {
        dispatch(updateIsEndDrag(status));
      }
    },
    [isEndDrag, dispatch]
  );

  const handleHover = useCallback(
    (item: DragItemDDPNS, monitor: any) => {
      updateDragStatus(false);

      if (!dragAndDropRef.current) return;

      const dragIndex = item.index;
      const hoverIndex = props.index;
      if (dragIndex === hoverIndex) return;

      const hoverBoundingRect = dragAndDropRef.current.getBoundingClientRect();
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY / 3) return;
      if (dragIndex > hoverIndex && hoverClientY / 3 > hoverMiddleY) return;

      props.checkInitDocument(item);
      props.movePage(dragIndex, hoverIndex);
      handleClickImage(item.semanticPageUUID, false, hoverIndex);

      item.index = hoverIndex;
    },
    [updateDragStatus, props, handleClickImage]
  );

  const prepareDataToRequest = useCallback(() => {
    props.addDataToRequest();
  }, [props.addDataToRequest]);

  const handleEndDnd = useCallback(
    (item: DragItemDDPNS, monitor: DragSourceMonitor) => {
      const didDrop = monitor.didDrop();

      if (!didDrop) {
        props.deleteDuplicate(item, false);
      }

      props.deleteLastIndex();
      dispatch(setInitDocument(null));
      updateDragStatus(true);
    },
    [props, dispatch, updateDragStatus]
  );

  // --- Effects ---
  useEffect(() => {
    if (
      !props.isStructureDetailsView &&
      props.semanticPageUUID === selectedSemanticPageUUID &&
      selectedSemanticDocumentUUID === semanticDocument?.uuid &&
      myRef.current &&
      queryId === props.index.toString()
    ) {
      const { bottom, top } = myRef.current.getBoundingClientRect();
      const valueBottom = window.innerHeight - bottom;
      const valueTop = window.innerHeight - top;

      if (!isEndDrag) {
        if (valueBottom > window.innerHeight || valueTop > window.innerHeight) {
          myRef.current.scrollIntoView({
            block: 'center',
            inline: 'center'
          });
        } else if (valueBottom < 0 || valueTop < 0) {
          myRef.current.scrollIntoView({
            block: 'end',
            inline: 'center'
          });
        }
      } else {
        myRef.current.scrollIntoView({
          block: 'center',
          inline: 'center'
        });
      }
    }
  }, [
    props.semanticPageUUID,
    selectedSemanticPageUUID,
    selectedSemanticDocumentUUID,
    semanticDocument,
    queryId,
    props.index,
    props.isStructureDetailsView,
    isEndDrag
  ]);

  // --- Drag and Drop ---
  const [{ isDragging }, drag] = useDrag(
    () => ({
      type: ItemTypes.DOCUMENT_PAGE_DDPNS,
      canDrag:
        !showDeletedSemanticDocuments &&
        access_mode !== 'read_only' &&
        semanticDocument?.access_mode !== 'read_only',
      item: (): DragItemDDPNS => ({
        index: props.index,
        semanticDocumentUUID: semanticDocumentUUID,
        semanticPageUUID: props.semanticPageUUID
      }),
      collect: (monitor) => ({
        isDragging: monitor.isDragging()
      }),
      end: handleEndDnd
    }),
    [
      props.index,
      props.movePage,
      props.semanticPageUUID,
      props.deleteLastIndex,
      showDeletedSemanticDocuments,
      initDocument,
      props.deleteDuplicate,
      semanticDocumentUUID,
      props.deleteDuplicate,
      lastDocuments,
      lastIndex
    ]
  );

  const [{ handlerId }, drop] = useDrop(
    () => ({
      accept: ItemTypes.DOCUMENT_PAGE_DDPNS,
      canDrop: () =>
        access_mode !== 'read_only' &&
        semanticDocument?.access_mode !== 'read_only',
      hover(item: DragItemDDPNS, monitor) {
        handleHover(item, monitor);
      },
      collect(monitor) {
        return {
          handlerId: monitor.getHandlerId(),
          canDrop: monitor.canDrop(),
          isOver: monitor.isOver()
        };
      },
      drop: () => {
        prepareDataToRequest();
      }
    }),
    [
      props.semanticPageUUID,
      props.movePage,
      dragAndDropRef,
      props.index,
      props.movePage,
      props.semanticPageUUID,
      props.deleteLastIndex,
      showDeletedSemanticDocuments,
      initDocument,
      props.deleteDuplicate,
      semanticDocumentUUID,
      props.deleteDuplicate,
      lastDocuments,
      lastIndex
    ]
  );

  if (!props.disableDnd) {
    drag(drop(dragAndDropRef));
  }

  return {
    myRef,
    wrapperImageRef,
    dragAndDropRef,
    dossierImageStyle,
    WrapperDivStyles,
    PageDetailsRowStyles,
    onLoad,
    handleClickImage,
    isSortingModalView,
    handlerId,
    selectedSemanticDocumentUUID,
    semanticDocument,
    selectedSemanticPageUUID
  };
};
