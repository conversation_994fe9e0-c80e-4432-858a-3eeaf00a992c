import React from 'react';
import { useTranslation } from 'react-i18next';

import { stopPropagation } from '../../utils';
import { TrashIcon } from '../icons/TrashIcon';
import { RESTORE_ICON_SIZE } from '../../constants';
import { SectionWithIcons } from '../SemanticDocumentPreviewListItem/style';
import { deleteSemanticDocumentAction } from './actions';
import { IconTitle, SectionWithIconWrapper } from './style';

import type { RootState } from '../../store/redux/types';
import type { IDossierSectionTitleAndFilename } from '../SemanticDocumentPreviewListItem/types';

import { useAppDispatch, useAppSelector } from '../../store/app/hooks';
import { useSwissfexDocumentActionPerformedData } from '../../SwissFex/hooks';

const DeleteIcon: React.FC<IDossierSectionTitleAndFilename> = (props) => {
  const { t } = useTranslation();
  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );

  const dispatch = useAppDispatch();

  const { setSwissfexRefreshData } = useSwissfexDocumentActionPerformedData();

  function handleClickDelete() {
    if (uuid) {
      setSwissfexRefreshData();
      dispatch(
        deleteSemanticDocumentAction(
          uuid,
          [props.semanticDocumentUUID],
          props.semanticDocumentTitle
        )
      );
    }
  }

  return (
    <SectionWithIcons onClick={stopPropagation}>
      <SectionWithIconWrapper onClick={handleClickDelete}>
        <IconTitle>{t('DELETE')}</IconTitle>
        <TrashIcon
          height={RESTORE_ICON_SIZE}
          width={RESTORE_ICON_SIZE}
          activateHover={true}
        />
      </SectionWithIconWrapper>
    </SectionWithIcons>
  );
};

export { DeleteIcon };
