import React from 'react';
import { useTranslation } from 'react-i18next';

import { HoveredSection } from './style';
import { undeleteSemanticDocumentAction } from './actions';

import type { IUnDeleteProps } from './types';
import type { RootState } from '../../store/redux/types';
import { useAppDispatch, useAppSelector } from '../../store/app/hooks';
import { useSwissfexDocumentActionPerformedData } from '../../SwissFex/hooks';

const UnDelete: React.FC<IUnDeleteProps> = (props) => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );

  const { setSwissfexRefreshData } = useSwissfexDocumentActionPerformedData();

  function handleClickUndelete(e: React.MouseEvent) {
    props.resetState();
    if (uuid) {
      setSwissfexRefreshData();
      dispatch(
        undeleteSemanticDocumentAction(
          uuid,
          props.semanticDocumentUUID,
          props.semanticDocumentTitle
        )
      );
    }
    e.preventDefault();
  }

  return (
    <HoveredSection onClick={handleClickUndelete}>
      {t('UNDELETE')}
    </HoveredSection>
  );
};

export { UnDelete };
