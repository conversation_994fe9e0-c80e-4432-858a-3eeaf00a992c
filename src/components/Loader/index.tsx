import React, { useEffect } from 'react';

import { Spinner } from '../Spinner';
import { LoaderWrapper, Curtain, SpinWrapper } from './styled';

import type { RootState } from '../../store/redux/types';
import { useAppSelector } from '../../store/app/hooks';

const Loader: React.FC = () => {
  const statusLoader = useAppSelector(
    (state: RootState) => state.loader.statusLoader
  );

  useEffect(() => {
    document.body.style.overflow = statusLoader ? 'hidden' : 'auto';
  }, [statusLoader]);

  return statusLoader ? (
    <LoaderWrapper>
      <SpinWrapper>
        <Spinner />
        <div className="hd-override-loading-wrapper">Loading...</div>
      </SpinWrapper>

      <Curtain />
    </LoaderWrapper>
  ) : null;
};
export { Loader };
