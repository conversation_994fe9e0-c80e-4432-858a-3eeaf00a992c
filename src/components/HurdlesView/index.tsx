import { Col, Row } from 'antd';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';

import {
  FinhurdleDocumentInformation,
  FinhurdleInformationWrapper,
  FinhurdleTitleDiv,
  HurdlesWrapper
} from './style';
import { FinhurdlesRow } from '../DataView/style';

import {
  setHighlight,
  setHighlightPageObject,
  setRefreshDocument
} from '../../pages/Documents/actions';
import { linkToDetailDocumentPage } from '../../utils/hooks/useGenerateLinkToDetailDocumentPage';

import type { RootState } from '../../store/redux/types';
import { useDossierHurdles } from './queries';
import { FinHurdleGroup, FinhurdleRef, PageObjectTitles } from '../../gen/dms';
import { useAppDispatch, useAppSelector } from '../../store/app/hooks';

const HurdlesView = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useAppDispatch();

  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );
  const hurdles = useDossierHurdles(uuid);

  const handleClick = (hurdleData: FinhurdleRef) => {
    // TODO: rather than setting highlight page_object_uuid and semantic_page_uuid via redux store
    // we should add it via page uri query
    if (hurdleData !== undefined) {
      dispatch(setRefreshDocument(false));
      dispatch(setHighlight(true));
      dispatch(
        setHighlightPageObject(
          hurdleData.page_object_uuid,
          hurdleData.semantic_page_uuid
        )
      );
    }
  };

  const getLink = (hurdleData: FinhurdleRef) => {
    return hurdleData?.semantic_document_uuid && uuid
      ? linkToDetailDocumentPage(
          i18n.language,
          uuid,
          hurdleData.semantic_document_uuid,
          hurdleData.semantic_page_number
        )
      : null;
  };

  const HurdlesViewContent = useMemo(() => {
    return (
      <>
        <FinhurdlesRow>
          {hurdles?.data.map((item: FinHurdleGroup) => {
            return (
              <HurdlesWrapper key={item.key}>
                <FinhurdleTitleDiv>
                  {i18n.language
                    ? item.titles[i18n.language as keyof PageObjectTitles]
                    : item.titles.de}
                </FinhurdleTitleDiv>
                <div>
                  {item?.finhurdle_refs?.map((finhurdleref: FinhurdleRef) => (
                    <FinhurdleInformationWrapper
                      key={`${finhurdleref.semantic_document_title}: ${
                        finhurdleref.semantic_page_number + 1
                      }: ${finhurdleref.page_object_value}`}
                    >
                      <Link
                        to={getLink(finhurdleref) || ''}
                        onClick={() => {
                          handleClick(finhurdleref);
                        }}
                      >
                        <FinhurdleDocumentInformation>
                          <Row>
                            <Col xs={10} xl={8} xxl={6}>
                              <span data-testid="finhurdleref-semantic-document-title">{`${finhurdleref.semantic_document_title}`}</span>
                            </Col>
                            <Col xs={14} xl={16} xxl={18}>
                              <span>
                                <div style={{ display: 'flex' }}>
                                  <div
                                    style={{ width: '30px' }}
                                  >{`${t('PAGE')}`}</div>
                                  <div
                                    style={{
                                      display: 'flex',
                                      justifyContent: 'flex-start'
                                    }}
                                  >
                                    <div
                                      style={{
                                        textAlign: 'end',
                                        width: '30px',
                                        marginRight: '3px'
                                      }}
                                    >
                                      {finhurdleref.semantic_page_number + 1}
                                    </div>
                                    <div>{`: ${finhurdleref.page_object_value}`}</div>
                                  </div>
                                </div>
                              </span>
                            </Col>
                          </Row>
                        </FinhurdleDocumentInformation>
                      </Link>
                    </FinhurdleInformationWrapper>
                  ))}
                </div>
              </HurdlesWrapper>
            );
          })}
        </FinhurdlesRow>
      </>
    );
  }, [hurdles?.data, t]);

  return <>{HurdlesViewContent}</>;
};

export { HurdlesView };
