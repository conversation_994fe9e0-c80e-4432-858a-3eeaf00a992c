import dayjs from 'dayjs';
import React from 'react';
import { useParams } from 'react-router-dom';
import relativeTime from 'dayjs/plugin/relativeTime';

import { ApiError, DossierAccessCheckErrorComponent } from '../../gen/dms';
import { useDossierAccessGrant } from './queries';
import { DossierNotAccessible } from './DossierNotAccessible';
import { NetworkErrorModal } from '../NetworkError/Modal/NetworkErrorModal';
import { useAccountContext } from '../Account/AccountContextProvider';
import { DossierNotAccessibleWithCustomMessage } from './DossierNotAccessible/DossierNotAccessibleWithCustomMessage';

// Load the relativeTime plugin
dayjs.extend(relativeTime);

type Props = {
  dossier_uuid: string;
  children?: React.ReactNode;
};

export const AccessGrant: React.FC<Props> = ({ dossier_uuid }) => {
  const dossierAccessCheck = useDossierAccessGrant(dossier_uuid);

  return dossierAccessCheck.data ? (
    <div>
      <div>
        Expires at {dayjs(dossierAccessCheck.data.accessGrant.expires).format()}
      </div>
      <div>Expires in {dossierAccessCheck.data.nextCheck.fromNow()}</div>
      <div>Recheck in {dayjs(dossierAccessCheck.data.nextCheck).format()}</div>
    </div>
  ) : (
    <div> No access grant </div>
  );
};

export const DossierAccessCheck: React.FC<Props> = ({
  dossier_uuid,
  children
}) => {
  const dossierAccessCheck = useDossierAccessGrant(dossier_uuid);

  const { dossier_access_check_error_component } = useAccountContext();

  if (dossierAccessCheck.isError) {
    if (dossierAccessCheck.error instanceof ApiError)
      if (dossierAccessCheck.error.status === 502) {
        return (
          <NetworkErrorModal
            isModalVisible={true}
            isDossierAccessCheckError={true}
          />
        );
      } else {
        switch (dossier_access_check_error_component) {
          case DossierAccessCheckErrorComponent.BCGE_ACCESS_CHECK_INSTRUCTIONS:
          case DossierAccessCheckErrorComponent.FINNOVA_ACCESS_CHECK_INSTRUCTIONS:
            return (
              <DossierNotAccessibleWithCustomMessage
                dossierUUID={dossier_uuid}
                errorStatus={dossierAccessCheck.error.status.toString()}
                errorText={dossierAccessCheck.error.statusText}
              />
            );

          case DossierAccessCheckErrorComponent.DEFAULT:
          default:
            return (
              <DossierNotAccessible
                dossierUUID={dossier_uuid}
                errorStatus={dossierAccessCheck.error.status.toString()}
                errorText={dossierAccessCheck.error.statusText}
              />
            );
        }
      }
  }

  if (dossierAccessCheck.isLoading) {
    return <div className="hd-override-loading-wrapper">Loading...</div>;
  }

  return <>{children}</>;
};

type ParamDossierAccessCheckProps = {
  children?: React.ReactNode;
};

export const ParamDossierAccessCheck: React.FC<
  ParamDossierAccessCheckProps
> = ({ children }) => {
  const params = useParams();

  return (
    <DossierAccessCheck dossier_uuid={params.uuid || ''}>
      {children}
    </DossierAccessCheck>
  );
};
