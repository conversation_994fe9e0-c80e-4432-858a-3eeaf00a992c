import { useTranslation } from 'react-i18next';
import React, { useEffect, useState } from 'react';

import {
  CustomSelect,
  ModalTitle,
  OptionContainer,
  OptionValue,
  OptionValuerExternal,
  OptionWrapper,
  StyledCategorySelectWrapper
} from './styles';
import { getDocumentCategories } from '../DossierSectionIcons/actions';
import { generateSemanticDocumentTitle } from '../DossierSectionIcons/utils';

import type { RootState } from '../../store/redux/types';
import type { IDocumentCategorySelectProps } from './types';
import type { CallbackForCategoryRecommendationType } from './types';
import type { ISemanticDocumentCategoryRecommendationType } from '../DossierSectionIcons/types';
import { CaretDownOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '../../store/app/hooks';
import { stripAccentsFromString } from '../../utils';
import { useAccountContext } from '../Account/AccountContextProvider.tsx';

const DocumentCategorySelect: React.FC<IDocumentCategorySelectProps> = (
  props
) => {
  const { show_document_category_external } = useAccountContext();
  const uuid = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.uuid
  );
  const lang = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.lang
  );
  const access_mode = useAppSelector(
    (state: RootState) => state.dossiers.dossiersData.access_mode
  );

  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const [currentValue, setCurrentValue] = useState<string>('');

  const [documentCategories, setDocumentCategories] = useState<
    ISemanticDocumentCategoryRecommendationType[]
  >([]);

  const [filterDocumentCategories, setFilterDocumentCategories] = useState<
    ISemanticDocumentCategoryRecommendationType[]
  >([]);
  const [documentDefaultCategories, setDefaultDocumentCategories] = useState<
    ISemanticDocumentCategoryRecommendationType[]
  >([]);

  const callbackForDocumentCategories: CallbackForCategoryRecommendationType = (
    data
  ) => {
    setDocumentCategories(data);
    setFilterDocumentCategories(data);
    if (props.setDocCatLoaded) {
      props.setDocCatLoaded(true);
    }
  };

  const callbackForDefaultDocumentCategories: CallbackForCategoryRecommendationType =
    (data) => {
      setDefaultDocumentCategories(data);
    };

  const updateDocumentCategories = () => {
    dispatch(
      getDocumentCategories(
        uuid,
        callbackForDocumentCategories,
        false,
        props.semanticDocument && props.semanticDocument.uuid
      )
    );
  };

  const prepareStringForCategoryComparison = (input: string) => {
    return stripAccentsFromString(input);
  };

  const getDocCatExternalInformation = (
    docCat: ISemanticDocumentCategoryRecommendationType,
    language: string
  ) => {
    if (!language || language === '' || !docCat) return '';

    const currLang: string = language?.toLowerCase();

    const docCatExternals: any = {
      de: docCat.de_external || '',
      en: docCat.en_external || '',
      fr: docCat.fr_external || '',
      it: docCat.it_external || ''
    };
    return docCatExternals[currLang] || '';
  };

  const fillOptions = (input: string) => {
    const newDocumentCategories = documentCategories.filter((item) => {
      const externalValue = getDocCatExternalInformation(item, lang);

      const defaultFilter =
        prepareStringForCategoryComparison(
          generateSemanticDocumentTitle(item)
        ).indexOf(prepareStringForCategoryComparison(input)) >= 0;

      const externalFilter = externalValue
        ? prepareStringForCategoryComparison(externalValue).indexOf(
            prepareStringForCategoryComparison(input)
          ) >= 0
        : false;

      const additionalSearchTermsDeFilter = item.additional_search_terms_de
        ? prepareStringForCategoryComparison(
            item.additional_search_terms_de
          ).indexOf(prepareStringForCategoryComparison(input)) >= 0
        : false;

      const additionalSearchTermsEnFilter = item.additional_search_terms_en
        ? prepareStringForCategoryComparison(
            item.additional_search_terms_en
          ).indexOf(prepareStringForCategoryComparison(input)) >= 0
        : false;

      const additionalSearchTermsFrFilter = item.additional_search_terms_fr
        ? prepareStringForCategoryComparison(
            item.additional_search_terms_fr
          ).indexOf(prepareStringForCategoryComparison(input)) >= 0
        : false;

      const additionalSearchTermsItFilter = item.additional_search_terms_it
        ? prepareStringForCategoryComparison(
            item.additional_search_terms_it
          ).indexOf(prepareStringForCategoryComparison(input)) >= 0
        : false;

      if (show_document_category_external) {
        return (
          defaultFilter ||
          externalFilter ||
          additionalSearchTermsDeFilter ||
          additionalSearchTermsEnFilter ||
          additionalSearchTermsFrFilter ||
          additionalSearchTermsItFilter
        );
      } else {
        return (
          defaultFilter ||
          additionalSearchTermsDeFilter ||
          additionalSearchTermsEnFilter ||
          additionalSearchTermsFrFilter ||
          additionalSearchTermsItFilter
        );
      }
    });

    if (newDocumentCategories.length) {
      setFilterDocumentCategories(newDocumentCategories);
    } else {
      setFilterDocumentCategories(documentDefaultCategories);
    }
  };

  const getDocumentCategoryFromString = (doc_cat: string) => {
    if (doc_cat) {
      const [doc_cat_id] = doc_cat.split(' ');

      return documentCategories.find((docCat) => {
        return String(docCat.id) === String(doc_cat_id);
      });
    }
  };

  const loadDocumentCategory = () => {
    if (props.semanticDocument && props.semanticDocument.document_category) {
      const value = getDocumentCategoryFromString(
        props.semanticDocument.document_category.id
      );

      if (value && Object.keys(props.documentCat).length === 0) {
        props.setDocumentCat(value);
      }
    }
  };

  const updateSelectValue = (value: string) => {
    if (access_mode === 'read_only') {
      return;
    }
    props.setDocumentCat(getDocumentCategoryFromString(value));
  };

  useEffect(() => {
    if (props.isModalVisible) {
      loadDocumentCategory();
    }
  }, [props.isModalVisible, props.semanticDocument, documentCategories]);

  useEffect(() => {
    if (props.isModalVisible) {
      updateDocumentCategories();
    }
  }, [props.isModalVisible]);

  useEffect(() => {
    dispatch(
      getDocumentCategories(uuid, callbackForDefaultDocumentCategories, true)
    );
  }, []);

  useEffect(() => {
    if (props.documentCat) {
      setCurrentValue(generateSemanticDocumentTitle(props.documentCat));
    }
  }, [props.documentCat]);

  const [categoryDropdownOpen, setCategoryDropdownOpen] = useState(false);

  const handleDropdownIconClick = () => {
    setCategoryDropdownOpen((prev) => !prev);
  };
  const onDropdownVisibleChange = (open: boolean) => {
    setCategoryDropdownOpen(open);
    if (open) {
      fillOptions('');
    }
  };

  return (
    <div style={{ width: '100%', marginBottom: '10px' }}>
      <ModalTitle>
        <i>{t('DOCUMENT_TYPE')}</i>
      </ModalTitle>
      <StyledCategorySelectWrapper>
        <CustomSelect
          showSearch
          open={categoryDropdownOpen}
          listHeight={300}
          // $isError={!!props.isError}
          className="hd-ant-rename-modal-category-select"
          getPopupContainer={(triggerNode) => triggerNode.parentNode}
          value={currentValue}
          onChange={updateSelectValue}
          onSearch={fillOptions}
          onDropdownVisibleChange={onDropdownVisibleChange}
          filterOption={false}
          disabled={access_mode === 'read_only'}
          // getPopupContainer={(trigger) => trigger.parentElement}
          suffixIcon={
            <CaretDownOutlined
              style={{ fontSize: 17, marginLeft: -10 }}
              onClick={handleDropdownIconClick}
            />
          }
        >
          {filterDocumentCategories.length &&
            filterDocumentCategories.map((docCat, index) => {
              const external = getDocCatExternalInformation(docCat, lang);

              const value = generateSemanticDocumentTitle(docCat);
              return (
                <OptionWrapper key={index} value={value}>
                  <OptionContainer>
                    <OptionValue
                      $showCategoryExternalColumn={
                        show_document_category_external
                      }
                    >
                      {value}
                    </OptionValue>
                    {show_document_category_external && (
                      <OptionValuerExternal>{external}</OptionValuerExternal>
                    )}
                  </OptionContainer>
                </OptionWrapper>
              );
            })}
        </CustomSelect>
      </StyledCategorySelectWrapper>
    </div>
  );
};

export { DocumentCategorySelect };
