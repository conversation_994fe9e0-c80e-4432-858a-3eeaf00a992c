import { ScissorOutlined } from '@ant-design/icons';
import React, { useRef, useState } from 'react';
import { StyledHypodossierModal } from '@components/Modal/GenericModal.tsx';
import { useTranslation } from 'react-i18next';
import SearchablePagePdfJs from '@components/SearchableDocuments/SearchablePagePdfJs.tsx';
import { Flex } from 'antd';
import {
  AnnotationModalProps,
  IRectViewportCoords,
  ICoord
} from '@components/DocumentListNavBar/AnnotationModal/types.ts';
import {
  mapDomRectsToViewportRectCoords,
  mapViewportCoordsToPdfCoords,
  mergeOverlappingRects,
  mapAbsoluteCoordsToRelative
} from './rects.ts';
import HighlightAnnotation from '@components/DocumentListNavBar/AnnotationModal/HighlightAnnotation';
import { useSemanticPageAnnotations } from '@components/DocumentListNavBar/AnnotationModal/hooks.ts';
import { ANNOTATION_TYPES } from '@components/DocumentListNavBar/AnnotationModal/constants.ts';
import { getAnnotationsType } from '@components/DocumentListNavBar/AnnotationModal/utils.ts';
import UndoAnnotation from '@components/DocumentListNavBar/AnnotationModal/Components/UndoAnnotation';

const AnnotationModal: React.FC<AnnotationModalProps> = ({
  handleCloseModal,
  annotationType,
  semanticPageUuid
}) => {
  const { t } = useTranslation();
  const [modalIsOpen, setModalIsOpen] = useState<boolean>(true);

  const [pdfPageRect, setPdfPageRect] = useState<IRectViewportCoords | null>(
    null
  );
  const [scrollOffset, setScrollOffset] = useState<ICoord>({ top: 0, left: 0 });
  const pdfPageRef = useRef<HTMLDivElement>(null);

  const {
    userAnnotationData,
    createAnnotationsMutation,
    deleteAnnotationsMutation,
    canUndo,
    undoLastAnnotation
  } = useSemanticPageAnnotations(semanticPageUuid, annotationType);

  const handleOk = () => {
    handleCloseModal();
  };

  const onPdfRenderSuccess = () => {
    if (pdfPageRef.current) {
      const rect = pdfPageRef.current.getBoundingClientRect();
      const pageDimensions = {
        width: rect.width,
        height: rect.height,
        top: rect.top,
        left: rect.left
      };
      setPdfPageRect(pageDimensions);
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollOffset({
      top: e.currentTarget.scrollTop,
      left: e.currentTarget.scrollLeft
    });
  };

  const handleNewHighlightAnnotation = () => {
    const selection = window.getSelection();
    const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
    const rects = range ? range.getClientRects() : null;
    if (!rects) return;
    const rectsInViewportCoords = mapDomRectsToViewportRectCoords(rects);
    const pdfCoords = mapViewportCoordsToPdfCoords(
      rectsInViewportCoords,
      scrollOffset,
      pdfPageRect
    );
    const mergedRects = mergeOverlappingRects(pdfCoords);
    createAnnotationsMutation.mutate(
      mapAbsoluteCoordsToRelative(
        mergedRects,
        pdfPageRect.width,
        pdfPageRect.height
      ).map((rect) => ({
        bbox_height: rect.height,
        bbox_left: rect.left,
        bbox_top: rect.top,
        bbox_width: rect.width,
        annotation_type: ANNOTATION_TYPES.HIGHLIGHT
      }))
    );
    selection.collapse(null);
  };

  return (
    <StyledHypodossierModal
      className="hd-custom-scrollbars"
      title={t('HIGHLIGHT')}
      icon={<ScissorOutlined rotate={270} />}
      open={modalIsOpen}
      onOk={handleOk}
      onCancel={handleCloseModal}
      destroyOnClose={true}
      okText={'ok'}
      cancelButtonProps={{ style: { display: 'none' } }}
      modalStyles={{ padding: '0px 24px 0px 24px' }}
      headerModalStyles={{ marginBottom: '0px' }}
      width={'fit-content'}
    >
      <Flex
        style={{
          width: '100%',
          height: `calc(100vh - 175px)`,
          overflowY: 'scroll',
          overflowX: 'hidden',
          padding: '20px 40px 20px 40px',
          position: 'relative'
        }}
        vertical
        justify="start"
        onScroll={handleScroll}
      >
        {annotationType === ANNOTATION_TYPES.HIGHLIGHT && (
          <Flex
            style={{
              position: 'sticky',
              top: 0,
              zIndex: 100,
              justifyContent: 'flex-end',
              width: '100%',
              padding: '8px',
              height: 'fit-content',
              visibility: canUndo ? 'visible' : 'hidden'
            }}
          >
            <UndoAnnotation onClick={undoLastAnnotation} />
          </Flex>
        )}

        <Flex justify="center" align="center">
          <div
            ref={pdfPageRef}
            style={{
              position: 'relative',
              boxShadow:
                '0 1px 3px 2px rgb(0 0 0 / 20%), 1px 1px 6px 0 rgb(0 0 0 / 30%)'
            }}
            onMouseUp={
              annotationType === ANNOTATION_TYPES.HIGHLIGHT
                ? handleNewHighlightAnnotation
                : null
            }
          >
            <SearchablePagePdfJs
              fileUrl={userAnnotationData?.searchable_pdf_url}
              scale={1}
              onPdfRenderSuccess={onPdfRenderSuccess}
            />
            {userAnnotationData?.user_annotations &&
              pdfPageRect &&
              Object.keys(userAnnotationData.user_annotations).map(
                (key) =>
                  getAnnotationsType(
                    userAnnotationData.user_annotations[key]
                  ) === ANNOTATION_TYPES.HIGHLIGHT && (
                    <HighlightAnnotation
                      key={key}
                      annotations={userAnnotationData.user_annotations[key]}
                      pdfPageRect={pdfPageRect}
                      handleDeleteAnnotations={() =>
                        deleteAnnotationsMutation.mutate(key)
                      }
                      showDeleteAnnotationIcon={true}
                    />
                  )
              )}
          </div>
        </Flex>
      </Flex>
    </StyledHypodossierModal>
  );
};

export default AnnotationModal;
