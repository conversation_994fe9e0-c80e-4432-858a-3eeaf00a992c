// Box dimensions and positioning
export const MIN_BOX_WIDTH = 40;
export const MIN_BOX_HEIGHT = 40;
export const EDGE_BUFFER = 9;
export const DRAG_HANDLE_OFFSET = 10;

// Font and text related constants
export const FONT_HEIGHT_TO_WIDTH_RATIO = 0.605;
export const MIN_FONT_SIZE_FACTOR = 0.005;
export const MAX_FONT_SIZE_FACTOR = 0.05;
export const FONT_PRECISION = 0.02;

// Styling constants
export const ANNOTATION_PADDING = 16;
export const ANNOTATION_BORDER_WIDTH = 1;
export const FONT_LINE_HEIGHT = 10;
export const RESIZE_HANDLE_HEIGHT = 20;
export const SAVE_BUTTON_HEIGHT = 21;
export const BOTTOM_PADDING = 20;
export const TEXT_AREA_INTERNAL_PADDING = 20;

// Navigation keys that should not propagate to parent components
export const NAVIGATION_KEYS = [
  'ArrowLeft',
  'ArrowRight',
  'ArrowUp',
  'ArrowDown',
  'Home',
  'End'
] as const;
