import { IRect } from '@components/DocumentListNavBar/AnnotationModal/types';
import { FONT_HEIGHT_TO_WIDTH_RATIO, MIN_BOX_HEIGHT } from './constants';

/**
 * Calculates the number of lines and optimal font size for a given text and container dimensions
 * @param text The text to calculate lines for
 * @param elementDimensions The dimensions of the container
 * @param minFontSize The minimum font size to use
 * @param maxFontSize The maximum font size to use which depends on size of the page
 * @returns [number of lines, font size, isError, intrinsicTextHeight]
 */
export function countTextLines(
  text: string,
  elementDimensions: IRect,
  minFontSize: number,
  maxFontSize: number
): [number, number, boolean, number] {
  // Helper function to calculate if text fits in a given width
  function calculateTextFit(
    text: string,
    fontSize: number,
    maxWidth: number
  ): { lines: number; fits: boolean; isMinimumSize: boolean } {
    const charWidth = fontSize * FONT_HEIGHT_TO_WIDTH_RATIO;
    const lineHeight = fontSize * 1.2;
    const paragraphs = text.split('\n');

    // Calculate how many characters can fit in one line
    const charsPerLine = Math.max(1, Math.floor(maxWidth / charWidth));

    let totalLines = 0;
    let currentLineChars = 0;
    let isFirstWordInLine = true;
    let hasOverflowingWord = false;

    for (const paragraph of paragraphs) {
      if (paragraph.length === 0) {
        totalLines++;
        continue;
      }

      const words = paragraph.split(' ');

      for (const word of words) {
        const wordLength = word.length;
        const spaceLength = isFirstWordInLine ? 0 : 1;
        const totalLength = currentLineChars + spaceLength + wordLength;

        // If word doesn't fit in current line
        if (totalLength > charsPerLine) {
          // If it's the first word in line and too long, break it
          if (isFirstWordInLine) {
            // If we're at minimum font size and word is too long, mark as overflow
            if (fontSize === minFontSize && wordLength > charsPerLine) {
              hasOverflowingWord = true;
            }
            const wordLines = Math.ceil(wordLength / charsPerLine);
            totalLines += wordLines;
            currentLineChars = wordLength % charsPerLine;
          } else {
            // Start new line
            totalLines++;

            if (wordLength > charsPerLine) {
              const wordLines = Math.ceil(wordLength / charsPerLine);
              totalLines += wordLines - 1; // already added 1 line above
              currentLineChars = wordLength % charsPerLine;
              if (fontSize === minFontSize) {
                hasOverflowingWord = true;
              }
            } else {
              currentLineChars = wordLength;
            }
          }
        } else {
          // Word fits in current line
          currentLineChars = totalLength;
        }
        isFirstWordInLine = false;
      }

      // Add line for paragraph end
      if (currentLineChars > 0) {
        totalLines++;
        currentLineChars = 0;
        isFirstWordInLine = true;
      }
    }

    // Calculate total height needed
    const totalHeight = totalLines * lineHeight;
    const fits = totalHeight <= elementDimensions.height;

    return {
      lines: totalLines,
      fits,
      isMinimumSize: fontSize === minFontSize && (hasOverflowingWord || !fits)
    };
  }

  // Helper function to find optimal font size
  function findOptimalFontSize(): {
    size: number;
    lines: number;
    error: boolean;
  } {
    // First try maximum font size
    const maxSizeResult = calculateTextFit(
      text,
      maxFontSize,
      elementDimensions.width
    );
    if (maxSizeResult.fits) {
      return { size: maxFontSize, lines: maxSizeResult.lines, error: false };
    }

    // Binary search for optimal size
    let left = minFontSize;
    let right = maxFontSize;
    let bestSize = minFontSize;
    let foundFittingSize = false;

    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const result = calculateTextFit(text, mid, elementDimensions.width);

      if (result.fits) {
        // Found a fitting size, try to find a larger one
        foundFittingSize = true;
        bestSize = mid;
        left = mid + 1;
      } else {
        // Size too large, try smaller
        right = mid - 1;
      }
    }

    // If we found a fitting size, verify it one last time
    if (foundFittingSize) {
      const finalCheck = calculateTextFit(
        text,
        bestSize,
        elementDimensions.width
      );
      if (finalCheck.fits) {
        return {
          size: Number(bestSize.toFixed(1)),
          lines: finalCheck.lines,
          error: false
        };
      }
    }

    // If no fitting size found, use minimum size and check for errors
    const minSizeResult = calculateTextFit(
      text,
      minFontSize,
      elementDimensions.width
    );
    return {
      size: minFontSize,
      lines: minSizeResult.lines,
      error: minSizeResult.isMinimumSize
    };
  }

  // Find the optimal font size and return results
  const result = findOptimalFontSize();

  // Calculate the optimal height needed for the text
  const lineHeight = result.size * 1.2;
  const textHeight = result.lines * lineHeight;

  // The padding used in EditableCommentBubbleContentContainer is pdfPageRect.width / 100
  // This is applied on all sides, so we need to account for top and bottom padding (2x)
  // We approximate this as ~10% of text height for simplicity
  const paddingFactor = 1.1; // Add ~10% for padding
  const intrinsicTextHeight = Math.max(
    MIN_BOX_HEIGHT,
    textHeight * paddingFactor
  );

  return [result.lines, result.size, result.error, intrinsicTextHeight];
}
