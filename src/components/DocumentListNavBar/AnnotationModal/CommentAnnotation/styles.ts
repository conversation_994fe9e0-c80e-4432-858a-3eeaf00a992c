import styled from 'styled-components';

interface DragHandlerIconContainerProps {
  $isDragging: boolean;
  $isViewMode: boolean;
}

const SAVE_BUTTON_HEIGHT = 22;

type EditableCommentBubbleContentContainerProps = {
  padding: number;
};

export const EditableCommentBubbleContentContainer = styled.div<EditableCommentBubbleContentContainerProps>`
  display: flex;
  flex-direction: row;
  justify-content: end;
  width: 100%;
  height: 100%;
  padding: ${(props) => props.padding}px;
  position: relative;
  overflow: hidden;
`;

export const HorizontalDragHandlerIconContainer = styled.div<DragHandlerIconContainerProps>`
  position: absolute;
  top: 0px;
  left: -1px;
  transform: translateY(-100%);

  // Creates a polygon shape that extends beyond the container boundaries to create a seamless
  // connection with the comment bubble below cutting the overflowing button box-shadow. The clip-path defines a shape that:
  // - Starts at (-15px, -15px) to extend beyond the top-left
  // - Goes to (width+15px, -15px) to extend beyond the top-right
  // - Goes to (width+15px, height+15px) to extend beyond the bottom-right
  // - Goes to (width, height+15px) to create a right edge
  // - Goes to (width, height) to create the bottom-right corner
  // - Goes to (-15px, height) to create the bottom edge
  clip-path: polygon(
    -15px -15px,
    calc(100% + 15px) -15px,
    calc(100% + 15px) calc(100% + 15px),
    100% calc(100% + 15px),
    100% 100%,
    -15px 100%
  );
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5);

  background-color: rgba(248, 229, 112, 0.55);
  border-radius: 5px 5px 0 0;
  border: 1px solid rgba(244, 214, 36);
  border-bottom: none;

  width: calc(100% + 2px);
  height: 19px;
  cursor: ${(props) =>
    props.$isViewMode ? 'default' : props.$isDragging ? 'grabbing' : 'grab'};
  display: flex;
  justify-content: flex-end;
  align-items: center;
`;

export const EditableCommentBubbleContent = styled.div`
  width: 100%;
  height: 100%;
  font-family: monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
`;

export const StyledTextArea = styled.textarea`
  border: none;
  outline: none;
  resize: none;
  background: transparent;
  width: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden;
  white-space: pre-wrap;
  word-wrap: break-word;
  box-sizing: border-box;
  font-family: monospace;
  display: block;
  flex: 1;
`;

export const StyledErrorMessage = styled.div`
  color: #ff4d4f;
  font-size: 8px;
  font-weight: 500;
  pointer-events: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const SaveButtonContainer = styled.div`
  position: absolute;
  bottom: 0px;
  left: -1px;

  border-radius: 0 0 5px 5px;
  border: 1px solid rgba(244, 214, 36);
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5);
  background-color: rgba(248, 229, 112, 0.55);
  transform: translateY(100%);
  width: calc(100% + 2px);
  height: ${SAVE_BUTTON_HEIGHT}px;
  min-height: ${SAVE_BUTTON_HEIGHT}px;
  display: flex;
  padding: 0 1px;
  justify-content: space-between;
  align-items: center;
`;
