import React, { useEffect, useState } from 'react';
import { IRectPdfCoords } from '@components/DocumentListNavBar/AnnotationModal/types';
import DeleteAnnotation from '@components/DocumentListNavBar/AnnotationModal/Components/DeleteAnnotation';
import EditCommentAnnotation from '../Components/EditCommentAnnotation';
import { ANNOTATION_TYPES } from '../constants';
import {
  StyledErrorMessage,
  SaveButtonContainer,
  StyledTextArea,
  EditableCommentBubbleContent,
  EditableCommentBubbleContentContainer,
  HorizontalDragHandlerIconContainer
} from './styles';
import SaveAnnotation from '@components/DocumentListNavBar/AnnotationModal/Components/SaveAnnotation';
import { useTranslation } from 'react-i18next';
import { Resizable } from 're-resizable';
import Draggable from 'react-draggable';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEllipsisVertical } from '@fortawesome/pro-solid-svg-icons';
import { usePositionAndSize } from './hooks/usePositionAndSize';
import { useTextHandling } from './hooks/useTextHandling';
import {
  DRAG_HANDLE_OFFSET,
  EDGE_BUFFER,
  MIN_BOX_HEIGHT,
  MAX_FONT_SIZE_FACTOR,
  MIN_FONT_SIZE_FACTOR
} from './constants';
import { countTextLines } from './utils';

interface CommentAnnotationProps {
  position?: IRectPdfCoords;
  text?: string;
  pdfPageRect: IRectPdfCoords;
  rotationAngle: number;
  handleDeleteAnnotations?: () => void;
  showAnnotationActionIcons?: boolean;
  onSaveCommentHandler: (
    annotationRects: IRectPdfCoords[],
    text: string,
    annotationGroupUuid?: string
  ) => void;
  pdfPageRef?: React.MutableRefObject<HTMLDivElement>;
  annotationGroupUuid?: string;
  isReadOnly?: boolean;
  isNewComment?: boolean;
}

export function CommentAnnotation({
  position: initialPosition = {
    top: 50,
    left: 50,
    width: 140,
    height: 70
  },
  text: initialText = '',
  pdfPageRect,
  handleDeleteAnnotations,
  showAnnotationActionIcons = false,
  onSaveCommentHandler,
  pdfPageRef,
  annotationGroupUuid,
  isReadOnly = false,
  isNewComment = false,
  rotationAngle
}: CommentAnnotationProps) {
  const [hover, setHover] = useState(false);
  const [isEditMode, setEditMode] = useState<boolean>(isNewComment);
  const [isDragging, setIsDragging] = useState(false);

  const { currentPosition, size, handleDrag, handleResize, resizeBounds } =
    usePositionAndSize({
      initialPosition,
      pdfPageRect,
      isNewComment
    });

  const {
    text,
    showLimitError,
    handleKeyDown,
    handleInputChange,
    textareaRef,
    displayRef
  } = useTextHandling({
    initialText,
    isEditMode,
    pdfPageRect
  });

  const { t } = useTranslation();

  const handleEditMode = () => {
    if (!isEditMode) {
      setEditMode(true);
    }
  };

  const handleSaveComment = () => {
    if (showLimitError) {
      return;
    }

    // Calculate the optimal text height for the provided content
    const [_lines, _fontSize, _isError, intrinsicTextHeight] = countTextLines(
      text.trimEnd(),
      {
        width: size.width,
        height: size.height,
        top: 0,
        left: 0
      },
      Math.max(1, Math.floor(pdfPageRect.width * MIN_FONT_SIZE_FACTOR)), // minFontSize
      MAX_FONT_SIZE_FACTOR * pdfPageRect.height // maxFontSize
    );

    onSaveCommentHandler(
      [{ ...currentPosition, width: size.width, height: intrinsicTextHeight }],
      text.trimEnd(),
      annotationGroupUuid
    );

    if (!isNewComment) {
      setEditMode(false);
      setHover(false);
    }
  };

  // If the page is rotated, turn off edit mode
  useEffect(() => {
    if (rotationAngle !== 0) {
      setEditMode(false);
    }
  }, [rotationAngle]);

  if (currentPosition.left === 0 || currentPosition.top === 0) {
    return null;
  }

  return (
    <Draggable
      disabled={!isEditMode}
      position={{
        x: currentPosition.left,
        y: currentPosition.top
      }}
      onStart={() => setIsDragging(true)}
      onStop={() => setIsDragging(false)}
      handle=".handle"
      bounds={{
        left: EDGE_BUFFER,
        top: EDGE_BUFFER,
        right:
          pdfPageRect.width -
          (size.width + EDGE_BUFFER + DRAG_HANDLE_OFFSET / 3),
        bottom: pdfPageRect.height - (size.height + EDGE_BUFFER)
      }}
      onDrag={handleDrag}
    >
      <Resizable
        bounds={pdfPageRef?.current}
        minWidth={pdfPageRect.width / 6}
        minHeight={MIN_BOX_HEIGHT}
        size={{
          width: size.width,
          height: size.height
        }}
        maxWidth={resizeBounds.maxWidth}
        maxHeight={resizeBounds.maxHeight}
        onResize={handleResize}
        enable={{
          top: false,
          topRight: false,
          bottom: isEditMode,
          left: false,
          bottomLeft: false,
          bottomRight: isEditMode,
          right: isEditMode
        }}
        style={{
          position: 'absolute',
          backgroundColor: 'rgba(248, 229, 112)',
          border: '1px solid rgba(244, 214, 36)',
          borderRadius: isEditMode ? '0' : hover ? '0 0 5px 5px' : '5px',
          boxShadow: '2px 2px 5px rgba(0, 0, 0, 0.5)',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <div
          onMouseEnter={() => setHover(true)}
          onMouseLeave={() => setHover(false)}
          style={{
            position: 'relative',
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          {isEditMode || hover ? (
            <HorizontalDragHandlerIconContainer
              className="handle"
              $isDragging={isDragging}
              $isViewMode={!isEditMode}
            >
              {isEditMode ? (
                <FontAwesomeIcon
                  icon={faEllipsisVertical}
                  style={{
                    height: '12px',
                    color: '#666666',
                    position: 'absolute',
                    left: '50%',
                    transform: 'translateX(-50%) rotate(90deg)'
                  }}
                />
              ) : null}
              {(showAnnotationActionIcons || isNewComment) &&
                (hover || isEditMode) && (
                  <div
                    style={{
                      display: 'flex',
                      width: 'fit-content',
                      alignItems: 'center',
                      zIndex: 5
                    }}
                  >
                    {!isReadOnly && (
                      <EditCommentAnnotation onClick={handleEditMode} />
                    )}
                    <DeleteAnnotation
                      onClick={handleDeleteAnnotations}
                      annotationType={ANNOTATION_TYPES.COMMENT}
                      position="relative"
                    />
                  </div>
                )}
            </HorizontalDragHandlerIconContainer>
          ) : null}

          <EditableCommentBubbleContentContainer
            padding={pdfPageRect.width / 100}
          >
            {isEditMode ? (
              <StyledTextArea
                ref={textareaRef}
                value={text}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                autoFocus
                spellCheck="false"
              />
            ) : (
              <EditableCommentBubbleContent
                ref={displayRef}
                title={showLimitError ? initialText : undefined}
              >
                {initialText}
              </EditableCommentBubbleContent>
            )}
          </EditableCommentBubbleContentContainer>

          {isEditMode ? (
            <SaveButtonContainer>
              <StyledErrorMessage>
                {isEditMode && showLimitError && t('NO_MORE_SPACE_FOR_COMMENT')}
              </StyledErrorMessage>
              {isEditMode && (
                <SaveAnnotation
                  disabled={showLimitError}
                  onClick={handleSaveComment}
                />
              )}
            </SaveButtonContainer>
          ) : null}
        </div>
      </Resizable>
    </Draggable>
  );
}

export default CommentAnnotation;
