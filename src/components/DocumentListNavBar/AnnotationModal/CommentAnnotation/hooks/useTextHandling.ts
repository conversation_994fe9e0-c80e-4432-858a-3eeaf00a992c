import { useState, useCallback, useEffect } from 'react';
import { useTextCalculation } from './useTextCalculation';
import {
  MAX_FONT_SIZE_FACTOR,
  MIN_FONT_SIZE_FACTOR,
  NAVIGATION_KEYS
} from '../constants';
import { IRectPdfCoords } from '../../types';

interface UseTextHandlingProps {
  initialText: string;
  isEditMode: boolean;
  pdfPageRect: IRectPdfCoords;
}

interface UseTextHandlingReturn {
  text: string;
  showLimitError: boolean;
  handleKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  textareaRef: React.RefObject<HTMLTextAreaElement>;
  displayRef: React.RefObject<HTMLDivElement>;
}

export const useTextHandling = ({
  initialText,
  isEditMode,
  pdfPageRect
}: UseTextHandlingProps): UseTextHandlingReturn => {
  const [text, setText] = useState(initialText);
  const [showLimitError, setShowLimitError] = useState(false);

  // Calculate minimum font size as 0.5% of page width
  const minFontSize = Math.max(
    1,
    Math.floor(pdfPageRect.width * MIN_FONT_SIZE_FACTOR)
  );

  const { textareaRef, displayRef, calculateTextFit } = useTextCalculation({
    text,
    isEditMode,
    onErrorChange: setShowLimitError,
    minFontSize,
    maxFontSize: MAX_FONT_SIZE_FACTOR * pdfPageRect.height
  });

  useEffect(() => {
    setText(initialText);
  }, [initialText, isEditMode]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (!textareaRef.current) return;

      // Navigation keys that should not propagate to parent components
      if (NAVIGATION_KEYS.includes(e.key as (typeof NAVIGATION_KEYS)[number])) {
        e.stopPropagation();
      }

      const textarea = e.target as HTMLTextAreaElement;
      const newValue = textarea.value;

      // Check if we're adding text (not deleting)
      const isAddingText = newValue.length > text.length;

      if (isAddingText && textareaRef.current) {
        // Calculate if the new text would fit
        const [_lines, fontSize, isError, _intrinsicTextHeight] =
          calculateTextFit(newValue, textareaRef.current);

        // If we're at minimum font size and the text doesn't fit, don't allow adding more
        if (fontSize === minFontSize && isError) {
          return;
        }
      }

      setText(newValue);
    },
    [text, textareaRef, calculateTextFit, minFontSize]
  );

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      if (!textareaRef.current) return;

      const newValue = e.target.value;

      // Check if we're adding text (not deleting)
      const isAddingText = newValue.length > text.length;

      if (isAddingText) {
        // Calculate if the new text would fit
        const [_lines, fontSize, isError, _intrinsicTextHeight] =
          calculateTextFit(newValue, textareaRef.current);

        // If we're at minimum font size and the text doesn't fit, don't allow adding more
        if (fontSize === minFontSize && isError) {
          return;
        }
      }

      setText(newValue);
    },
    [text, textareaRef, calculateTextFit, minFontSize]
  );

  return {
    text,
    showLimitError,
    handleKeyDown,
    handleInputChange,
    textareaRef,
    displayRef
  };
};
