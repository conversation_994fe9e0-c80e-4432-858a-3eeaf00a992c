import { useEffect, useCallback, useRef } from 'react';
import { countTextLines } from '../utils';

interface UseTextCalculationProps {
  text: string;
  isEditMode: boolean;
  onErrorChange?: (hasError: boolean) => void;
  minFontSize: number;
  maxFontSize: number;
}

export function useTextCalculation({
  text,
  isEditMode,
  onErrorChange,
  minFontSize,
  maxFontSize
}: UseTextCalculationProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const displayRef = useRef<HTMLDivElement>(null);

  const updateFontSize = useCallback(
    (element: HTMLElement | null) => {
      if (!element) return;

      const [lines, fontSize, isError, _intrinsicTextHeight] = countTextLines(
        text,
        {
          width: element.offsetWidth,
          height: element.offsetHeight,
          top: 0,
          left: 0
        },
        minFontSize,
        maxFontSize
      );

      element.style.fontSize = `${fontSize}px`;
      element.style.lineHeight = `${fontSize * 1.2}px`;

      if (onErrorChange) {
        if (isError) {
          element.style.overflow = 'clip';
        }
        onErrorChange(isError);
      }

      return { lines, fontSize, isError };
    },
    [text, onErrorChange, minFontSize, maxFontSize]
  );

  useEffect(() => {
    const element = isEditMode ? textareaRef.current : displayRef.current;
    updateFontSize(element);
  }, [
    text,
    isEditMode,
    textareaRef?.current?.offsetWidth,
    textareaRef?.current?.offsetHeight,
    displayRef?.current?.offsetWidth,
    displayRef?.current?.offsetHeight,
    updateFontSize,
    minFontSize
  ]);

  const calculateTextFit = useCallback(
    (newText: string, element: HTMLElement) => {
      return countTextLines(
        newText,
        {
          width: element.offsetWidth,
          height: element.offsetHeight,
          top: 0,
          left: 0
        },
        minFontSize,
        maxFontSize
      );
    },
    [minFontSize, maxFontSize]
  );

  return {
    textareaRef,
    displayRef,
    updateFontSize,
    calculateTextFit
  };
}
