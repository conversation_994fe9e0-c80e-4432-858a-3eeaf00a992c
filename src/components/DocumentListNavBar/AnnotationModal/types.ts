import { ANNOTATION_TYPES } from '@components/DocumentListNavBar/AnnotationModal/constants.ts';

export interface ICoord {
  top: number;
  left: number;
}
export interface IRect extends ICoord {
  width: number;
  height: number;
}

export interface IRectViewportCoords extends IRect {}
export interface IRectPdfCoords extends IRect {}
export interface IRectRelativePdfCoords extends IRect {}

export interface AnnotationModalProps {
  handleCloseModal: () => void;
  annotationType: (typeof ANNOTATION_TYPES)[keyof typeof ANNOTATION_TYPES];
  semanticPageUuid: string;
}
