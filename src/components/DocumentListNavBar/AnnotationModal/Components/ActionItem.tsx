import React from 'react';
import { Flex } from 'antd';

export interface ActionItemProps {
  onClick?: (e: React.MouseEvent) => void;
  children?: React.ReactNode;
  rightOffset?: number;
  topOffset?: number;
  disabled?: boolean;
  position?: 'absolute' | 'relative';
}

const ActionItem: React.FC<ActionItemProps> = ({
  onClick,
  children,
  rightOffset = 0,
  topOffset = 0,
  position = 'absolute'
}) => {
  return (
    <Flex
      vertical={true}
      justify={'center'}
      align={'center'}
      style={{
        backgroundColor: '',
        height: 'fit-content',
        width: 'fit-content',
        top: topOffset,
        right: rightOffset,
        position: position,
        cursor: 'pointer',
        color: 'black',
        margin: '3px'
      }}
      onClick={onClick}
      onMouseUp={(e: React.MouseEvent) => {
        // prevent the click event from bubbling up and triggering creating a new highlight
        e.stopPropagation();
        e.preventDefault();
      }}
    >
      {children}
    </Flex>
  );
};

export default ActionItem;
