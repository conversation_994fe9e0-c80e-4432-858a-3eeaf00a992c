import React from 'react';
import { ActionItemProps } from '@components/DocumentListNavBar/AnnotationModal/Components/ActionItem.tsx';
import { WHITE_COLOR } from '../../../../constants/theme';
import { Flex } from 'antd';
import styled from 'styled-components';
import { DefaultLightBlueButton } from '@components/Buttons';

export const Button = styled(DefaultLightBlueButton)`
  background: ${(props) => props.theme.colorPrimary};
  border-color: ${(props) => props.theme.colorPrimary};
  color: ${WHITE_COLOR};

  height: 16px;
  margin: 1px;

  padding: 0 9px;

  justify-content: center;
  box-shadow: 2px 2px 4px 1px rgb(0 0 0 / 20%);
  border-radius: 3px;

  .hd-ant-btn-icon {
    font-size: 11px;
  }

  &:hover,
  &:focus,
  &:active {
    cursor: pointer;

    .hd-anticon {
      filter: none;
    }
  }
`;

const SaveAnnotation: React.FC<ActionItemProps> = ({ onClick, disabled }) => {
  return (
    <Flex
      vertical={true}
      justify={'center'}
      align={'center'}
      style={{
        height: 'fit-content',
        width: 'fit-content'
      }}
      onClick={onClick}
      onMouseUp={(e: React.MouseEvent) => {
        if (disabled) {
          return;
        }
        // prevent the click event from bubbling up and triggering creating a new highlight
        e.stopPropagation();
        e.preventDefault();
      }}
    >
      <Button
        style={{ width: 'auto', filter: disabled ? 'grayscale(100%)' : 'none' }}
      >
        <span style={{ fontSize: '10px' }}>ok</span>
      </Button>
    </Flex>
  );
};

export default SaveAnnotation;
