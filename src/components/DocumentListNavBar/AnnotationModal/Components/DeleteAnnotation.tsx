import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrashCan } from '@fortawesome/pro-solid-svg-icons';
import ActionItem, {
  ActionItemProps
} from '@components/DocumentListNavBar/AnnotationModal/Components/ActionItem.tsx';
import { HYPO_DOSSIER_BLUE } from '../../../../constants/theme';
import { ANNOTATION_TYPES } from '../constants';

export interface DeleteAnnotationProps extends ActionItemProps {
  annotationType: string;
  position?: 'absolute' | 'relative';
}

const DeleteAnnotation: React.FC<DeleteAnnotationProps> = ({
  onClick,
  annotationType,
  position
}) => {
  return (
    <ActionItem
      onClick={onClick}
      rightOffset={annotationType === ANNOTATION_TYPES.HIGHLIGHT ? -15 : 0}
      topOffset={0}
      position={position}
    >
      {annotationType === ANNOTATION_TYPES.COMMENT && (
        <FontAwesomeIcon
          icon={faTrashCan}
          style={{
            height: '13px',
            color: HYPO_DOSSIER_BLUE
          }}
        />
      )}
      {annotationType === ANNOTATION_TYPES.HIGHLIGHT && (
        <FontAwesomeIcon
          icon={faTrashCan}
          style={{
            height: '13px',
            color: HYPO_DOSSIER_BLUE,
            backgroundColor: 'rgba(248, 229, 112)',
            border: '1px solid rgba(244, 214, 36)',
            padding: '4px',
            borderRadius: '2px'
          }}
        />
      )}
    </ActionItem>
  );
};

export default DeleteAnnotation;
