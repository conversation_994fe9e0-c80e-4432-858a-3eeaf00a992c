import { AnonymousArrowFunction } from '../../utils/types';
import { IActionDefault } from '../../store/redux/types';
import { IPageObjectUUID, ISemanticPageUUID } from '../DocumentData/types';

export const SET_TOTAL_NET = 'AREA_CALCULATOR/SET_TOTAL_NET';

export type FigureType = 'area' | 'referenceArea' | 'referenceDistance';

export type PointType = Array<Array<number>>;

export interface IAreaCalculateState {
  disableArea: boolean;
  setDisableArea: React.Dispatch<React.SetStateAction<boolean>>;
  areas: IArea[];
  setAreas: (value: ((prevState: IArea[]) => IArea[]) | IArea[]) => void;
  area: IArea;
  setNewArea: (newArea: IArea) => void;
  handleDeleteArea: (id: number) => void;
  handleDeleteAllAreas: () => void;
  reference: IReference;
  setReference: React.Dispatch<React.SetStateAction<IReference>>;
  handleAddButton: AnonymousArrowFunction;
  resetAreaCalculationState: AnonymousArrowFunction;
  onChangeReferenceType: (type: string) => void;
  clearReferenceFigure: AnonymousArrowFunction;
}

export interface AreaCalculatorProps
  extends IPageObjectUUID,
    ISemanticPageUUID {
  semanticPageImage: string;
  visible: boolean;
  scale: number;
  areaCalculateState: IAreaCalculateState;
  canvasRef: React.MutableRefObject<any>;
  floorPlanRef: React.MutableRefObject<any>;
  onOk: AnonymousArrowFunction;
  onCancel: AnonymousArrowFunction;
  setScale: React.Dispatch<React.SetStateAction<number>>;
}

export interface AreaCalculatorContentProps
  extends IPageObjectUUID,
    ISemanticPageUUID {
  canvasRef: React.MutableRefObject<any>;
  floorPlanRef: React.MutableRefObject<any>;
  semanticPageImage: string;
  areaCalculateState: IAreaCalculateState;
  scale: number;
  handleZoom: (zoomValue: number) => void;
  onOk: AnonymousArrowFunction;
  onCancel: AnonymousArrowFunction;
}

export interface IArea {
  id: number;
  type: FigureType | null;
  points: any[];
  init_points: any[];
  flat_points: any[];
  curMousePos: number[];
  center: number[];
  isMouseOverStartPoint: boolean;
  isFinished: boolean;
  area: number;
  overlap: number;
}

export interface AreaProps {
  area: IArea;
  reference?: IReference;
  areaKey: number | null;
  pixelToMeter?: number;
  editArea?: (index: number, editFigure: IArea) => void;
  setArea?: (figure: IArea) => void;
}

export interface ToolsProps extends IPageObjectUUID, ISemanticPageUUID {
  reference: IReference;
  areas: IArea[];
  area: IArea;
  setReferenceValue: (reference: string) => void;
  handleAddButton: AnonymousArrowFunction;
  handleDeleteArea: (id: number) => void;
  handleDeleteAllAreas: () => void;
  setAreas: (value: ((prevState: IArea[]) => IArea[]) | IArea[]) => void;
  handleChangeLayerPosition: (name: string, position: string) => void;
  onChangeReferenceType: (type: string) => void;
  clearReferenceFigure: AnonymousArrowFunction;
  handleZoom: (zoomValue: number) => void;
  onOk: AnonymousArrowFunction;
  onCancel: AnonymousArrowFunction;
}

export interface AreaToolsProps {
  areas: IArea[];
  area: IArea;
  type: FigureType;
  disableArea: boolean;
  activeArea: IArea;
  setActiveArea: (newActiveArea: IArea) => void;
  handleAddArea: (type: FigureType) => void;
  handleDeleteArea: (id: number) => void;
}

export interface ReferenceProps {
  type: FigureType;
  handleAddArea: (type: FigureType) => void;
  referenceValue: string;
  setReferenceValue: (reference: string) => void;
  setOpenReference: (openReference: string) => void;
  openReference: string | null;
  handleDeleteArea: (id: number) => void;
  referenceItem: IArea;
  setActiveArea: (newActiveArea: IArea) => void;
}

export interface ReferenceInputProps {
  background: string;
  valueColor: string;
  title: string;
  measure: string;
}

export interface InstructionsProps {
  reference: IReference;
  activeSelection: string;
}

export interface IReference {
  referenceFigure: IArea | null;
  value: string;
}

export interface AreasListProps extends IPageObjectUUID, ISemanticPageUUID {
  areas: IArea[];
  activeSelection?: string;
  handleDeleteArea: (id: number) => void;
  handleDeleteAllAreas: () => void;
  handleChangeLayerPosition: (name: string, position: string) => void;
}

export interface IImageSize {
  width: number;
  height: number;
}

export interface AreaItemProps {
  index: number;
  id: number;
  areaSize: number;
  areaType: FigureType;
  handleDeleteArea: (id: number) => void;
  handleChangeLayerPosition: (name: string, position: string) => void;
}

export interface IAreaCalculatorSavedState {
  version: string;
  data: IAreaCalculatorData;
}

export interface IAreaCalculatorData {
  areas: PointType[];
  reference: PointType;
  referenceType: FigureType;
  referenceValue: string;
  referenceDistance?: PointType;
  referenceArea?: PointType;
  referenceDistanceValue?: string;
  referenceAreaValue?: string;
}

export interface IScaleOfReferenceProps {
  id: number;
  referenceType: FigureType;
  isFinished: boolean;
  activeSelection: string;
  setActiveSelection: React.Dispatch<React.SetStateAction<string>>;
  onChangeReferenceType: (value: string) => void;
  handleChangeLayerPosition: (name: string, position: string) => void;
  clearReferenceFigure: AnonymousArrowFunction;
}

export interface AreaPopoverProps {
  id: number;
  areaType: FigureType;
  handleChangeLayerPosition: (name: string, position: string) => void;
}

export interface CalculatedArea {
  totalNet: number;
  semanticPageUUID: string;
  pageObjectUUID: string;
}
export interface CalculatedAreaList {
  areaList: CalculatedArea[];
}

export interface ISetNetAreaValue extends IActionDefault {
  payload: CalculatedArea;
}
export type AreaCalculatorActions = ISetNetAreaValue;
