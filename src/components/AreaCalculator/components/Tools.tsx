import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import { useTranslation } from 'react-i18next';
import { Button, InputRef } from 'antd';

import {
  AddAreaButton,
  ReferenceInput,
  ReferenceInputContainer,
  ToolsContainer
} from '../styles';
import { Instructions } from './Instructions';
import { AreasList } from './AreasList';
import { ScaleOfReference } from './ScaleOfReference';
import { setTotalNetAction } from '../actions';

import { ToolsProps } from '../types';
import { useAppDispatch } from '../../../store/app/hooks';
import { ZoomFeature } from '@components/Header/ZoomFeature';
import GreyCircle from './GreyCircle';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRulerTriangle } from '@fortawesome/pro-light-svg-icons';
import { ModalHeaderContainer } from '@components/DocumentListPreview/PagesListViewModal/style.ts';
import { ModalIconWrapper } from '@components/Modal/styles.ts';

const Tools: React.FC<ToolsProps> = React.memo(
  ({
    reference,
    areas,
    area,
    setReferenceValue,
    handleAddButton,
    handleDeleteArea,
    handleDeleteAllAreas,
    onChangeReferenceType,
    handleChangeLayerPosition,
    clearReferenceFigure,
    semanticPageUUID,
    pageObjectUUID,
    handleZoom,
    onOk,
    onCancel
  }) => {
    const { t } = useTranslation();
    const dispatch = useAppDispatch();

    const [activeSelection, setActiveSelection] = useState('');

    const [refDistanceVal, setRefDistanceVal] = useState(reference?.value);
    const [refAreaVal, setRefAreaVal] = useState(reference?.value);

    const referenceDistanceInputRef =
      useRef() as React.MutableRefObject<InputRef>;
    const referenceAreaInputRef = useRef() as React.MutableRefObject<InputRef>;

    const onChangeRefDistanceVal = () => {
      setRefDistanceVal(referenceDistanceInputRef?.current?.input?.value || '');
    };

    const onChangeRefAreaVal = () => {
      setRefAreaVal(referenceAreaInputRef?.current?.input?.value || '');
    };

    const onClearRefDistanceVal = () => {
      setRefDistanceVal('');
    };

    const onClearRefAreaVal = () => {
      setRefAreaVal('');
    };

    const handleClearReferenceFigure = useCallback(() => {
      if (reference?.referenceFigure?.type === 'referenceArea') {
        onClearRefAreaVal();
      } else if (reference?.referenceFigure?.type === 'referenceDistance') {
        onClearRefDistanceVal();
      }
      clearReferenceFigure();
    }, [reference?.referenceFigure?.type]);

    const handleOnChangeReferenceType = (value: string) => {
      setActiveSelection(value);
      onChangeReferenceType(value);
    };

    const onChangeReferenceValue = () => {
      let value;
      if (reference?.referenceFigure?.type === 'referenceArea') {
        value = referenceAreaInputRef?.current?.input?.value;
      } else if (reference?.referenceFigure?.type === 'referenceDistance') {
        value = referenceDistanceInputRef?.current?.input?.value;
      }
      const found = value?.match(/^\d*(\.\d*)?$/);
      if (value) {
        if (found || !value?.length) {
          setReferenceValue(value);
        }
      }
    };

    useEffect(() => {
      onChangeReferenceValue();
    }, [refDistanceVal, refAreaVal]);

    useEffect(() => {
      // Only set initial value or handle reference type changes
      if (
        !activeSelection ||
        (reference?.referenceFigure?.type && !activeSelection) ||
        (!reference?.referenceFigure?.type && activeSelection)
      ) {
        setActiveSelection(reference?.referenceFigure?.type || 'referenceArea');
      }
    }, [reference?.referenceFigure?.type]); // Remove activeSelection from dependencies

    useEffect(() => {
      if (areas.length === 0)
        dispatch(setTotalNetAction(0, semanticPageUUID, pageObjectUUID));
    }, [areas]);

    useEffect(() => {
      if (reference?.value && reference?.value !== '') {
        if (reference?.referenceFigure?.type === 'referenceArea') {
          setRefAreaVal(reference.value);
        }
        if (reference?.referenceFigure?.type === 'referenceDistance') {
          setRefDistanceVal(reference.value);
        }
        setTimeout(() => {
          if (reference?.referenceFigure?.type === 'referenceArea') {
            referenceAreaInputRef.current.input.value = reference.value;
          }
          if (reference?.referenceFigure?.type === 'referenceDistance') {
            referenceDistanceInputRef.current.input.value = reference.value;
          }
        }, 200);
      }
    }, [reference?.value, reference?.referenceFigure?.type]);

    const referenceInputs = useMemo(() => {
      return (
        <>
          {activeSelection === 'referenceDistance' && (
            <div style={{ width: 'fit-content', display: 'inline-block' }}>
              <ReferenceInput
                ref={referenceDistanceInputRef}
                // defaultValue={reference?.value}
                value={refDistanceVal}
                onChange={onChangeRefDistanceVal}
              />
              <span style={{ marginLeft: '5px' }}>m</span>
            </div>
          )}
          {activeSelection === 'referenceArea' && (
            <div style={{ width: 'fit-content', display: 'inline-block' }}>
              <ReferenceInput
                ref={referenceAreaInputRef}
                // defaultValue={reference?.value}
                value={refAreaVal}
                onChange={onChangeRefAreaVal}
              />
              <span style={{ marginLeft: '5px' }}>
                m<sup style={{ lineHeight: 0, fontSize: '9px' }}>2</sup>
              </span>
            </div>
          )}
        </>
      );
    }, [
      refAreaVal,
      refDistanceVal,
      activeSelection,
      reference,
      reference.value,
      activeSelection
    ]);

    const ReferenceAreaInputText = (inputField: React.ReactElement) => (
      <div>
        <span>
          {t('AREA_CALCULATION.REFERENCE_AREA_INPUT_1')} {` `}
        </span>
        <span>
          <b>
            {` `}
            {t('AREA_CALCULATION.REFERENCE_AREA_INPUT_2')}
            {` `}
          </b>
        </span>
        <span>
          {` `}
          {t('AREA_CALCULATION.REFERENCE_AREA_INPUT_3')}
          {` `}
          {inputField}
        </span>
      </div>
    );

    const ReferenceDistanceInputText = (inputField: React.ReactElement) => (
      <div>
        <span>
          {t('AREA_CALCULATION.REFERENCE_DISTANCE_INPUT_1')} {` `}
        </span>
        <span>
          <b>
            {` `}
            {t('AREA_CALCULATION.REFERENCE_DISTANCE_INPUT_2')}
            {` `}
          </b>
        </span>
        <span>
          {` `}
          {t('AREA_CALCULATION.REFERENCE_DISTANCE_INPUT_3')}
          {` `}
          {inputField}
        </span>
      </div>
    );

    const instruction = t('AREA_CALCULATION.FLOOR_PLAN_AREAS', {
      word: 'BREAK_'
    });

    // Split on "{{word}}" placeholder
    const [before, after] = instruction.split('BREAK_');

    const modalIcon = (
      <FontAwesomeIcon
        icon={faRulerTriangle}
        style={{
          height: '20px'
        }}
      />
    );

    const modalTitle = useMemo(() => {
      return (
        <ModalHeaderContainer
          style={{
            paddingRight: 0,
            marginRight: 'auto',
            display: 'flex',
            justifyContent: 'center'
          }}
        >
          <div style={{ marginBottom: '0px' }}>
            <ModalIconWrapper>{modalIcon}</ModalIconWrapper>
            <span>
              <b style={{ fontSize: 16 }}>{t('AREA_CALCULATION.TITLE')}</b>
            </span>
          </div>
        </ModalHeaderContainer>
      );
    }, []);

    return (
      <ToolsContainer>
        <div
          style={{
            display: 'flex',
            justifyContent: 'end',
            marginBottom: '40px'
          }}
        >
          {modalTitle}
          <div style={{ marginRight: -25 }}>
            <ZoomFeature
              handleZoom={handleZoom}
              isModal={true}
              name="area-calculator"
            />
          </div>
        </div>
        <ScaleOfReference
          id={reference?.referenceFigure?.id}
          referenceType={reference?.referenceFigure?.type}
          isFinished={
            reference?.referenceFigure
              ? reference?.referenceFigure?.isFinished &&
                !!reference?.referenceFigure?.points.length
              : false
          }
          onChangeReferenceType={(value: string) =>
            handleOnChangeReferenceType(value)
          }
          handleChangeLayerPosition={handleChangeLayerPosition}
          clearReferenceFigure={handleClearReferenceFigure}
          activeSelection={activeSelection}
          setActiveSelection={setActiveSelection}
        />

        <Instructions reference={reference} activeSelection={activeSelection} />

        <div style={{ display: 'flex', padding: '30px 0px 15px 0px' }}>
          <GreyCircle number={2} />
          <ReferenceInputContainer>
            {activeSelection === 'referenceDistance'
              ? ReferenceDistanceInputText(referenceInputs)
              : ReferenceAreaInputText(referenceInputs)}
          </ReferenceInputContainer>
        </div>
        <div style={{ display: 'flex', padding: '20px 0px 10px 0px' }}>
          <GreyCircle number={3} />
          <div>
            <p style={{ margin: '0px' }}>
              {before}
              <strong>{t('AREA_CALCULATION.FLOOR_PLAN_AREAS_BOLD')}</strong>
              {after}
            </p>
          </div>
        </div>
        <AddAreaButton
          type="primary"
          onClick={handleAddButton}
          disabled={
            (area?.isFinished === false && area?.type === 'area') ||
            (reference?.referenceFigure &&
              !reference?.referenceFigure?.isFinished) ||
            ((!refAreaVal || refAreaVal === '') &&
              (!refDistanceVal || refDistanceVal === ''))
          }
        >
          {`+ ${t('AREA_CALCULATION.ADD_AREA')}`}
        </AddAreaButton>
        {areas.length < 1 && (
          <div
            style={{
              color: '#ae0295',
              marginBottom: '10px',
              fontStyle: 'italic',
              textAlign: 'center'
            }}
          >
            <div>{t('AREA_CALCULATION.ADD_AREA_INSTRUCTIONS_1')}</div>
            <div>{t('AREA_CALCULATION.ADD_AREA_INSTRUCTIONS_2')}</div>
          </div>
        )}
        {areas.length > 0 && (
          <AreasList
            areas={areas}
            handleDeleteArea={handleDeleteArea}
            handleDeleteAllAreas={handleDeleteAllAreas}
            handleChangeLayerPosition={handleChangeLayerPosition}
            semanticPageUUID={semanticPageUUID}
            pageObjectUUID={pageObjectUUID}
            activeSelection={activeSelection}
          />
        )}

        {/* <div>{t('AREA_CALCULATION.DEFINE_AREAS_AS_POLYGONS')}</div> */}
        {(area?.type === 'referenceArea' || area?.type === 'area') &&
          area?.isFinished && (
            <div style={{ color: '#ae0295', fontStyle: 'italic' }}>
              {t('AREA_CALCULATION.PLEASE_MIND')}
            </div>
          )}

        <div
          style={{
            marginTop: '20px',
            display: 'flex',
            justifyContent: 'space-between'
          }}
        >
          <div style={{ marginRight: '20px', width: '100%' }}>
            <Button
              key="close"
              onClick={onCancel}
              className="cancel-button"
              style={{ width: '100%' }}
            >
              {' '}
              {t('CANCEL')}
            </Button>
          </div>
          <div style={{ width: '100%' }}>
            <Button
              key="submit"
              type="primary"
              onClick={onOk}
              autoFocus
              className="save-button"
              style={{ width: '100%' }}
            >
              {t('SAVE')}
            </Button>
          </div>
        </div>
      </ToolsContainer>
    );
  }
);

export { Tools };
