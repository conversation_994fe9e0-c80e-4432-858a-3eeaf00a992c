import { ConfigProvider, Modal } from 'antd';
import { cdpLanguage } from '../../types';

import { getTranslatedRecommendationTitle } from '../../utils';
import { RecommendationModalContent } from './RecommendationModalContent';
import { ErrorBoundary } from './ErrorBoundary';
import { setFieldValueCallbackFunction } from '../../types.ts';
interface RecommendationModalProps {
  token?: string;
  onClose?: () => void;
  cdpBackendUrl: string;
  requestId?: string;
  fieldName?: string;
  fieldContext?: any;
  selectedValue?: string | null;
  isHandEdited?: boolean;
  uiLanguage?: cdpLanguage;
  handleUseValue?: setFieldValueCallbackFunction;
}

export function RecommendationModal(props: RecommendationModalProps) {
  const translatedTitle = getTranslatedRecommendationTitle(
    props.fieldName,
    props.uiLanguage
  );

  return (
    <div
      data-testid="cdp-iframe-modal"
      style={{
        width: '100%',
        height: '100%',
        padding: '0px',
        backgroundColor: 'rgba(0, 0, 0, 0.4)'
      }}
    >
      <ConfigProvider
        theme={{
          components: {
            Modal: {
              titleFontSize: 18, // overridden by global setting of 14px foor fontSize. 16 is default for Modals
              titleLineHeight: 1.0 // 1.5 is default
            }
          }
        }}
      >
        <Modal
          title={translatedTitle ? translatedTitle.toUpperCase() : ''}
          open={true}
          onOk={() => props.onClose()}
          onCancel={() => props.onClose()}
          width={900}
          style={{ top: 40, maxHeight: '93vh' }}
          footer={null}
        >
          <ErrorBoundary
            key={props.fieldName}
            fallback={<div>Error inside IFrame, please try again later.</div>}
          >
            <RecommendationModalContent
              cdpBackendUrl={props.cdpBackendUrl}
              fieldName={props.fieldName}
              fieldContext={props.fieldContext}
              selectedValue={props.selectedValue}
              isHandEdited={props.isHandEdited}
              uiLanguage={props.uiLanguage}
              setFieldValueCallback={props.handleUseValue}
              token={props.token}
            />
          </ErrorBoundary>
        </Modal>
      </ConfigProvider>
    </div>
  );
}
