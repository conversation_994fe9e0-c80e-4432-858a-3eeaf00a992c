import { <PERSON><PERSON>, Collapse, ConfigProvider } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import type { UnifiedRecommendationResponse } from '../../../../gen/cdp';
import type {
  RecommendationRow,
  cdpLanguage,
  setFieldValueCallbackFunction
} from '../../types';
import { useGroupedRecommendations } from './hooks';
import { useHasScrollbar } from '@components/cdp/hooks';
import { getDocumentPanelKey } from './utils';
import { SourcePreview } from './SourcePreview/SourcePreview';
import { Recommendation } from './Recommendation';
import { StyledRecommendationsContainer, StyledValueGroup } from './styles';
import { CDPModalRecommendationsContext } from './context';
import { useModalContext } from '../Wrapper/ModalContext';

interface RecommendationsProps {
  hdfFieldId: string;
  setFieldValueCallback?: setFieldValueCallbackFunction;
  uiLanguage?: cdpLanguage;
  selectedValue?: string | null;
  isHandEdited?: boolean;
  recommendations: UnifiedRecommendationResponse[];
}

const isFirstSourceForSingleRecommendation = (
  groupIndex: number,
  sourceIndex: number,
  firstSourceDocumentKey: string,
  isSingleRecommendation: boolean
): boolean =>
  groupIndex == 0 &&
  sourceIndex === 0 &&
  !firstSourceDocumentKey &&
  isSingleRecommendation;

const Recommendations: React.FC<RecommendationsProps> = (props) => {
  const uiLanguage = props.uiLanguage || 'en';
  const { t } = useTranslation();
  const { recommendations, hdfFieldId } = props;

  // Use context instead of props
  const { state: modalState } = useModalContext();
  const { previewMode } = modalState;

  const {
    groupedRecommendations,
    isSingleRecommendation,
    openedSourceDocuments,
    firstSourceDocumentKey,
    setFirstSourceDocumentKey,
    handleSourceDocumentChange,
    state,
    dispatch
  } = useGroupedRecommendations(recommendations, uiLanguage, hdfFieldId);

  const { hasScrollbar, containerRef } = useHasScrollbar();

  // If there are no recommendations, show an alert
  if (recommendations.length === 0) {
    return (
      <Alert
        message={t('CDP.ALERT.MESSAGE', { lng: uiLanguage })}
        description={t('CDP.ALERT.DESCRIPTION', { lng: uiLanguage })}
        type="warning"
        showIcon
      />
    );
  }

  const numRecommendationsLength = Object.keys(groupedRecommendations).length;

  // If there are recommendations, show the accordion
  // NB: when Collapse Panel is moved to separate component, accordion stopped working
  return (
    <CDPModalRecommendationsContext.Provider value={{ value: state, dispatch }}>
      <StyledRecommendationsContainer ref={containerRef}>
        {Object.keys(groupedRecommendations).map((groupValue, groupIndex) => (
          <StyledValueGroup key={groupValue}>
            <ConfigProvider
              theme={{
                components: {
                  Collapse: {
                    contentPadding: '6px 0px', // horizontal padding
                    headerPadding: '0px 0px' // 12px 16px by default
                  }
                }
              }}
            >
              <Collapse
                ghost
                bordered={false}
                onChange={handleSourceDocumentChange}
                activeKey={openedSourceDocuments}
                expandIcon={() => null}
                style={{
                  marginBottom:
                    groupIndex === numRecommendationsLength - 1 ? 0 : 20
                }}
              >
                {groupedRecommendations[groupValue].map(
                  (row: RecommendationRow, sourceIndex: number) => {
                    if (
                      isFirstSourceForSingleRecommendation(
                        groupIndex,
                        sourceIndex,
                        firstSourceDocumentKey,
                        isSingleRecommendation
                      )
                    ) {
                      setFirstSourceDocumentKey(row.key);
                    }
                    return (
                      <Collapse.Panel
                        data-testid="cdp-accordion-source"
                        header={
                          <Recommendation
                            initialValue={groupValue}
                            row={row}
                            maxLength={state.maxLength}
                            minLength={state.minLength}
                            selectedValue={props.selectedValue}
                            isHandEdited={props.isHandEdited}
                            setFieldValueCallback={props.setFieldValueCallback}
                            open={openedSourceDocuments.includes(
                              getDocumentPanelKey(row.key)
                            )}
                            hasScrollbar={hasScrollbar}
                          />
                        }
                        key={getDocumentPanelKey(row.key)}
                        style={{
                          marginTop: 0,
                          marginBottom: 0,
                          padding: 0,
                          border: 'none'
                        }}
                      >
                        <div style={{ marginTop: -8, marginBottom: -10 }}>
                          <SourcePreview
                            row={row}
                            openedSourceDocuments={openedSourceDocuments}
                            selectCallback={
                              previewMode
                                ? null
                                : () =>
                                    props.setFieldValueCallback(
                                      groupValue,
                                      groupedRecommendations[groupValue][0]
                                        ?.preview
                                        ? {
                                            imageUrl:
                                              groupedRecommendations[
                                                groupValue
                                              ][0]?.preview.url,
                                            bbox: groupedRecommendations[
                                              groupValue
                                            ][0]?.preview.bbox,
                                            rotationAngle:
                                              groupedRecommendations[
                                                groupValue
                                              ][0]?.rotationAngle
                                          }
                                        : null,
                                      groupedRecommendations[groupValue][0]
                                        ?.type,
                                      {}
                                    )
                            }
                          />
                        </div>
                      </Collapse.Panel>
                    );
                  }
                )}
              </Collapse>
            </ConfigProvider>
          </StyledValueGroup>
        ))}
      </StyledRecommendationsContainer>
    </CDPModalRecommendationsContext.Provider>
  );
};

export default Recommendations;
