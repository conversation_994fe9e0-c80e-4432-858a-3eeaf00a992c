export interface ValueRendererProps {
  setRawValue: (value: string | number | boolean) => void;
  setValue: (value: string | number | boolean) => void;
  rawValue: string | number | boolean;
  value: string | number | boolean;
  value_prefix?: string;
  value_suffix?: string;
  type: string;
  maxLength?: number;
  minLength?: number;
  isHover: boolean;
  selectValueCallback?: (e: any) => void;
  rowKey: string;
  editing: boolean;
  setEditing: (value: boolean) => void;
  previewOnly?: boolean;
}
