import { queryOptions, useQuery } from '@tanstack/react-query';
import { Divider } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CdpClient } from '../../gen/cdp';

import { dmsClient } from '../../apis/dmsAccountClient.ts';

import { cdpLanguage } from './types';

import {
  LanguageUpdateMessageSchema,
  MessageType,
  NumRecommendationsRequestMessageSchema,
  RecommendationRequestMessageSchema,
  TokenRefreshedMessageSchema
} from '@components/cdp/messagingProtocol/messageTypes.ts';

import { useRecommendationsCountQuery } from './Form/RecommendationsForm/hooks.tsx';
import { ModalType, SourceDocumentPreview } from './types';

import { useRecommendationsCountQueue } from './hook-queue.ts';
import { ENABLE_NUM_RECOMMENDATIONS_REQUEST_QUEUE } from './featureFlags.ts';
import { useIframeToCDPFormMessages } from './messagingProtocol/IframeToCDPForm.hooks.ts';

// add to global window object
export function useEventListener(type: string, ev: EventListener) {
  return useEffect(() => {
    window.addEventListener(type, ev);
    return () => {
      window.removeEventListener(type, ev);
    };
  }, []);
}

export const useCdpMessageAdapter = () => {
  const [token, setToken] = useState<string | null>(null);
  const [fieldName, setFieldName] = useState<string | null>(null);
  const [selectedValue, setSelectedValue] = useState<any | null>(null);
  const [requestId, setRequestId] = useState<string | null>(null);
  const [uiLanguage, setUiLanguage] = useState<cdpLanguage>('en');
  const [fieldDefinitions, setFieldDefinitions] = useState<Array<string>>([]);
  const [isHandEdited, setIsHandEdited] = useState<boolean>(false);
  const [modalType, setModalType] = useState<ModalType>(
    ModalType.RECOMMENDATION
  );
  const [fieldContext, setFieldContext] = useState<object>({});

  const iframeUrlParams = new URLSearchParams(window.location.search);
  const cdpFrontendUrl = decodeURIComponent(
    iframeUrlParams.get('cdpFrontendUrl')
  );
  const cdpBackendUrl = decodeURIComponent(
    iframeUrlParams.get('cdpBackendUrl')
  );

  // message target - client app url
  const messageTarget = cdpFrontendUrl;

  const {
    sendCloseMessage,
    sendIframeReadyMessage,
    sendRecommendationSelectedMessage,
    sendNumRecommendationsResponseMessage
  } = useIframeToCDPFormMessages(messageTarget, requestId, fieldName);

  // message queue for recommendations count
  const { addToNumRecommendationsQueue } = useRecommendationsCountQueue(
    token,
    cdpBackendUrl,
    sendNumRecommendationsResponseMessage
  );

  useEventListener(
    'message',
    (
      message: MessageEvent<
        | TokenRefreshedMessageSchema
        | RecommendationRequestMessageSchema
        | LanguageUpdateMessageSchema
        | NumRecommendationsRequestMessageSchema
      >
    ) => {
      switch (message.data.type) {
        case MessageType.TOKEN_REFRESHED:
          setToken(message.data.payload.token);
          break;
        case MessageType.RECOMMENDATION_REQUESTED:
          setFieldName(message.data.payload.fieldName);
          setRequestId(message.data.payload.requestId);
          setSelectedValue(message.data.payload.selectedValue || null);
          setIsHandEdited(message.data.payload.isHandEdited || false);
          setModalType(ModalType.RECOMMENDATION);
          setFieldContext(message.data.payload.fieldContext || {});
          break;
        case MessageType.LANGUAGE_UPDATED:
          setUiLanguage(message.data.payload.language);
          break;
        case MessageType.NUM_RECOMMENDATIONS_REQUESTED:
          const fieldDefinitions = message.data.payload.field_definitions;
          const fieldContext = message.data.payload.field_context;
          const timestamp = message.data.payload.timestamp;
          const requestUuid = crypto.randomUUID();

          // add request to the queue
          if (ENABLE_NUM_RECOMMENDATIONS_REQUEST_QUEUE)
            addToNumRecommendationsQueue({
              requestUuid,
              fieldDefinitions,
              fieldContext,
              timestamp
            });
          // old way to process request via context & field definitions state change to triger useEffect
          else handleGetNumRecommendations(fieldDefinitions, fieldContext);
          break;
      }
    }
  );

  // next effect after adding event listeners, sending handshake message at the start & on token & language change
  useEffect(() => {
    // TODO: send that iframe is ready only when token also was received
    sendIframeReadyMessage();
  }, [token, fieldName, requestId, uiLanguage]);

  const { recommendationsCount } = useRecommendationsCountQuery(
    fieldDefinitions,
    token,
    cdpBackendUrl, // hypodossierBackendUrl,
    fieldContext
  );

  useEffect(() => {
    // send message with updated recommendations count
    // TODO: check for change and send only if changed, Redux like
    if (recommendationsCount)
      sendNumRecommendationsResponseMessage(recommendationsCount);
  }, [recommendationsCount]);

  // standard handlers
  const handleGetNumRecommendations = (
    fieldDefinitions: Array<string>,
    fieldContext?: object
  ) => {
    // TODO: implement queue to handle all new requests
    setFieldDefinitions(fieldDefinitions);
    setFieldContext(fieldContext || {});
  };

  // TODO: add extras from type - currency etc.
  const handleUseValue = useCallback(
    (
      value: any,
      preview: SourceDocumentPreview,
      type: string,
      payload: object,
      handEdited?: boolean
    ) => {
      // 1) set the field value
      sendRecommendationSelectedMessage(
        value,
        preview,
        type,
        fieldName,
        payload,
        handEdited
      );
      // 2) close the modal
      sendCloseMessage();
    },
    [sendCloseMessage, requestId, fieldName, messageTarget]
  );
  return {
    token,
    fieldName,
    fieldContext,
    selectedValue,
    requestId,
    uiLanguage,
    cdpBackendUrl,
    handleUseValue,
    sendCloseMessage,
    isHandEdited,
    modalType
  };
};

export function useRecommendations(
  cdpBackendUrl: string,
  fieldName: string,
  fieldContext: object,
  token: string | null
) {
  const cdpApi = new CdpClient({
    BASE: cdpBackendUrl,
    HEADERS: {
      Authorization: `Bearer ${token}`
    }
  });
  return useQuery(
    queryOptions({
      queryKey: ['recommendation', fieldName],
      enabled: token != null && fieldName != null,
      queryFn: () =>
        cdpApi.default.recommendationPost({
          field_definition: fieldName,
          field_context: fieldContext
        })
    })
  );
}

export function useGroupedRecommendations(
  cdpBackendUrl: string,
  fieldName: string,
  fieldContext: object,
  token: string | null
) {
  const cdpApi = new CdpClient({
    BASE: cdpBackendUrl,
    HEADERS: {
      Authorization: `Bearer ${token}`
    }
  });
  return useQuery(
    queryOptions({
      queryKey: ['recommendation', fieldName],
      queryFn: () =>
        cdpApi.default.groupedRecommendationPost({
          field_definition: fieldName,
          field_context: fieldContext
        }),
      enabled: token != null && fieldName != null
    })
  );
}

export function useFormatFormatPageNumbers(
  pageNumber: number,
  totalPages: number,
  uiLanguage
) {
  const { t } = useTranslation();
  const renderedPageNumber = pageNumber + 1;

  return (
    <Divider plain>
      {t('PAGE', { lng: uiLanguage })} {renderedPageNumber} / {totalPages}{' '}
      {t('PAGES', { lng: uiLanguage })}
    </Divider>
  );
}

export function useCdpDossierToken(dossierUuid: string) {
  const HYPODOSSIER_TOKEN_REFRESH_INTERVAL = 1000 * 60 * 8; // 8 minutes with 10 minutes token validity time
  const HYPODOSSIER_TOKEN_STALE_TIME = 1000 * 60 * 5; // 5 minutes with 10 minutes token validity time
  const HYPODOSSIER_TOKEN_CACHE_TIME = 1000 * 60 * 9; // 9 minutes with 10 minutes token validity time

  const dossierToken = useQuery(
    queryOptions({
      queryKey: ['dossierToken', dossierUuid],
      queryFn: () =>
        dmsClient.default.dossierApiGetDossierGrantToken(dossierUuid),
      refetchInterval: HYPODOSSIER_TOKEN_REFRESH_INTERVAL,
      staleTime: HYPODOSSIER_TOKEN_STALE_TIME,
      gcTime: HYPODOSSIER_TOKEN_CACHE_TIME,
      enabled: dossierUuid != null
    })
  );

  return dossierToken;
}

export const useHasScrollbar = () => {
  const [hasScrollbar, setHasScrollbar] = useState(false);
  const containerRef = useRef(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const resizeObserver = new ResizeObserver(() => {
      const hasVerticalScrollbar =
        container.scrollHeight > container.clientHeight;
      setHasScrollbar(hasVerticalScrollbar);
    });

    resizeObserver.observe(container);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return { hasScrollbar, containerRef };
};
