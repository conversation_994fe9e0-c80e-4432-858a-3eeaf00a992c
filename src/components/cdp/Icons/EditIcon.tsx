import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPenToSquare } from '@fortawesome/pro-solid-svg-icons';

import {
  HYPO_DOSSIER_GREY_PALETTE,
  HYPO_DOSSIER_BLUE
} from '../../../constants/theme';

interface Props {
  isEmpty: boolean;
  isHover: boolean;
  previewOnly?: boolean;
}
export const EditIcon: React.FC<Props> = ({
  isEmpty,
  isHover,
  previewOnly
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => setIsHovered(false);

  return (
    <FontAwesomeIcon
      icon={faPenToSquare}
      style={{
        height: '14px',
        cursor: previewOnly ? 'not-allowed' : 'pointer',
        color: previewOnly
          ? HYPO_DOSSIER_GREY_PALETTE.LIGHT_GREY
          : isHovered
            ? HYPO_DOSSIER_BLUE
            : HYPO_DOSSIER_GREY_PALETTE.MEDIUM_GREY
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    />
  );
};
