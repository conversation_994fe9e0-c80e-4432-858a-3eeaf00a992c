import React from 'react';
import styled from 'styled-components';
import { HYPPO_CDP_DOSSIER_DARK_GREEN } from '../../../constants/theme.ts';
interface ButtonProps {
  $size: string;
  $margin: string;
  onClick: (e: React.MouseEvent) => void;
  $isHover?: boolean;
}
const HandSelectionIconButton = styled.button<ButtonProps>`
  height: ${(props) => props.$size};
  width: ${(props) => props.$size};
  border-radius: 50%;
  margin-left: ${(props) => props.$margin};
  margin-top: -3px;
  color: ${HYPPO_CDP_DOSSIER_DARK_GREEN};
  background-color: ${(props) => (props.$isHover ? '#f4fbfe' : 'white')};
  border: none;
  border-image-width: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  & > svg {
    stroke: transparent;
    stroke-width: 0px;
  }
  &:hover {
    cursor: pointer;
    border-style: solid;
    border-width: 0px;
    border-color: ${HYPPO_CDP_DOSSIER_DARK_GREEN};
  }
`;

interface Props {
  size?: number;
  margin?: string;
  onClick: (e: React.MouseEvent) => void;
  isHover?: boolean;
}

export const HandSelectionIcon: React.FC<Props> = (props) => {
  const basicSize = 24;
  const size = props.size || basicSize;
  const margin = props.margin || '10px';

  const basicScale = 17;
  const scale = props.size ? (basicSize / props.size) * basicScale : basicScale;
  const width = 202 / scale;
  const height = 319 / scale;

  return (
    <HandSelectionIconButton
      $size={`${size}px`}
      $margin={margin}
      onClick={props.onClick}
      $isHover={props.isHover}
      data-testid="hand-selection-icon"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
        viewBox="0 0 202 319"
        style={{
          shapeRendering: 'geometricPrecision',
          textRendering: 'geometricPrecision',
          fillRule: 'evenodd',
          clipRule: 'evenodd'
        }}
        xmlnsXlink="http://www.w3.org/1999/xlink"
        stroke="transparent"
        fill="currentColor"
      >
        <g>
          <path
            style={{
              opacity: 1
            }}
            fill="currentColor"
            d="M 83.5,5.5 C 89.2912,4.48885 94.1245,6.15551 98,10.5C 103.833,20.8333 109.667,31.1667 115.5,41.5C 117.585,36.5813 120.919,32.7479 125.5,30C 130.44,28.9595 135.106,29.6261 139.5,32C 142.629,35.7522 145.129,39.9188 147,44.5C 149.462,53.1468 151.795,61.8135 154,70.5C 159.199,67.1452 164.699,66.6452 170.5,69C 173.106,70.9384 174.939,73.4384 176,76.5C 178.605,105.435 180.272,134.435 181,163.5C 184.027,169.765 185.36,176.431 185,183.5C 185.73,222.374 187.23,261.207 189.5,300C 188.984,304.044 186.984,307.044 183.5,309C 157.167,309.667 130.833,309.667 104.5,309C 102.714,308.215 101.214,307.049 100,305.5C 99.2375,302.53 97.7375,300.03 95.5,298C 80.6191,290.397 66.9525,281.064 54.5,270C 40.3621,256.863 26.5288,243.363 13,229.5C 12,227.5 11,225.5 10,223.5C 10.38,206.839 11.0467,190.172 12,173.5C 13.4134,169.761 15.2467,166.261 17.5,163C 16.0535,159.081 16.2202,155.248 18,151.5C 29.1958,139.97 40.6958,128.803 52.5,118C 56.749,116.874 61.0823,116.208 65.5,116C 78.2636,115.586 90.9303,115.419 103.5,115.5C 91.1923,88.0781 80.359,60.0781 71,31.5C 69.576,20.0176 73.7427,11.3509 83.5,5.5 Z"
          />
        </g>
        <g>
          <path
            style={{
              opacity: 1
            }}
            fill="#dff1e4"
            d="M 85.5,14.5 C 86.8734,14.3433 88.2068,14.51 89.5,15C 104.846,41.8574 119.012,69.3574 132,97.5C 146,122.167 160,146.833 174,171.5C 175.876,214.482 177.376,257.482 178.5,300.5C 154.831,300.667 131.164,300.5 107.5,300C 106.355,296.71 104.688,293.71 102.5,291C 90.6868,285.261 79.6868,278.261 69.5,270C 58.2008,260.369 47.2008,250.369 36.5,240C 30.634,234.469 25.134,228.635 20,222.5C 19.2377,205.748 19.9043,189.081 22,172.5C 22.804,171.196 23.9707,170.529 25.5,170.5C 35.545,173.89 41.3784,180.89 43,191.5C 43.3333,197.167 43.6667,202.833 44,208.5C 52.3583,213.187 59.8583,219.02 66.5,226C 78.0548,225.491 89.3881,223.491 100.5,220C 107.594,215.903 113.761,210.736 119,204.5C 121.341,195.933 120.674,187.6 117,179.5C 108.845,169.346 100.012,159.846 90.5,151C 80.1667,150.333 69.8333,150.333 59.5,151C 50.9784,160.045 40.645,163.379 28.5,161C 26.6707,159.887 25.8374,158.387 26,156.5C 35.3449,145.989 45.1782,135.989 55.5,126.5C 70.1061,124.557 84.7728,124.057 99.5,125C 113.764,136.593 127.431,148.927 140.5,162C 146.268,166.879 152.602,170.712 159.5,173.5C 159.159,170.484 157.993,167.818 156,165.5C 144.387,152.554 132.054,140.221 119,128.5C 105.551,97.4868 92.8843,66.1535 81,34.5C 80.3333,29.8333 80.3333,25.1667 81,20.5C 82.332,18.34 83.832,16.34 85.5,14.5 Z"
          />
        </g>
        <g>
          <path
            style={{
              opacity: 1
            }}
            fill="#dff1e4"
            d="M 128.5,38.5 C 131.325,38.0807 133.491,39.0807 135,41.5C 136.333,44.8333 137.667,48.1667 139,51.5C 144.968,73.364 149.968,95.364 154,117.5C 143.159,98.6432 133.159,79.3099 124,59.5C 122.291,51.6783 123.791,44.6783 128.5,38.5 Z"
          />
        </g>
        <g>
          <path
            style={{
              opacity: 1
            }}
            fill="#dff1e4"
            d="M 160.5,76.5 C 164.162,75.6371 166.328,76.9704 167,80.5C 169.146,101.455 170.146,122.455 170,143.5C 165.82,123.414 161.32,103.414 156.5,83.5C 157.071,80.6873 158.404,78.354 160.5,76.5 Z"
          />
        </g>
        <g>
          <path
            style={{
              opacity: 1
            }}
            fill="white"
            d="M 62.5,160.5 C 70.84,160.334 79.1733,160.5 87.5,161C 96.162,169.318 103.662,178.484 110,188.5C 110.667,192.5 110.667,196.5 110,200.5C 99.4212,210.874 86.5879,216.207 71.5,216.5C 65.5401,212.18 59.5401,207.846 53.5,203.5C 53.7955,191.591 50.4621,180.925 43.5,171.5C 47.8644,169.767 52.1977,167.933 56.5,166C 58.9962,164.669 60.9962,162.836 62.5,160.5 Z"
          />
        </g>
      </svg>
    </HandSelectionIconButton>
  );
};
