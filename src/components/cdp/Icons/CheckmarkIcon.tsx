import React from 'react';
import styled from 'styled-components';
import {
  HYPO_DOSSIER_GREY_PALETTE,
  HYPPO_CDP_DOSSIER_DARK_GREEN,
  HYPPO_CDP_DOSSIER_LIGHT_GREEN,
  HYPO_DOSSIER_BLUE
} from '../../../constants/theme.ts';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleCheck } from '@fortawesome/pro-solid-svg-icons';

interface ButtonProps {
  $size: string;
  $margin: string;
  $green: boolean;
  onClick: (e: React.MouseEvent) => void;
  $isHover?: boolean;
  $disabled?: boolean;
}

const RecommendationsSelectedValueButton = styled.button<ButtonProps>`
  // background-color: ${HYPPO_CDP_DOSSIER_DARK_GREEN};
  background-color: ${(props) =>
    props.$green ? HYPPO_CDP_DOSSIER_LIGHT_GREEN : 'white'};
  border-radius: 50%;
  border: none;
  height: ${(props) => props.$size};
  width: ${(props) => props.$size};
  margin-left: ${(props) => props.$margin};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &:hover {
    cursor: pointer;
  }
`;

const calculateButtonColor = (
  green: boolean,
  isHover: boolean,
  disabled: boolean
) => {
  if (disabled) return 'white';
  if (green) return HYPPO_CDP_DOSSIER_DARK_GREEN;
  if (isHover) return HYPO_DOSSIER_BLUE;
  return HYPO_DOSSIER_GREY_PALETTE.MEDIUM_GREY;
};

const calculateButtonBackgroundColor = (green: boolean, disabled: boolean) => {
  if (disabled) return HYPO_DOSSIER_GREY_PALETTE.LIGHT_GREY;
  if (green) return HYPPO_CDP_DOSSIER_LIGHT_GREEN;

  return HYPO_DOSSIER_GREY_PALETTE.LIGHT_GREY;
};

/*
green - true: light green + dark green bg
green - false: white + light grey bg

*/

const RecommendationsNonSelectedValueButton = styled.button<ButtonProps>`
  background-color: ${(props) =>
    calculateButtonBackgroundColor(props.$green, props.$disabled || false)};
  color: ${(props) =>
    calculateButtonColor(
      props.$green,
      props.$isHover,
      props.$disabled || false
    )};
  border-radius: 50%;
  border: none;
  height: ${(props) => props.$size};
  width: ${(props) => props.$size};
  margin-left: ${(props) => props.$margin};
  margin-top: ${(props) => (props.$green ? 0 : '-3px')};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: ${(props) => (props.$disabled ? 'not-allowed' : 'pointer')};
  &:hover {
    cursor: ${(props) => (props.$disabled ? 'not-allowed' : 'pointer')};
    background-color: ${(props) =>
      props.$green
        ? HYPPO_CDP_DOSSIER_DARK_GREEN
        : props.$disabled
          ? 'white'
          : HYPO_DOSSIER_GREY_PALETTE.LIGHT_GREY};
    color: ${(props) =>
      props.$green
        ? HYPPO_CDP_DOSSIER_LIGHT_GREEN
        : props.$disabled
          ? HYPO_DOSSIER_GREY_PALETTE.LIGHT_GREY
          : HYPO_DOSSIER_BLUE};
    border: 0px solid ${HYPO_DOSSIER_GREY_PALETTE.LIGHT_GREY};
  }
`;

interface Props {
  size?: number;
  selected: boolean;
  margin?: string;
  green?: boolean;
  onClick: (e: React.MouseEvent) => void;
  isHover?: boolean;
  previewOnly?: boolean;
  testId?: string;
}

const calculateColor = (
  isValueSelected: boolean,
  green: boolean,
  isHover: boolean
) => {
  if (isValueSelected) {
    return green
      ? HYPPO_CDP_DOSSIER_LIGHT_GREEN
      : isHover
        ? '#f4fbfe'
        : 'white';
  }
  return green ? HYPPO_CDP_DOSSIER_LIGHT_GREEN : 'white';
};

export const CheckmarkIcon: React.FC<Props> = (props) => {
  const basicSize = 24;
  const size = props.size || basicSize;
  const margin = props.margin || '10px';
  const green = props.green || false;

  const isValueSelected = props.selected;

  // TODO: create subcomponent - CheckmarkIconSelected
  if (isValueSelected)
    return (
      <FontAwesomeIcon
        onClick={props.previewOnly ? null : props.onClick}
        icon={faCircleCheck}
        transform="grow-1.25"
        style={{
          color: props.previewOnly
            ? 'white'
            : calculateColor(isValueSelected, green, props.isHover),
          backgroundColor: props.previewOnly
            ? HYPO_DOSSIER_GREY_PALETTE.MEDIUM_GREY
            : HYPPO_CDP_DOSSIER_DARK_GREEN,
          borderRadius: '50%',
          marginLeft: margin,
          height: size,
          width: size,
          cursor: props.previewOnly ? 'not-allowed' : 'pointer',
          border: 'none'
        }}
        data-testid={props.testId || 'checkmark-icon'}
        data-active={props.previewOnly ? 'false' : 'true'}
      />
    );

  // TODO: create subcomponent - CheckmarkIconUnselected
  return (
    <RecommendationsNonSelectedValueButton
      $size={`${size - 3}px`}
      $margin={margin}
      onClick={props.previewOnly ? null : props.onClick}
      $green={green}
      $isHover={props.isHover}
      $disabled={props.previewOnly}
      data-testid={props.testId || 'checkmark-icon'}
      data-active={props.previewOnly ? 'false' : 'true'}
    >
      <FontAwesomeIcon
        icon={faCircleCheck}
        style={{
          height: size - 3,
          width: size - 3,
          marginTop: 0
        }}
      />
    </RecommendationsNonSelectedValueButton>
  );
};
