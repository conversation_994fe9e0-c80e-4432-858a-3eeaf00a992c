import { useMemo } from 'react';
import { useQuery, queryOptions } from '@tanstack/react-query';
import {
  CdpClient,
  NumRecommendationsResponse,
  Date
} from '../../../../gen/cdp';
import { getFieldValueForRecommendation } from '../../utils';
import { RecommendationsCountMap, RecommendationCountData } from './types';
import { formatDate } from '../../formatters';
import { formatFieldId } from './utils';
import { getGroupContext } from '@components/cdp/Form/RecommendationsForm/reducer';
import type { RecommendationsFormState } from './reducer';

interface UseRecommendationsCount {
  recommendationsCount: RecommendationsCountMap;
}

export const useRecommendationsCountQuery = (
  fieldSet: Array<string>,
  authToken: string,
  hypodossierBackendUrl: string,
  fieldContext: object
): UseRecommendationsCount => {
  const emptyAnswer = {
    recommendationsCount: null
  } as UseRecommendationsCount;

  const enabled =
    authToken != null && hypodossierBackendUrl != null && fieldSet.length > 0;

  const cdpApi = new CdpClient({
    BASE: hypodossierBackendUrl,
    HEADERS: {
      Authorization: `Bearer ${authToken}`
    }
  });

  // generate a unique key for the query
  const fieldSetName = fieldSet.join(',');
  const fieldContextKey = JSON.stringify(fieldContext);

  const fieldsCounts = useQuery(
    queryOptions({
      queryKey: ['hdfFieldsCounts', fieldSetName, fieldContextKey],
      queryFn: () =>
        cdpApi.default.numRecommendationsPost({
          field_definitions: fieldSet,
          field_context: fieldContext
        }),
      refetchInterval: 5 * 60 * 1000,
      enabled: enabled
    })
  );

  // return null to use as loading state in the UI
  if (fieldsCounts.isLoading || fieldsCounts.isError) {
    return emptyAnswer;
  }

  const data = fieldsCounts?.data as NumRecommendationsResponse;
  const recommendationsCount = formatRecommendationsCount(data);

  return { recommendationsCount };
};

// format data helper
export const formatRecommendationsCount = (
  data: NumRecommendationsResponse
): RecommendationsCountMap => {
  const recommendationsCount = {} as RecommendationsCountMap;
  try {
    if (data)
      Object.keys(data).forEach((hdfRecommendationId) => {
        const fieldData = data[hdfRecommendationId];
        const type = fieldData?.single_recommendation?.field_value?.return_type;

        const fieldRecommendationCountData = {
          total: fieldData?.total_recommendation_count,
          unique: fieldData?.grouped_sppo_recommendation_count,
          hints: fieldData?.hint_recommendation_count,
          singleRecommendation: fieldData?.is_single_recommendation,
          singleRecommendationValue:
            type == 'date'
              ? formatDate(
                  (fieldData?.single_recommendation?.field_value as Date).value
                )
              : getFieldValueForRecommendation(
                  fieldData?.single_recommendation?.field_value,
                  hdfRecommendationId
                ).value,
          singleRecommendationSource: fieldData?.single_recommendation?.source,
          fieldContext: fieldData?.field_context
        } as RecommendationCountData;
        recommendationsCount[hdfRecommendationId] =
          fieldRecommendationCountData;
      });
  } catch (e) {
    console.warn('Error in processing CDP data (recommendations counts)', e);
  }

  return recommendationsCount;
};

export const useCustomGroupContext = (
  CDPFormState: RecommendationsFormState,
  group: string
) => {
  const groupContext = useMemo(
    () => getGroupContext(CDPFormState, group),
    [
      CDPFormState.fields[formatFieldId(group, 'hdf_person_firstname')]?.value,
      CDPFormState.fields[formatFieldId(group, 'hdf_person_lastname')]?.value,
      CDPFormState.fields[formatFieldId(group, 'hdf_person_street')]?.value,
      CDPFormState.fields[formatFieldId(group, 'hdf_property_street')]?.value,
      group
    ]
  );
  return groupContext;
};
