// import type { RecommendationsFormState } from './types';
import { RefObject } from 'react';
import type { SourceDocumentPreview } from '../../types';
import { RecommendationCountData, HintSourceDocumentPreview } from './types';
import { FieldStatus } from '../../types';

import type { FormFields } from '../FieldsController/types';
import { formatFieldId, formatGroupName } from './utils';
import { allowedGroupContextKeys } from '../../utils';
// import { CDPIframeMessageSystem } from '../tools/CDP.iframe.tools.module';
import { CDPFormToIframeMessagingFunctions } from '@components/cdp/messagingProtocol/messageTypes';

export type FieldState = {
  fieldId: string;
  group: string;
  hdfRecommendationId: string;
  value: string;
  type?: string; // FIXME: add enum
  status: FieldStatus;
  justSelected: boolean;
  recommendationsCountData: RecommendationCountData | null;
  documentPreview: SourceDocumentPreview | null;
  hintPreview: HintSourceDocumentPreview | null;
};

const initialFieldState: FieldState = {
  fieldId: '',
  group: '',
  hdfRecommendationId: '',
  value: '',
  status: FieldStatus.INITIAL,
  justSelected: false,
  type: 'UNKNOWN',
  recommendationsCountData: null,
  documentPreview: null,
  hintPreview: null
};

// TODO: implement field reducer for managing field state
const fieldReducer = (state: FieldState = initialFieldState, action: any) => {
  switch (action.type) {
    case 'REMOVE_JUST_SELECTED':
      return {
        ...state,
        justSelected: false
      };
    case 'SELECT': {
      const { value, documentPreview, type, handEdited, hdfFieldId, payload } =
        action.payload;
      return {
        ...state,
        value: value,
        type: type,
        justSelected: true,
        documentPreview: handEdited ? null : documentPreview, // set preview when value is selected
        status: handEdited
          ? FieldStatus.HAND_EDITED_OR_VERIFIED
          : FieldStatus.SELECTED
      };
    }
    case 'EDIT': {
      const { value } = action.payload;

      return {
        ...state,
        value: value,
        documentPreview: null, // clear preview if the value was edited
        hintPreview: null, // clear hint preview if the value was edited
        status:
          value == ''
            ? FieldStatus.INITIAL
            : FieldStatus.HAND_EDITED_OR_VERIFIED
      };
    }
    case 'VERIFY': {
      const { documentPreview } = action.payload;
      // act like selection
      return {
        ...state,
        justSelected: true,
        documentPreview: documentPreview,
        status: FieldStatus.SELECTED
      };
    }
    case 'SET_TYPE': {
      const { type } = action.payload;
      return {
        ...state,
        type: type
      };
    }
    case 'SET_RECOMMENDATIONS_COUNT': {
      const recommendationsCountData: RecommendationCountData =
        action.payload.recommendationsCountData;

      const DEFAULT_SOURCE_INDEX = 0;
      const sourceDocument =
        recommendationsCountData?.singleRecommendationSource
          ?.source_document_details[DEFAULT_SOURCE_INDEX];
      const pageIndex = sourceDocument?.page_index_to_scroll;

      const documentPreview = {
        imageUrl: sourceDocument?.semantic_pages[pageIndex]?.image_url,
        bbox: sourceDocument?.semantic_pages[pageIndex]?.page_objects[0]?.bbox,
        rotationAngle: sourceDocument?.semantic_pages[pageIndex]?.rotation_angle
      } as SourceDocumentPreview;

      // one hint case
      if (
        recommendationsCountData?.singleRecommendation &&
        recommendationsCountData?.hints === 1
      ) {
        const hintPreview = {
          pages: sourceDocument?.semantic_pages,
          preferred_page_index: pageIndex
        } as HintSourceDocumentPreview;

        return {
          ...state,
          hintPreview,
          recommendationsCountData: recommendationsCountData
        };
      }
      // one recommendation - prefill case
      // set value in the form only if it's empty for single recommendation
      // check if the prefilled value is the same so we can update image link
      if (!state.value && recommendationsCountData?.singleRecommendation) {
        const firstValue = recommendationsCountData?.singleRecommendationValue;
        return {
          ...state,
          value: firstValue,
          status: FieldStatus.PREFILLED,
          documentPreview,
          recommendationsCountData: recommendationsCountData
        };
      }
      // just update preview
      else if (
        state.value == recommendationsCountData?.singleRecommendationValue &&
        recommendationsCountData?.singleRecommendation
      ) {
        return {
          ...state,
          documentPreview,
          recommendationsCountData: recommendationsCountData
        };
      } else {
        const hasNoRecommendations = recommendationsCountData?.unique === 0;

        if (hasNoRecommendations) {
          return {
            ...state,
            status: FieldStatus.NO_RECOMMENDATIONS,
            recommendationsCountData: recommendationsCountData
          };
        } else
          return {
            ...state,
            recommendationsCountData: recommendationsCountData
          };
      }
    }
    default:
      return state;
  }
};

export type FieldStateFlags = Record<string, FieldStatus>;
export type FormFieldsState = Record<string, FieldState>;

export type RecommendationsFormState = {
  dossierUuid: string | null;
  formFields: FormFields;
  language: string | null;
  // fields
  fields: FormFieldsState;
  // iframe state machine
  iFrameInDOM: boolean;
  iFrameSentReadyMessage: boolean;
  iFrameReady: boolean;
  authTokenSent: boolean;
  numRecommendationsRequested: boolean;
  numRecommendationsReceived: boolean;
  // iframe ref
  iframeRef: RefObject<HTMLIFrameElement>;
  formCleared: boolean; // to prevent fetching num_recommendations
  previewMode: boolean; // to prevent editing fields
};

// initial state generator
const initNewField = (
  fieldId: string,
  group: string,
  hdfRecommendationId: string
): FieldState => {
  return {
    ...initialFieldState,
    fieldId: fieldId,
    group: group,
    hdfRecommendationId: hdfRecommendationId
  } as FieldState;
};
const generateInitialFields = (formFields: FormFields): FormFieldsState => {
  const fields: FormFieldsState = {};
  for (const group of Object.keys(formFields)) {
    for (const hdfRecommendationId of Object.keys(formFields[group])) {
      const fieldId = formatFieldId(group, hdfRecommendationId);
      fields[fieldId] = initNewField(fieldId, group, hdfRecommendationId);
    }
  }
  return fields;
};

export const dossierInitialState = ({
  dossierUuid,
  formFields
}: {
  dossierUuid: string;
  formFields: FormFields;
}): RecommendationsFormState => {
  const loadedFormFieldsState = loadFromLocalStorage(
    getFullCdpStorageKey(dossierUuid)
  );

  // TODO: sync with backend for what values were selected and not edited

  // instantiate MessageSystem
  // const messageSystem = new CDPIframeMessageSystem();

  for (const group of Object.keys(formFields)) {
    for (const hdfRecommendationId of Object.keys(formFields[group])) {
      const fieldId = formatFieldId(group, hdfRecommendationId);
      if (!loadedFormFieldsState[fieldId]) {
        loadedFormFieldsState[fieldId] = initNewField(
          fieldId,
          group,
          hdfRecommendationId
        );
      }
    }
  }

  return {
    dossierUuid: dossierUuid,
    language: 'en',
    formFields: formFields,
    // fields state
    fields: { ...loadedFormFieldsState },
    //iframe state
    iFrameInDOM: false,
    iFrameSentReadyMessage: false,
    iFrameReady: false,
    authTokenSent: false,
    numRecommendationsRequested: false,
    numRecommendationsReceived: false,
    iframeRef: null,
    formCleared: false,
    previewMode: false
  };
};

// usage
// const [state, dispatch] = useReducer(recommendationsFormReducer, dossierUuid, dossierInitialState);

// helper functions
const getFullCdpStorageKey = (dossierUuid: string) =>
  'cdp_form_' + dossierUuid + '_full';

const clearLocalStorage = (key: string) => {
  localStorage?.setItem(key, JSON.stringify({} as any));
};

const saveToLocalStorage = (key: string, data: any) => {
  localStorage?.setItem(key, JSON.stringify(data));
};

const loadFromLocalStorage = (key: string): any => {
  const data = localStorage?.getItem(key);
  return data ? JSON.parse(data) : {};
};

const isIframeReady = (isReadyMessageSent: boolean, isIframeInDOM: boolean) =>
  isReadyMessageSent && isIframeInDOM;

/*
Reducer to manage the state of the RecommendationsForm component
Actions:
- UPDATE_FORM_FIELDS
- SEND_AUTH_TOKEN
- SEND_LANGUAGE
- HANDLE_IFRAME_READY
- HANDLE_IFRAME_IN_DOM
- UPDATE_FIELD_VALUE
- GET_NUM_RECOMMENDATIONS
- HANDLE_NUM_RECOMMENDATIONS_RESPONSE
- HANDLE_RECOMMENDATION_SELECTED
- CLEAR_FORM
...
- HEARTBEAT_IFRAME // TODO: (to check that the connection is alive)
*/

export const createRecommendationsFormReducer =
  (messagingFunctions: CDPFormToIframeMessagingFunctions) =>
  (state: RecommendationsFormState, action: any) => {
    switch (action.type) {
      case 'UPDATE_FORM_FIELDS':
        const { formFields } = action.payload;
        const loadedFormFieldsState = state.fields;
        // TODO: extract function to reuse on init too
        for (const group of Object.keys(formFields)) {
          for (const hdfRecommendationId of Object.keys(formFields[group])) {
            const fieldId = formatFieldId(group, hdfRecommendationId);
            if (!loadedFormFieldsState[fieldId]) {
              loadedFormFieldsState[fieldId] = initNewField(
                fieldId,
                group,
                hdfRecommendationId
              );
            }
          }
        }

        return {
          ...state,
          formFields: formFields,
          // fields state
          fields: { ...loadedFormFieldsState },
          //iframe state
          numRecommendationsRequested: false,
          numRecommendationsReceived: false
        };
      case 'SEND_AUTH_TOKEN': {
        const { authToken } = action.payload;
        messagingFunctions.sendAuthToken(state.iframeRef, authToken);

        return {
          ...state,
          authTokenSent: true
        };
      }
      case 'SEND_LANGUAGE': {
        const { language } = action.payload;
        messagingFunctions.sendLanguage(state.iframeRef, language);

        return { ...state, language };
      }
      case 'HANDLE_IFRAME_IN_DOM': {
        // save iframe ref to the state
        const { iframeRef } = action.payload;
        return {
          ...state,
          iFrameInDOM: true,
          iFrameReady: isIframeReady(state.iFrameSentReadyMessage, true),
          iframeRef: iframeRef
        };
      }
      case 'HANDLE_IFRAME_READY':
        return {
          ...state,
          iFrameSentReadyMessage: true,
          iFrameReady: isIframeReady(true, state.iFrameInDOM)
        };
      case 'HANDLE_NUM_RECOMMENDATIONS_RESPONSE': {
        // fieldContext is added to the recommendationsCount item
        const recommendationsCount = action.payload;
        let updatedFields = {
          ...state.fields
        };

        // set recommendations count for each field & set first value if it's single recommendation inside field reducer
        for (const hdfRecommendationId of Object.keys(recommendationsCount)) {
          // TODO: filter out only for specific group
          const groupId =
            recommendationsCount[hdfRecommendationId]?.fieldContext?.groupId;

          Object.keys(state.fields).forEach((fieldId) => {
            const hdfFieldId = state.fields[fieldId].hdfRecommendationId;
            const fieldGroup = state.fields[fieldId].group;
            if (hdfFieldId !== hdfRecommendationId) return;
            if (groupId && fieldGroup !== groupId) {
              return;
            }
            const action = {
              type: 'SET_RECOMMENDATIONS_COUNT',
              payload: {
                recommendationsCountData: recommendationsCount[hdfFieldId]
              }
            };
            updatedFields = {
              ...updatedFields,
              [fieldId]: fieldReducer(state.fields[fieldId], action)
            };
          });
        }

        saveToLocalStorage(
          getFullCdpStorageKey(state.dossierUuid),
          updatedFields
        );

        return {
          ...state,
          fields: { ...updatedFields },
          numRecommendationsReceived: true,
          isIframeReady: isIframeReady(true, state.iFrameInDOM) // as we got some message form iFrame
        };
      }
      case 'HANDLE_RECOMMENDATION_SELECTED': {
        // use hdfFieldId and payload to be processed based on hdfFieldId
        const hdfFieldId = action.payload.hdfFieldId;
        const payload = action.payload.payload;
        const fieldId = action.payload.requestId;
        const value = action.payload.recommendationValue;
        const type = action.payload.type;
        const previewImageUrl = action.payload?.previewImageUrl;
        const previewBbox = action.payload?.previewBbox;
        const previewRotationAngle = action.payload?.previewRotationAngle;
        const handEdited = action.payload.handEdited;

        const hasPreview = !!previewImageUrl && !!previewBbox;
        const documentPreview = hasPreview
          ? ({
              imageUrl: previewImageUrl,
              bbox: previewBbox,
              rotationAngle: previewRotationAngle
            } as SourceDocumentPreview)
          : null;

        const field = fieldId;
        let updatedFields = {
          ...state.fields,
          [field]: fieldReducer(state.fields[field], {
            type: 'SELECT',
            payload: {
              value,
              type,
              documentPreview,
              handEdited,
              hdfFieldId,
              payload
            }
          })
        };
        // fill street number if street name is selected
        if (
          field.includes('hdf_person_street') ||
          field.includes('hdf_property_street')
        ) {
          const streetNoFieldId = field + '_no';
          const suffixValue = payload?.value_suffix || '';
          if (suffixValue !== '')
            updatedFields = {
              ...updatedFields,
              [streetNoFieldId]: fieldReducer(state.fields[streetNoFieldId], {
                type: 'SELECT',
                payload: {
                  value: suffixValue,
                  type,
                  documentPreview,
                  handEdited,
                  hdfFieldId,
                  payload
                }
              })
            };
        }

        saveToLocalStorage(
          getFullCdpStorageKey(state.dossierUuid),
          updatedFields
        );

        return {
          ...state,
          fields: { ...updatedFields }
        };
      }
      case 'REMOVE_JUST_SELECTED': {
        const fieldId = action.payload.fieldId;
        const field = fieldId;
        const updatedFields = {
          ...state.fields,
          [field]: fieldReducer(state.fields[field], {
            type: 'REMOVE_JUST_SELECTED',
            payload: {}
          })
        };

        saveToLocalStorage(
          getFullCdpStorageKey(state.dossierUuid),
          updatedFields
        );

        return {
          ...state,
          fields: { ...updatedFields }
        };
      }
      case 'HANDLE_VALIDATE_SINGLE_VALUE': {
        if (checkPreviewMode(state)) return state; // do not edit in preview mode
        // validation act like selection
        const { fieldId, documentPreview } = action.payload;

        const field = fieldId;
        const updatedFields = {
          ...state.fields,
          [field]: fieldReducer(state.fields[field], {
            type: 'VERIFY',
            payload: { documentPreview }
          })
        };

        saveToLocalStorage(
          getFullCdpStorageKey(state.dossierUuid),
          updatedFields
        );

        return {
          ...state,
          fields: { ...updatedFields }
        };
      }
      case 'UPDATE_FIELD_VALUE':
        if (checkPreviewMode(state)) return state; // do not edit in preview mode
        const { fieldId, value } = action.payload;
        const field = fieldId;
        const updatedFields = {
          ...state.fields,
          [field]: fieldReducer(state.fields[field], {
            type: 'EDIT',
            payload: { value }
          })
        };

        saveToLocalStorage(
          getFullCdpStorageKey(state.dossierUuid),
          updatedFields
        );

        return {
          ...state,
          fields: { ...updatedFields }
        };
      case 'GET_NUM_RECOMMENDATIONS': {
        const { iframeRef, fieldSet, fieldContext } = action.payload;

        if (state.formCleared) return state;
        const timestamp = Date.now();
        messagingFunctions.requestNumRecommendations(
          state.iframeRef,
          fieldSet,
          fieldContext,
          timestamp
        );

        return {
          ...state,
          numRecommendationsRequested: true
        };
      }
      case 'CLEAR_FORM':
        if (checkPreviewMode(state)) return state; // do not edit in preview mode
        clearLocalStorage(getFullCdpStorageKey(state.dossierUuid));

        return {
          ...state,
          numRecommendationsRequested: false,
          numRecommendationsReceived: false,
          formCleared: true,
          fields: generateInitialFields(state.formFields) // use reinit function for fields
        };
      case 'TOGGLE_PREVIEW_MODE': {
        const newPreviewMode = !state.previewMode;

        // Use messaging functions from state
        if (state.iframeRef?.current) {
          messagingFunctions.sendPreviewMode(state.iframeRef, newPreviewMode);
        }
        return {
          ...state,
          previewMode: newPreviewMode
        };
      }
      default:
        return state;
    }
  };

// selectors

// return group context for the group
export const getGroupContext = (
  state: RecommendationsFormState,
  group: string
) => {
  // get group context from the state
  // find all fields for this group
  const groupFields = Object.keys(state.fields).filter((fieldId) =>
    fieldId.includes(group)
  );
  // map fields to hdfRecommendationId -> value map
  const groupContextFields = groupFields.reduce(
    (acc, fieldId) => {
      const field = state.fields[fieldId];
      const hdfRecommendationId = field.hdfRecommendationId;
      // filter for only available fields for group context and non-empty
      if (
        !allowedGroupContextKeys.has(hdfRecommendationId) ||
        field.value == ''
      )
        return acc;

      acc[hdfRecommendationId] = field.value;
      return acc;
    },
    {} as Record<string, string>
  );
  // get all field contexts
  const groupContext = {
    groupName: formatGroupName(group),
    groupId: group,
    ...groupContextFields
  };
  return groupContext;
};

export const checkPreviewMode = (state: RecommendationsFormState) => {
  if (state.previewMode) {
    console.warn('Update blocked: Preview mode active');
    return true;
  }
  return false;
};
