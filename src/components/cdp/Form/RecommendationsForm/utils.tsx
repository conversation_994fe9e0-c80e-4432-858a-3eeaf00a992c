import { RecommendationsCountMap } from './types';
import { FIELDS_NAMES_ALL } from '../FieldsController/constants';
import { HDF_FIELDS_TYPES } from '../FieldsController/fieldTypes';
import { rightAlignSet, rightAlignExceptionsSet } from './constants';

// TODO: add translations for group names ('en' | 'de' | 'fr' | 'it')
const formatGroupName = (groupName: string) =>
  groupName
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

const formatFieldId = (group: string, fieldName: string) =>
  `${group}_${fieldName}`;

const getEmptyRecommendationsSet = (
  recommendationsCount: RecommendationsCountMap
) => {
  const emptyRecommendationsSet = new Set();
  Object.keys(recommendationsCount).forEach((hdfRecommendationId) => {
    if (recommendationsCount[hdfRecommendationId]?.total === 0) {
      emptyRecommendationsSet.add(hdfRecommendationId);
    }
  });
  return emptyRecommendationsSet;
};

const generateDefaultLabel = (fieldName: string) =>
  fieldName
    .split('_')
    .slice(1)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

const generateFieldLabel = (fieldName: string, language: string) => {
  // use default German translations
  if (language === 'de') return FIELDS_NAMES_ALL[fieldName];

  // use default English translations from the field name
  switch (fieldName) {
    default:
      return generateDefaultLabel(fieldName);
  }
};

const calculateTextAlign = (hdfRecommendationId: string): 'left' | 'right' => {
  const type = HDF_FIELDS_TYPES[hdfRecommendationId] || 'string';

  return rightAlignSet.has(type) ||
    rightAlignExceptionsSet.has(hdfRecommendationId)
    ? 'right'
    : 'left';
};

// '20250226T130000Z' -> '2025-02-26T13:00:00.000Z'
function convertToISO8601(basicISOString: string) {
  // Validate input format
  const regex = /(\d{4})(\d{2})(\d{2})T(\d{2})(\d{2})(\d{2})Z/;
  const match = basicISOString.match(regex);

  if (!match) {
    throw new Error('Invalid input format. Expected YYYYMMDDTHHMMSSZ');
  }

  // Extract parts
  const [, year, month, day, hours, minutes, seconds] = match;

  // Construct standard ISO 8601 format
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`;
}

const isImageLinkExpired = (previewImageUrl: string) => {
  try {
    // extract params from the link
    const previewUrl = new URL(previewImageUrl);
    // date and time in ISO 8601 without separators format, needs conversion to JS ISO 8601
    const createdAt = previewUrl.searchParams.get('X-Amz-Date');
    const ttlSeconds = parseInt(previewUrl.searchParams.get('X-Amz-Expires'));

    let expirationTimestamp = Date.parse(convertToISO8601(createdAt));
    expirationTimestamp += ttlSeconds * 1000;
    return Date.now() > expirationTimestamp;
  } catch (e) {
    // if parsing failed, consider provided URL a local one used in tests and return non-expired result
    return false;
  }
};

export {
  formatGroupName,
  formatFieldId,
  getEmptyRecommendationsSet,
  generateFieldLabel,
  calculateTextAlign,
  isImageLinkExpired
};
