import {
  UnifiedRecommendationSource,
  SourceSemanticPageDetails
} from '../../../../gen/cdp';

import type { SourceDocumentPreview } from '../../types';

export type FormValues = Record<string, string>;
export type FieldsFlags = Record<string, boolean>;

export interface HintSourceDocumentPreview {
  pages: SourceSemanticPageDetails[];
  preferred_page_index: number;
}

export type FieldsSourcePreviews = Record<string, SourceDocumentPreview>;
export interface RecommendationCountData {
  total: number;
  unique: number;
  hints: number;
  singleRecommendation: boolean;
  singleRecommendationValue: string;
  singleRecommendationSource?: UnifiedRecommendationSource;
  fieldContext?: object;
}

export type RecommendationsCountMap = Record<string, RecommendationCountData>;
