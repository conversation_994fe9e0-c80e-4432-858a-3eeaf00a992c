import { Form, Input, Space, Tooltip } from 'antd';
import React, { useContext, useEffect, useRef } from 'react';

import {
  formatFieldId,
  generateFieldLabel,
  calculateTextAlign
} from './utils.tsx';

import { RecommendationsFormCounterButton } from './RecommendationsFormCounterButton.tsx';
import {
  CDPFormStateContext,
  CDPFormDispatchContext,
  CDPFormHandlersContext
} from './context.ts';

import { FieldStatus } from '../../types.ts';
import { HDF_FIELDS_TYPES } from '../FieldsController/fieldTypes';
import { formatValue } from '../../formatters.tsx';
import { RecommendationsFormCounterPlaceholder } from './styles.ts';
import { useCustomGroupContext } from './hooks';

interface Props {
  group: string;
  hdfRecommendationId: string;
}

export const RecommendationsFormField: React.FC<Props> = (props) => {
  const { group, hdfRecommendationId } = props;
  const fieldRef = useRef(null);

  const fieldId = formatFieldId(group, hdfRecommendationId);
  const CDPFormState = useContext(CDPFormStateContext);
  const fieldState = CDPFormState.fields[fieldId];

  const value = fieldState?.value;
  const CDPFormHandlers = useContext(CDPFormHandlersContext);
  const dispatch = useContext(CDPFormDispatchContext);
  const language = CDPFormState.language;
  const previewMode = CDPFormState.previewMode;

  const isRecommendationUnique =
    fieldState?.recommendationsCountData &&
    fieldState?.recommendationsCountData?.unique == 1;

  const recommendationsNumber = fieldState?.recommendationsCountData
    ? fieldState?.recommendationsCountData?.unique
    : undefined;

  const hasRecommendations =
    CDPFormState.numRecommendationsReceived &&
    fieldState?.recommendationsCountData?.unique > 0;

  const isHandEdited =
    fieldState?.status === FieldStatus.HAND_EDITED_OR_VERIFIED;
  const isPrefilled = fieldState?.status === FieldStatus.PREFILLED;
  const isValueSelected = fieldState?.status === FieldStatus.SELECTED;

  // group context (person's name, surname, etc.)
  const fieldContext = useCustomGroupContext(CDPFormState, group);

  // TODO: use isPrefilled as param to getRecommendations to avoid selecting the recommendation
  const getRecommendations = () => {
    CDPFormHandlers.handleGetRecommendations(
      fieldId,
      hdfRecommendationId,
      isValueSelected ? value : null, // send selected value only if status is SELECTED
      isHandEdited,
      fieldContext
    );
  };

  const type = fieldState?.type;
  // FIXME: use different feature flag
  // const prefix = VALIDATION_IN_POPOVER ? (type == 'currency' ? 'CHF' : '') : '';
  const prefix = '';

  const handleUpdateFormValues = (e: any) => {
    dispatch({
      type: 'UPDATE_FIELD_VALUE',
      payload: { fieldId, value: e.target.value }
    });
  };

  const cancelFocus = () => {
    if (fieldRef.current) {
      fieldRef.current.blur();
    }
  };

  useEffect(() => {
    if (fieldState?.justSelected) {
      setTimeout(
        () =>
          dispatch({
            type: 'REMOVE_JUST_SELECTED',
            payload: { fieldId }
          }),
        200
      );
    }
  }, [fieldState?.justSelected]);

  const textAlign = calculateTextAlign(hdfRecommendationId);

  const formattedValue =
    HDF_FIELDS_TYPES[hdfRecommendationId] == 'currency'
      ? formatValue(value, 'currency').toString()
      : value;

  const renderingOptions = {
    formattedValue:
      HDF_FIELDS_TYPES[hdfRecommendationId] == 'currency'
        ? formatValue(value, 'currency').toString()
        : value
  };
  const fieldLabel = generateFieldLabel(hdfRecommendationId, language);

  return (
    <Form.Item
      key={fieldId}
      label={
        <Tooltip title={fieldLabel}>
          <span
            style={{
              maxWidth: 210,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              display: 'inline-block'
            }}
          >
            {fieldLabel}
          </span>
        </Tooltip>
      }
    >
      <Space>
        <Input
          ref={fieldRef}
          width="100%"
          style={{
            fontSize: 13,
            textAlignLast: textAlign,
            direction: value == '' && textAlign == 'right' ? 'rtl' : 'ltr', // cursor position fix for empty value with right align
            borderColor: fieldState?.justSelected ? 'green' : null,
            cursor: previewMode ? 'not-allowed' : 'text'
          }}
          prefix={prefix}
          id={fieldId}
          data-testid={`cdp-form-input-${fieldId}`}
          // placeholder={generateFieldLabel(hdfRecommendationId, language)} //FIELD_TRANSLATIONS[item][language]
          value={renderingOptions.formattedValue}
          onChange={(e: any) =>
            dispatch({
              type: 'UPDATE_FIELD_VALUE',
              payload: { fieldId, value: e.target.value }
            })
          }
          onFocus={previewMode ? cancelFocus : undefined}
          // disabled={previewMode}
        />{' '}
        {hasRecommendations && (
          <RecommendationsFormCounterButton
            recommendationsNumber={recommendationsNumber}
            isHandSelected={isHandEdited}
            isValueSelected={isValueSelected}
            isUnique={isRecommendationUnique}
            isPrefilled={isPrefilled}
            getRecommendations={getRecommendations}
            previewSource={fieldState.documentPreview}
            hintSource={fieldState.hintPreview}
            fieldId={fieldId}
          />
        )}
        {!hasRecommendations && <RecommendationsFormCounterPlaceholder />}
      </Space>
    </Form.Item>
  );
};
