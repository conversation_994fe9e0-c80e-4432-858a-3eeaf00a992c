import { Typo<PERSON>, Row, Col } from 'antd';
import React, { useContext, useEffect } from 'react';
import type { FormFields } from '../FieldsController/types';

import { formatGroupName, formatFieldId } from './utils';

import { RecommendationsFormField } from './RecommendationsFormField';
import { CDPFormDispatchContext, CDPFormStateContext } from './context';

import { HYPO_DOSSIER_GREY_PALETTE } from '../../../../constants/theme';
import { GROUP_CARD_WIDTH } from '../constants';
import { useCustomGroupContext } from './hooks';
import { UnderscoreSeparatedStrings } from '@components/cdp/types';

const { Text } = Typography;

interface Props {
  groupId: UnderscoreSeparatedStrings;
  formFields: FormFields;
}

export const RecommendationsFormGroup: React.FC<Props> = (props) => {
  const { groupId, formFields } = props;

  let borderColor = '#000';
  switch (groupId) {
    case 'Person_1':
      borderColor = '#80b6d1';
      break;
    case 'Person_2':
      borderColor = '#cc91a2';
      break;
    case 'Objekt':
      borderColor = '#89b360';
      break;
    default:
      borderColor = HYPO_DOSSIER_GREY_PALETTE.DARK_GREY;
      break;
  }

  const CDPFormState = useContext(CDPFormStateContext);
  const groupName = formatGroupName(groupId);

  // group context (person's name, surname, etc.)
  const groupContext = useCustomGroupContext(CDPFormState, groupId);

  const dispatch = useContext(CDPFormDispatchContext);

  // 3. Get recommendations for entire form
  useEffect(() => {
    dispatch({
      type: 'GET_NUM_RECOMMENDATIONS',
      payload: {
        iframeRef: undefined,
        fieldSet: Object.keys(formFields[groupId]),
        fieldContext: { ...groupContext, groupId, group: groupName } // TODO: add filtering to context fields
      }
    });
  }, [JSON.stringify(groupContext), formFields]); // TODO: check useCustomGroupContext to return new object ref when context changes to trigger request

  return (
    <div
      key={groupId}
      style={{
        marginRight: '20px',
        marginTop: '20px',
        padding: '12px 24px 8px 24px',
        width: GROUP_CARD_WIDTH,
        border: `1px solid ${borderColor}`,
        borderRadius: 8
      }}
    >
      <Row justify="start">
        <Col
          span={12}
          style={{
            width: '100%',
            display: 'flex',
            flexDirection: 'row-reverse',
            justifyItems: 'end',
            paddingRight: 13,
            marginBottom: 8
          }}
        >
          <div>
            <Text
              style={{
                color: borderColor
              }}
            >
              {formatGroupName(groupId).toUpperCase()}
            </Text>
          </div>
        </Col>
      </Row>
      {Object.keys(formFields[groupId]).map((hdfRecommendationId) => (
        <RecommendationsFormField
          key={hdfRecommendationId}
          fieldId={formatFieldId(groupId, hdfRecommendationId)}
          hdfRecommendationId={hdfRecommendationId}
          fieldContext={groupContext}
        />
      ))}
    </div>
  );
};
