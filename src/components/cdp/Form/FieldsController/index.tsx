import { Col, Row, Divider, Grid } from 'antd';
import { useReducer, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { RecommendationsForm } from '../RecommendationsForm';
import { FIELDS_ALL, PILOT_FIELDS_SET } from './constants';
import { RecommendationsFormFieldsSelector } from './RecommendationsFormFieldsSelector';
import { fieldsInitialState, formFieldsReducer } from './reducer';
import { FieldSet } from './types';
import { GROUP_CARD_WIDTH } from '../constants';
import { PREVIEW_MODE_SWITCH_ENABLED_IN_FORM } from '@components/cdp/featureFlags';

const { useBreakpoint } = Grid;

interface Props {
  iframeRef: React.RefObject<HTMLIFrameElement>;
  handleGetRecommendations: (
    sourceId: string,
    fieldName: string,
    selectedValue?: string,
    isHandEdited?: boolean,
    fieldContext?: object
  ) => void;
  authToken: string;
  dossierUuid: string;
  iframeInitialized: boolean;
}

export const FormFieldsController: React.FC<Props> = ({
  iframeRef,
  handleGetRecommendations,
  dossierUuid,
  authToken,
  iframeInitialized
}) => {
  const screens = useBreakpoint();
  const { i18n } = useTranslation();
  const language = i18n.language;
  // 2. form fields list reducer
  const [formFieldsState, formFieldsDispatch] = useReducer(
    formFieldsReducer,
    {
      allFields: FIELDS_ALL,
      filterFieldSet: PILOT_FIELDS_SET,
      selectedFieldSet: FieldSet.PILOT
    },
    fieldsInitialState
  );

  // field set selector at the top of the Form
  const handleSelect = useCallback((selectedFieldSet: FieldSet) => {
    formFieldsDispatch({
      type: 'SET_SELECTED_FIELD_SET',
      payload: {
        selectedFieldSet
      }
    });
  }, []);

  return (
    <Row gutter={[0, 16]} style={{ marginTop: 0 }}>
      <Col
        style={{
          width: screens.xl
            ? 2 * (GROUP_CARD_WIDTH + 20) + 2
            : GROUP_CARD_WIDTH + 20
        }}
      >
        <RecommendationsForm
          key={language}
          iframeRef={iframeRef}
          formFields={formFieldsState.filteredFields}
          formFieldsSet={formFieldsState.filterFieldSet}
          iframeInitialized={iframeInitialized}
          dossierUuid={dossierUuid}
          handleGetRecommendations={handleGetRecommendations}
          language={language}
          authToken={authToken}
        />
      </Col>
      <Col span={3}>
        <Row
          style={{
            marginTop: PREVIEW_MODE_SWITCH_ENABLED_IN_FORM ? 20 + 22 : 20,
            marginLeft: 16
          }}
        >
          <Col offset={0} span={24}>
            <RecommendationsFormFieldsSelector
              selectedFieldSet={formFieldsState.selectedFieldSet}
              language={language}
              handleSelect={handleSelect}
            />
          </Col>
        </Row>
      </Col>

      <Divider orientation="center"></Divider>
    </Row>
  );
};
