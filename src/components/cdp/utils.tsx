import type {
  RecommendationType,
  RecommendationWithoutHintsType
} from './types';
import type { Street, Currency, Area, Volume, Hint } from '../../gen/cdp';

import type {
  UnifiedRecommendationResponse,
  SourceDocumentDetails,
  UnifiedRecommendationSource
} from '../../gen/cdp';

import type { cdpLanguage, RecommendationRow } from './types';
import {
  FIELD_TRANSLATIONS,
  FIELDS_NAMES_ALL
} from './Form/FieldsController/constants';
import { HINTS_ENABLED } from './featureFlags';

export const groupRecommendationsByValue = (
  formattedRecommendations: RecommendationRow[]
) => {
  const groupedRecommendations = formattedRecommendations.reduce(
    (acc, item: RecommendationRow) => {
      const key = item.value.toString();
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(item);
      return acc;
    },
    {}
  );
  return groupedRecommendations;
};

// TODO: use translations of field names to all languages (en, fr, it)
export const getTranslatedRecommendationTitle = (
  fieldName: string,
  uiLanguage: cdpLanguage
) => {
  const translatedTitle =
    fieldName && FIELD_TRANSLATIONS[fieldName]
      ? FIELD_TRANSLATIONS[fieldName][uiLanguage]
      : FIELDS_NAMES_ALL[fieldName];

  if (translatedTitle) return translatedTitle.toUpperCase();
  else return '';
};

export const formatGroupedRecommendations = (
  groupedRecommendations: UnifiedRecommendationResponse[],
  uiLanguage: string,
  hdf_field_name?: string
): Record<string, RecommendationRow[]> => {
  let formattedGroupedRecommendations = {} as Record<
    string,
    RecommendationRow[]
  >;
  if (!groupedRecommendations) return {} as Record<string, RecommendationRow[]>; // return null to use as loading state in the UI

  try {
    formattedGroupedRecommendations = groupedRecommendations.reduce(
      (acc, item: UnifiedRecommendationResponse, index: number) => {
        const isHint = item.is_hint;

        // TODO: process extra returned fields for different types (like currency)
        if (!isHint) {
          const type = item.field_value.return_type;
          const recommendationItem = item as UnifiedRecommendationResponse;
          const { value, value_prefix, value_suffix } =
            getFieldValueForRecommendation(
              recommendationItem.field_value,
              hdf_field_name
            );
          const key = value
            ? value.toString()
            : hdf_field_name.toString() + '_' + index.toString();

          // if (!acc[key]) {
          //   acc[key] = [];
          // }

          const source = item.source as UnifiedRecommendationSource;
          const sourceDocumentsSet = new Set([]);
          const recommendationSources = source.source_document_details;
          recommendationSources.forEach(
            (
              recommendationSource: SourceDocumentDetails,
              sourceIndex: number
            ) => {
              const document = recommendationSource.semantic_document_uuid;
              if (sourceDocumentsSet.has(document)) return;
              else sourceDocumentsSet.add(document);

              const semanticPageIndex =
                recommendationSource.page_index_to_scroll;
              const currentSemanticPage =
                recommendationSource.semantic_pages[semanticPageIndex];
              const DEFAULT_PAGE_OBJECT_INDEX = 0;
              const currentPageObject =
                currentSemanticPage.page_objects[DEFAULT_PAGE_OBJECT_INDEX];

              const row: RecommendationRow = {
                index,
                key: currentPageObject.page_object_uuid,
                value: value,
                value_prefix: value_prefix,
                value_suffix: value_suffix,
                type: type,
                preview: {
                  url: currentSemanticPage.image_url,
                  bbox: currentPageObject.bbox
                },
                documentUuid: recommendationSource.semantic_document_uuid,
                pageObjectUuid: currentPageObject.page_object_uuid,
                pageObjectTitle:
                  currentPageObject.page_object_title[uiLanguage] ||
                  currentPageObject.page_object_title.key,
                documentTitle: recommendationSource.semantic_document_title,
                documentCategory: recommendationSource.document_category,
                documentDate: recommendationSource.document_date,
                rotationAngle: currentSemanticPage.rotation_angle,
                pageNumber: semanticPageIndex,
                totalPages: recommendationSource.semantic_document_page_count,
                documentCategoryTranslated:
                  recommendationSource.document_category_translated ||
                  recommendationSource.document_category,
                documentSuffix:
                  recommendationSource.semantic_document_suffix ||
                  recommendationSource.semantic_document_title,
                hdf_field_name
              };
              // const rowKey = key + ` (${sourceIndex})`;
              const rowKey = key;
              if (!acc[rowKey]) {
                acc[rowKey] = [];
              }
              acc[rowKey].push(row);
            }
          );
        } else {
          if (!HINTS_ENABLED) return acc;

          const hintItem = item as UnifiedRecommendationResponse;
          const fieldValue = item.field_value as Hint;
          const type = fieldValue.expected_return_type;
          const source = hintItem.source as UnifiedRecommendationSource;
          const hintsSources = source.source_document_details;
          const key = ''; // FIXME: we use keys as values in rendering while grouping docs, not values from row
          if (!acc[key]) {
            acc[key] = [];
          }

          hintsSources.forEach(
            (hintSource: SourceDocumentDetails, hintIndex: number) => {
              const row: RecommendationRow = {
                index,
                key: 'hint-' + index + '-' + hintIndex,
                value: '',
                type: type,
                preview: null,
                pageObjectUuid: null,
                pageObjectTitle: null,
                documentUuid: hintSource.semantic_document_uuid,
                documentTitle: hintSource.semantic_document_title,
                documentCategory: hintSource.document_category,
                documentDate: hintSource.document_date,
                rotationAngle: null,
                pageNumber: null,
                totalPages: null,
                documentCategoryTranslated:
                  hintSource.document_category_translated ||
                  hintSource.document_category,
                documentSuffix:
                  hintSource.semantic_document_suffix ||
                  hintSource.semantic_document_title,
                page_images: hintSource.semantic_pages.map(
                  (page) => page.image_url
                ),
                page_index_preferred: hintSource.page_index_to_scroll,
                isHint: true,
                hdf_field_name
              };
              acc[key].push(row);
            }
          );
        }

        return acc;
      },
      {} as Record<string, RecommendationRow[]>
    );
  } catch (e) {
    console.warn('Error in formatting CDP recommendations data', e);
    throw new Error('Error in formatting CDP recommendations data');
  }

  // run deduplication between documents
  const deduplicatedFormattedGroupedRecommendations = {} as Record<
    string,
    RecommendationRow[]
  >;

  for (const [key, sources] of Object.entries(
    formattedGroupedRecommendations
  )) {
    const uniqueSources = {} as any;

    for (const source of sources) {
      if (source.documentUuid && !uniqueSources[source.documentUuid])
        uniqueSources[source.documentUuid] = source;
    }

    deduplicatedFormattedGroupedRecommendations[key] =
      Object.values(uniqueSources);
  }

  return deduplicatedFormattedGroupedRecommendations;
};

export interface FieldValueForRecommendation {
  value: string;
  value_prefix: string | null;
  value_suffix: string | null;
}

export const getFieldValueForRecommendation = (
  field_value: RecommendationType,
  hdf_field_name?: string
): FieldValueForRecommendation => {
  // TODO: process extra returned fields for different types (like currency)
  let value_prefix = null as string | null;
  let value_suffix = null as string | null;
  const type = field_value?.return_type;
  let value = '';

  try {
    switch (type) {
      case 'hint':
        break; // no value for hints
      case 'currency':
        value = (field_value as Currency).value.toString();
        value_suffix = (field_value as Currency).currency;
        break;
      case 'area':
        value = (field_value as Area).value.toString();
        value_suffix = (field_value as Area).unit;
        break;
      case 'volume':
        value = (field_value as Volume).value.toString();
        value_suffix = (field_value as Volume).unit || 'm³';
        break;
      case 'street':
        // test for requested hdf_field_name
        if (
          hdf_field_name === 'hdf_property_street' ||
          hdf_field_name === 'hdf_person_street'
        ) {
          value =
            (field_value as Street).value.street_name ||
            (field_value as Street).value.page_object_value;
          const suffix = (field_value as Street).value.suffix ?? '';
          value_suffix =
            (field_value as Street).value.street_number.toString() + suffix;
        } else if (
          hdf_field_name === 'hdf_property_street_no' ||
          hdf_field_name === 'hdf_person_street_no'
        ) {
          const suffix = (field_value as Street).value.suffix ?? '';
          const streetNumber = (field_value as Street).value.street_number
            ? (field_value as Street).value.street_number.toString()
            : (field_value as Street).value.page_object_value;
          value = streetNumber + suffix;
          // add street name if available to show it in grey
          value_prefix = (field_value as Street).value.street_name;
        } else if (hdf_field_name === 'hdf_person_street_with_number') {
          const suffix = (field_value as Street).value.suffix ?? '';
          const street_sufix =
            (field_value as Street).value.street_number.toString() + suffix;
          value =
            (field_value as Street).value.street_name + ' ' + street_sufix;
        }
        break;
      default:
        const raw_value = (
          field_value as Exclude<RecommendationWithoutHintsType, Street>
        )?.value;
        value = raw_value ? raw_value.toString() : '';
        // special treatment of hdf_person_debt_enforcement_record_status = 'Betreibungen'
        if (hdf_field_name == 'hdf_person_debt_enforcement_record_status') {
          const lowercase_value = raw_value?.toString().toLowerCase() || '';

          const noCondition =
            lowercase_value.includes('kein') ||
            lowercase_value.includes('keine') ||
            lowercase_value.includes('oder verlustscheine registriert sind');

          if (noCondition) {
            value_prefix = 'Betreibungen';
            value = 'nein';
          }
        }
        break;
    }
  } catch (e) {
    console.warn('Error getting CDP value', e);
  }

  return { value, value_prefix, value_suffix };
};
const isNonEmptyValue = (value: string | null | undefined) =>
  value !== null && value !== undefined && value !== '';

export const allowedGroupContextKeys = new Set([
  'groupId',
  'groupName',
  'hdf_person_firstname',
  'hdf_person_lastname',
  'hdf_person_street',
  'hdf_property_street'
]);

// filtering out empty values && not allowed keys
export function filterFieldContext(fieldContext: object) {
  if (!fieldContext) return undefined;

  const filteredFieldContext = Object.fromEntries(
    Object.entries(fieldContext).filter(
      ([key, value]) =>
        allowedGroupContextKeys.has(key) && isNonEmptyValue(value)
    )
  );

  return filteredFieldContext;
}
