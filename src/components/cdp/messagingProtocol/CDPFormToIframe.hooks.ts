import { useCallback, RefObject } from 'react';

import {
  MessageType,
  NumRecommendationsRequestMessageSchema,
  CDPFormToIframeMessagingFunctions
} from '@components/cdp/messagingProtocol/messageTypes';
import { filterFieldContext } from '@components/cdp/utils';

import { debounceFunction } from './utils';

export const useCDPFormToIframeMessages = (
  messageTarget: string
): CDPFormToIframeMessagingFunctions => {
  const sendAuthToken = useCallback(
    (iframeRef: RefObject<HTMLIFrameElement>, authToken: string) => {
      const contentWindow = iframeRef?.current?.contentWindow;
      if (!contentWindow) return;
      contentWindow.postMessage(
        {
          type: MessageType.TOKEN_REFRESHED,
          payload: { token: authToken }
        },
        messageTarget //'*' //HYPODOSSIER_CDP_BACKEND_URL = HYPODOSSIER_CDP_IFRAME_URL
      );
    },
    [messageTarget]
  );

  const sendLanguage = useCallback(
    (iframeRef: RefObject<HTMLIFrameElement>, language: string) => {
      {
        const contentWindow = iframeRef?.current?.contentWindow;
        if (!contentWindow) return;
        contentWindow.postMessage(
          {
            type: MessageType.LANGUAGE_UPDATED,
            payload: { language: language }
          },
          messageTarget //'*' //HYPODOSSIER_CDP_BACKEND_URL = HYPODOSSIER_CDP_IFRAME_URL
        );
      }
    },
    [messageTarget]
  );

  const requestNumRecommendations = useCallback(
    // Use the properly typed debounceFunction
    debounceFunction(
      (
        iframeRef: RefObject<HTMLIFrameElement>,
        fieldSet: Set<string>,
        fieldContext?: object,
        timestamp?: number
      ) => {
        // TODO: remove filtering from sending message concern, filter in reducer when update state
        const filteredFieldContext = filterFieldContext(fieldContext);

        const contentWindow = iframeRef?.current?.contentWindow;
        if (!contentWindow) return;

        contentWindow?.postMessage(
          {
            type: MessageType.NUM_RECOMMENDATIONS_REQUESTED,
            payload: {
              field_definitions: Array.from(fieldSet),
              field_context: filteredFieldContext,
              timestamp
            }
          } as NumRecommendationsRequestMessageSchema,
          messageTarget // '*' //HYPODOSSIER_CDP_BACKEND_URL = HYPODOSSIER_CDP_IFRAME_URL
        );
      },
      1000
    ),
    [messageTarget]
  );

  const sendPreviewMode = useCallback(
    (iframeRef: RefObject<HTMLIFrameElement>, previewMode: boolean) => {
      {
        const contentWindow = iframeRef?.current?.contentWindow;
        if (!contentWindow) return;
        contentWindow.postMessage(
          {
            type: MessageType.SET_PREVIEW_MODE,
            payload: { previewMode }
          },
          messageTarget // '*' //HYPODOSSIER_CDP_BACKEND_URL = HYPODOSSIER_CDP_IFRAME_URL
        );
      }
    },
    [messageTarget]
  );

  const sendGetRecommendations = useCallback(
    (
      iframeRef: RefObject<HTMLIFrameElement>,
      sourceId: string,
      fieldName: string,
      selectedValue?: string,
      isHandEdited?: boolean,
      fieldContext?: object
    ) => {
      // 1) set the field name to get recommendations for
      // TODO: extract message function to utils in messaging protocol
      if (iframeRef.current) {
        const filteredFieldContext = filterFieldContext(fieldContext);

        const contentWindow = iframeRef.current.contentWindow;
        if (contentWindow) {
          contentWindow.postMessage(
            {
              type: MessageType.RECOMMENDATION_REQUESTED,
              payload: {
                requestId: sourceId,
                fieldName: fieldName,
                selectedValue: selectedValue,
                isHandEdited: isHandEdited,
                fieldContext: filteredFieldContext
              }
            },
            messageTarget // '*' // TODO: target CDP Frontend URL
          );
        }
      }
      // 2) show the iframe with recommendations
      // extracted from messaging concern, as this is UI concern
      // setShowIframe(true);
    },
    [messageTarget]
  );

  return {
    sendAuthToken,
    sendLanguage,
    requestNumRecommendations,
    sendGetRecommendations,
    sendPreviewMode
  };
};
