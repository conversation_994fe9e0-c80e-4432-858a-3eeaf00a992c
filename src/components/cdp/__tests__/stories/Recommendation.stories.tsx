import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Recommendation } from '@components/cdp/Modal/Recommendations/Recommendation';
import {
  withModal,
  withMemoryRouter,
  withQueryClient,
  withReduxStore,
  withModalContextProvider
} from './decorators';
import {
  GrossIncomeRow1,
  GrossIncomeRow2,
  GrossIncomeRowHint,
  StreetRow,
  VolumeRow,
  AHVTextRow
} from './mockRecommendationRows';
import { SetPreviewModePlayFn, CheckIconStylesInPreviewModePlayFn } from './interactions';
import dayjs from 'dayjs';

import AHVPage from './AHVPage.jpg';

const emptyRow = {
  index: 0,
  key: 'hint-0-0',
  value: '',
  type: 'string',
  preview: null,
  pageObjectUuid: null,
  pageObjectTitle: null,
  documentUuid: 'doc-UUID',
  documentTitle: '310 Steuererklärung Mustermann Max ZH 2019',
  documentCategory: 'TAX_DECLARATION',
  documentDate: '2020-04-20',
  rotationAngle: null,
  pageNumber: null,
  totalPages: null,
  documentCategoryTranslated: 'Steuererklärung',
  documentSuffix: 'Mustermann Max ZH 2019',
  page_images: [AHVPage],
  page_index_preferred: 0,
  isHint: true,
  hdf_field_name: 'hdf_person_income_alimony_total'
};

const dateRow = {
  index: 2,
  key: 'e1bcff5c-7859-47f7-a9b3-16ca3007a0a6',
  value: '1977-09-04',
  value_prefix: null,
  value_suffix: null,
  type: 'date',
  preview: {
    url: AHVPage,
    bbox: {
      ref_height: 4210,
      ref_width: 2975,
      top: 1269,
      left: 736,
      right: 967,
      bottom: 1329
    }
  },
  pageObjectUuid: 'e1bcff5c-7859-47f7-a9b3-16ca3007a0a6',
  pageObjectTitle: 'Geburtsdatum',
  documentUuid: 'doc-UUID',
  documentTitle: '410 PK Ausweis Thiemann Manuel AXA Zusatz GL 2017-03-01',
  documentCategory: 'PENSION_CERTIFICATE',
  documentDate: '2017-01-03',
  rotationAngle: 0,
  pageNumber: 0,
  totalPages: 2,
  documentCategoryTranslated: 'PK Ausweis',
  documentSuffix: 'Thiemann Manuel AXA Zusatz GL 2017-03-01',
  hdf_field_name: 'hdf_person_date_of_birth'
};

const currencyNumberRow = {
  index: 0,
  key: 'fb033e9b-551e-4d9e-8fd2-b7a465d3c85a',
  value: '10000',
  value_prefix: null,
  value_suffix: 'CHF',
  type: 'currency',
  preview: {
    url: AHVPage,
    bbox: {
      ref_height: 4194,
      ref_width: 2973,
      top: 2259,
      left: 2671,
      right: 2809,
      bottom: 2340
    }
  },
  pageObjectUuid: 'fb033e9b-551e-4d9e-8fd2-b7a465d3c85a',
  pageObjectTitle: 'Bruttolohn/Rente',
  documentUuid: 'doc-UUID',
  documentTitle: "330 Lohnausweis Manuel Thiemann 2016 brutto CHF 10'000",
  documentCategory: 'SALARY_CERTIFICATE',
  documentDate: '2017-01-20',
  rotationAngle: 0,
  pageNumber: 0,
  totalPages: 1,
  documentCategoryTranslated: 'Lohnausweis',
  documentSuffix: "Manuel Thiemann 2016 brutto CHF 10'000",
  hdf_field_name: 'hdf_person_income_gross'
};

const meta: Meta<typeof Recommendation> = {
  title: 'Cdp/Components/Modal/Recommendations/Recommendation',
  component: Recommendation,
  decorators: [
    withModal,
    withMemoryRouter,
    withReduxStore,
    withQueryClient,
    withModalContextProvider
  ]
};

export default meta;
type Story = StoryObj<typeof Recommendation>;

export const AllTypes: Story = {
  render: () => (
    <>
      <Recommendation
        initialValue={'10000'}
        selectedValue={''}
        minLength={10}
        maxLength={10}
        setFieldValueCallback={() => {}}
        row={GrossIncomeRow1}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'188762'}
        selectedValue={''}
        minLength={10}
        maxLength={10}
        setFieldValueCallback={() => {}}
        row={GrossIncomeRow2}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={10}
        maxLength={10}
        setFieldValueCallback={() => {}}
        row={GrossIncomeRowHint}
        open={false}
        hasScrollbar={false}
      />
    </>
  )
};

export const StreetName: Story = {
  render: () => (
    <Recommendation
      initialValue={'Birmensdorferstrasse'}
      selectedValue={''}
      maxLength={23}
      setFieldValueCallback={() => {}}
      row={StreetRow}
      open={false}
      hasScrollbar={false}
    />
  )
};

export const Text: Story = {
  render: () => (
    <Recommendation
      initialValue={'756.4078.9585.31'}
      selectedValue={''}
      setFieldValueCallback={() => {}}
      row={AHVTextRow}
      open={false}
      hasScrollbar={false}
    />
  )
};

export const TextSelected: Story = {
  render: () => (
    <Recommendation
      initialValue={'756.4078.9585.31'}
      selectedValue={'756.4078.9585.31'}
      setFieldValueCallback={() => {}}
      row={AHVTextRow}
      open={false}
      hasScrollbar={false}
    />
  )
};

export const TextEmpty: Story = {
  render: () => (
    <Recommendation
      initialValue={''}
      selectedValue={'1'}
      minLength={0}
      setFieldValueCallback={() => {}}
      row={AHVTextRow}
      open={false}
      hasScrollbar={false}
    />
  )
};

export const TextEdited: Story = {
  render: () => (
    <Recommendation
      initialValue={'756.4078.9585.31'}
      selectedValue={'756.4078.9585.31'}
      isHandEdited={true}
      setFieldValueCallback={() => {}}
      row={AHVTextRow}
      open={false}
      hasScrollbar={false}
    />
  )
};

export const Volume: Story = {
  render: () => (
    <Recommendation
      initialValue={'7150'}
      selectedValue={''}
      setFieldValueCallback={() => {}}
      row={VolumeRow}
      open={false}
      hasScrollbar={false}
    />
  )
};

export const Date: Story = {
  render: () => (
    <Recommendation
      initialValue={dayjs('04.09.1977', 'DD-MM-YYYY')}
      selectedValue={''}
      setFieldValueCallback={() => {}}
      row={dateRow}
      open={false}
      hasScrollbar={false}
    />
  )
};

export const Currency3Figures: Story = {
  render: () => (
    <>
      <Recommendation
        initialValue={'243'}
        selectedValue={''}
        maxLength={3 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '243' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'12'}
        selectedValue={''}
        maxLength={3 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '12' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'1'}
        selectedValue={''}
        maxLength={3 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '1' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'986'}
        selectedValue={''}
        maxLength={3 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '986' }}
        open={false}
        hasScrollbar={false}
      />
    </>
  )
};

export const Currency6Figures: Story = {
  render: () => (
    <>
      <Recommendation
        initialValue={'10000'}
        selectedValue={''}
        maxLength={6 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '10000' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'100000'}
        selectedValue={''}
        maxLength={6 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '100000' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'999000'}
        selectedValue={''}
        maxLength={6 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '999000' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'35000'}
        selectedValue={''}
        maxLength={6 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '35000' }}
        open={false}
        hasScrollbar={false}
      />
    </>
  )
};

export const Currency9Figures: Story = {
  render: () => (
    <>
      <Recommendation
        initialValue={'12000000'}
        selectedValue={''}
        maxLength={9 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '12000000' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'1000000'}
        selectedValue={''}
        maxLength={9 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '1000000' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'40000000'}
        selectedValue={''}
        maxLength={9 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '40000000' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'100000000'}
        selectedValue={''}
        maxLength={9 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '100000000' }}
        open={false}
        hasScrollbar={false}
      />
    </>
  )
};

export const Currency12Figures: Story = {
  render: () => (
    <>
      <Recommendation
        initialValue={'12000000000'}
        selectedValue={''}
        maxLength={12 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '12000000000' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'1000000000'}
        selectedValue={''}
        maxLength={12 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '1000000000' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'40000000000'}
        selectedValue={''}
        maxLength={12 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '40000000000' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={'100000000000'}
        selectedValue={''}
        maxLength={12 + 3}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '100000000000' }}
        open={false}
        hasScrollbar={false}
      />
    </>
  )
};

export const EmptyCurrencyAlignment: Story = {
  render: () => (
    <>
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={1}
        setFieldValueCallback={() => {}}
        row={{ ...emptyRow, type: 'currency' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={2}
        setFieldValueCallback={() => {}}
        row={{ ...emptyRow, type: 'currency' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={3}
        setFieldValueCallback={() => {}}
        row={{ ...emptyRow, type: 'currency' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={4}
        setFieldValueCallback={() => {}}
        row={{ ...emptyRow, type: 'currency' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={5}
        setFieldValueCallback={() => {}}
        row={{ ...emptyRow, type: 'currency' }}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={6}
        setFieldValueCallback={() => {}}
        row={{ ...emptyRow, type: 'currency' }}
        open={false}
        hasScrollbar={false}
      />
    </>
  )
};

export const EmptyStringAlignment: Story = {
  render: () => (
    <>
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={0}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={1}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={2}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={3}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={4}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={5}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={6}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={7}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={8}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={9}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
      <Recommendation
        initialValue={''}
        selectedValue={''}
        minLength={10}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
    </>
  )
};

export const TextPreviewMode: Story = {
  render: () => (
    <Recommendation
      initialValue={'756.4078.9585.31'}
      selectedValue={''}
      setFieldValueCallback={() => {}}
      row={AHVTextRow}
      open={false}
      hasScrollbar={false}
    />
  ),
  play: SetPreviewModePlayFn()
};

export const CurrencyPreviewMode: Story = {
  render: () => (
    <Recommendation
      initialValue={'10000'}
      selectedValue={''}
      maxLength={6 + 3}
      setFieldValueCallback={() => {}}
      row={{ ...currencyNumberRow, value: '10000' }}
      open={false}
      hasScrollbar={false}
    />
  ),
  play: SetPreviewModePlayFn()
};

export const DatePreviewMode: Story = {
  render: () => (
    <Recommendation
      initialValue={dayjs('04.09.1977', 'DD-MM-YYYY')}
      selectedValue={''}
      setFieldValueCallback={() => {}}
      row={dateRow}
      open={false}
      hasScrollbar={false}
    />
  ),
  play: SetPreviewModePlayFn()
};

export const StreetNamePreviewMode: Story = {
  render: () => (
    <Recommendation
      initialValue={'Birmensdorferstrasse'}
      selectedValue={''}
      maxLength={23}
      setFieldValueCallback={() => {}}
      row={StreetRow}
      open={false}
      hasScrollbar={false}
    />
  ),
  play: SetPreviewModePlayFn()
};

export const EmptyPreviewMode: Story = {
  render: () => (
    <Recommendation
      initialValue={''}
      selectedValue={''}
      minLength={0}
      setFieldValueCallback={() => {}}
      row={emptyRow}
      open={false}
      hasScrollbar={false}
    />
  ),
  play: SetPreviewModePlayFn()
};

export const IconStylesInPreviewMode: Story = {
  render: () => (
    <>
      {/* Selected value with checkmark */}
      <Recommendation
        initialValue={'756.4078.9585.31'}
        selectedValue={'756.4078.9585.31'}
        setFieldValueCallback={() => {}}
        row={AHVTextRow}
        open={false}
        hasScrollbar={false}
      />
      {/* Hand edited value, selectedValue will be null in this case */}
      <Recommendation
        initialValue={'10000'}
        selectedValue={null}
        isHandEdited={true}
        setFieldValueCallback={() => {}}
        row={{ ...currencyNumberRow, value: '10000' }}
        open={false}
        hasScrollbar={false}
      />
      {/* Empty value */}
      <Recommendation
        initialValue={''}
        selectedValue={''}
        setFieldValueCallback={() => {}}
        row={emptyRow}
        open={false}
        hasScrollbar={false}
      />
    </>
  ),
  play: CheckIconStylesInPreviewModePlayFn(),
  parameters: {
    docs: {
      description: {
        story: 'This story demonstrates and tests how icons change their appearance in preview mode:\n' +
          '- Selected values show a grey checkmark with not-allowed cursor\n' +
          '- Hand edited values show no hand icon, only disabled checkmark icon\n' +
          '- Empty values show a placeholder'
      }
    }
  }
};
