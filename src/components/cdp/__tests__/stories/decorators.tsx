import { MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  CDPScrollingContext,
  CDPFormStateContext,
  CDPFormDispatchContext
} from '@components/cdp/Form/RecommendationsForm/context';
import demoData from './hdfFieldsDemoData.json';
import { mockCDPFormState as CDPFormState } from './mockCDPFormState.tsx';
import { ModalContextProvider } from '../../Modal/Wrapper/ModalContext';

const withMemoryRouter = (Story) => (
  <MemoryRouter initialEntries={['/']}>
    <Story />
  </MemoryRouter>
);

const store = configureStore({
  reducer: () => ({
    dossiers: {
      dossiersData: {
        semantic_documents: [],
        uuid: 'eaa7b34a-5d35-490f-ae90-54ef8e3c2ce1',
        access_mode: 'read_write'
      }
    },
    header: { logoutURL: /logout/ }
  })
});

const withReduxStore = (Story) => (
  <Provider store={store}>
    <Story />
  </Provider>
);

const queryClient = new QueryClient({
  defaultOptions: { queries: { retry: false } }
});

const withQueryClient = (Story) => (
  <QueryClientProvider client={queryClient}>
    <Story />
  </QueryClientProvider>
);

const withModal = (Story) => (
  <div style={{ width: '1200px', height: '800px' }}>
    <Story />
  </div>
);

const withRecommendationsContainer = (Story) => (
  <div style={{ width: 852 }}>
    <Story />
  </div>
);

const withScrollingContextProvider = (Story) => (
  <CDPScrollingContext.Provider
    value={{ setScrollingState: () => {}, scrollingState: false }}
  >
    <Story />
  </CDPScrollingContext.Provider>
);

// iterate over all fields in CDPFormState and set values from demoData map using hdfFieldId as key
Object.keys(CDPFormState.fields).forEach((key) => {
  const field = CDPFormState.fields[key];
  if (demoData.hasOwnProperty(field.hdfRecommendationId)) {
    const newValue = demoData[field.hdfRecommendationId];
    field.value = demoData[field.hdfRecommendationId];
    CDPFormState.fields[key] = { ...field };
  }
});

const withCDPFormStateContextProvider = (Story) => (
  <CDPFormStateContext.Provider value={CDPFormState}>
    <Story />
  </CDPFormStateContext.Provider>
);

const withCDPFormDispatchContextProvider = (Story) => (
  <CDPFormDispatchContext.Provider value={() => {}}>
    <Story />
  </CDPFormDispatchContext.Provider>
);

const withModalContextProvider = (Story) => (
  <ModalContextProvider>
    <Story />
  </ModalContextProvider>
);

export {
  withMemoryRouter,
  withReduxStore,
  withQueryClient,
  withModal,
  withRecommendationsContainer,
  withScrollingContextProvider,
  withCDPFormStateContextProvider,
  withCDPFormDispatchContextProvider,
  withModalContextProvider
};
