import type { StoryObj } from '@storybook/react';
import { RecommendationModal } from '../../Modal/Wrapper/RecommendationModal';
import { expect, userEvent, waitFor } from '@storybook/test';
import { MessageType } from '@components/cdp/messagingProtocol/messageTypes';

type Story = StoryObj<typeof RecommendationModal>;
const DELAY = 1500;
const GROUP_TEST_ID_SELECTOR = '[data-testid="cdp-accordion-source"]';
const DOCUMENTS_COUNT = 14;
const DOCUMENTS_WITH_SCROLLING_COUNT = 16; // deduplicated from 17

export function OpenSourceDocumentsPlayFn(): Story['play'] {
  return async ({ canvasElement, step }) => {
    await step('Loading Modal component', async () => {
      await waitFor(
        () => {
          expect(
            document.querySelectorAll(GROUP_TEST_ID_SELECTOR)
          ).toBeTruthy();
        },
        { timeout: DELAY }
      );
    });

    await step('Test if all the Group Panels are diplayed', async () => {
      await waitFor(
        () => {
          const elementList = document.querySelectorAll(GROUP_TEST_ID_SELECTOR);
          expect(elementList.length).toBe(DOCUMENTS_COUNT);
        },
        { timeout: DELAY }
      );
    });

    await step('Open Accordion', async () => {
      const elementList = document.querySelectorAll(GROUP_TEST_ID_SELECTOR);
      const firstRecommendation = elementList[0];
      userEvent.click(firstRecommendation);
    });

    await step('Close Accordion', async () => {
      const elementList = document.querySelectorAll(GROUP_TEST_ID_SELECTOR);
      const firstRecommendation = elementList[0];
      await userEvent.click(firstRecommendation);
    });
  };
}

export function OpenSourceDocumentsWithScrollingPlayFn(): Story['play'] {
  return async ({ canvasElement, step }) => {
    await step('Loading Modal component', async () => {
      await waitFor(
        () => {
          expect(
            document.querySelectorAll(GROUP_TEST_ID_SELECTOR)
          ).toBeTruthy();
        },
        { timeout: DELAY }
      );
    });

    await step('Test if all the Group Panels are diplayed', async () => {
      await waitFor(
        () => {
          const elementList = document.querySelectorAll(GROUP_TEST_ID_SELECTOR);
          expect(elementList.length).toBe(DOCUMENTS_WITH_SCROLLING_COUNT);
        },
        { timeout: DELAY }
      );
    });

    await step('Open Accordion', async () => {
      const elementList = document.querySelectorAll(GROUP_TEST_ID_SELECTOR);
      const firstRecommendation = elementList[0];
      await userEvent.click(firstRecommendation);
    });

    await step('Close Accordion', async () => {
      const elementList = document.querySelectorAll(GROUP_TEST_ID_SELECTOR);
      const firstRecommendation = elementList[0];
      await userEvent.click(firstRecommendation);
    });
  };
}

export function CheckCannotEditInPreviewModePlayFn(): StoryObj<any>['play'] {
  return async ({ canvasElement, step }) => {
    const FIELD_ID = 'Person_1_hdf_person_street';
    const INPUT_SELECTOR = `[data-testid=\"cdp-form-input-${FIELD_ID}\"]`;
    const NEW_VALUE = 'New Street Name';

    await step('Set preview mode ON', async () => {
      // Find and click the preview mode switch if present
      const switchLabel = Array.from(document.querySelectorAll('span')).find(
        (el) => el.textContent?.toLowerCase().includes('preview mode')
      );
      if (switchLabel) {
        userEvent.click(switchLabel);
      }
    });

    await step('Try to change value in preview mode', async () => {
      const input = document.querySelector(INPUT_SELECTOR) as HTMLInputElement;
      expect(input).toBeTruthy();
      const originalValue = input.value;
      // try to focus the input
      await userEvent.click(input);
      // check that element is not focused
      expect(document.activeElement).not.toBe(input);
      await userEvent.type(input, NEW_VALUE);
      // Wait a bit for any state updates
      await waitFor(() => {
        expect(input.value).toBe(originalValue);
      });
    });

    await step(
      'Check that clear form button is disabled in preview mode',
      async () => {
        const button = document.querySelector(
          '[data-testid="cdp-form-clear-button"]'
        ) as HTMLButtonElement;
        expect(button).toBeTruthy();
        expect(button.disabled).toBe(true);
      }
    );
  };
}

export function SetPreviewModePlayFn(): StoryObj<any>['play'] {
  return async ({ canvasElement }) => {
    // Simulate postMessage event to set preview mode
    window.dispatchEvent(
      new MessageEvent('message', {
        data: {
          type: MessageType.SET_PREVIEW_MODE,
          payload: { previewMode: true }
        }
      })
    );
  }
}

export function CheckIconStylesInPreviewModePlayFn(): StoryObj<any>['play'] {
  return async ({ canvasElement, step }) => {
    await step('Check that icons are enabled in preview mode OFF', async () => {
      // Get all checkmark icons before preview mode
      const checkmarkIcons = canvasElement.querySelectorAll('[data-testid="checkmark-icon"]');

      // Check initial state - should be clickable
      checkmarkIcons.forEach(icon => {
        expect(icon).toHaveAttribute('data-active', 'true');
      });
    });

    await step('Check that hand selection icons are visible in preview mode OFF', async () => {
      // Get all hand selection icons before preview mode
      const handSelectionIcons = canvasElement.querySelectorAll('[data-testid="hand-selection-icon"]');
      // Check that hand selection icons are visible
      handSelectionIcons.forEach(icon => {
        expect(icon).toBeVisible();
      });
    });
    await step('Set preview mode ON', async () => {

      // Enable preview mode
      window.dispatchEvent(
        new MessageEvent('message', {
          data: {
            type: MessageType.SET_PREVIEW_MODE,
            payload: { previewMode: true }
          }
        })
      );
      // Wait for a bit to ensure the preview mode is set
      await new Promise(resolve => setTimeout(resolve, DELAY));
    });
    await step('Wait for preview mode to be set', async () => {
      // Wait for the preview mode to be set
      await waitFor(() => {
        const checkmarkIcons = canvasElement.querySelectorAll('[data-testid="checkmark-icon"]');
        expect(checkmarkIcons.length).toBeGreaterThan(0);
      });
    });

    // Wait for the preview mode to be set
    await step('Check that icons are now disabled', async () => {
      // Get all checkmark icons after preview mode
      const checkmarkIcons = canvasElement.querySelectorAll('[data-testid="checkmark-icon"]');

      // Check that icons are now disabled
      checkmarkIcons.forEach(icon => {
        expect(icon).toHaveAttribute('data-active', 'false');
        expect(icon).toHaveStyle({ cursor: 'not-allowed' });
      });

      // Check that hand selection icons are not visible
      const handSelectionIcons = canvasElement.querySelectorAll('[data-testid="hand-selection-icon"]');
      handSelectionIcons.forEach(icon => {
        expect(icon).not.toBeVisible();
      });
    });
  };
}

