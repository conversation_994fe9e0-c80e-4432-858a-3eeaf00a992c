import {
  DEFAULT_APPLIED_ROLES_FILTERS,
  DEFAULT_APPLIED_BC_FILTERS,
  DEFAULT_APPLIED_STATUS_FILTERS,
  DEFAULT_APPLIED_SEARCH_VALUE,
  DEFAULT_FILTER_LAST_UPDATED_VALUE
} from '../components/DossierListFilter/constants';
import { VIEWS } from './cfg';
export { ROUTES } from './routes';

export const BASE_URL = '/'; // Base URL of your application

export const DESC = 'desc';
export const CREATED_AT = 'created_at';
export const STATUS = 'status';

export const MIN_WIDTH_TO_DISPLAY_BREADCRUMB_LOGO_AND_MANAGER = '750px';

export const AUTH_TOKEN_NAME = 'accountGrantToken';
export const enablePageDelete = true;

export const KEYCLOAK_DOSSIER_MANAGER_ROLE_KEY = 'Dossier-manager';

export const title = 'title';
export const filename = 'filename';

export const BACKEND_QUERY_PARAM_FOR_SHOWING_SOFT_DELETED = 'show_soft_deleted';

export const RESTORE_ICON_SIZE = 25;

export const BACKEND_STATUS_PROCESSING = 'Processing';
export const BACKEND_STATUS_PROCESSED = 'Processed';
export const SEMANTIC_PATH_FOR_API = 'semantic_documents';

export const STRING_TYPE_FOR_EDIT_PAGE_OBJECT = 'STRING';
export const PARAGRAPH_TYPE_FOR_EDIT_PAGE_OBJECT = 'PARAGRAPH';
export const ADDRESS_BLOCK_TYPE_FOR_EDIT_PAGE_OBJECT = 'ADDRESS_BLOCK';
export const INT_TYPE_FOR_EDIT_PAGE_OBJECT = 'INT';
export const CURRENCY_TYPE_FOR_EDIT_PAGE_OBJECT = 'CURRENCY';
export const DATE_TYPE_FOR_EDIT_PAGE_OBJECT = 'DATE';
export const FINHURDLE_TYPE_FOR_EDIT_PAGE_OBJECT = 'FINHURDLE';

export const AVAILABLE_FOR_EDIT_PAGE_OBJECT_TYPES: string[] = [
  STRING_TYPE_FOR_EDIT_PAGE_OBJECT,
  PARAGRAPH_TYPE_FOR_EDIT_PAGE_OBJECT,
  ADDRESS_BLOCK_TYPE_FOR_EDIT_PAGE_OBJECT,
  INT_TYPE_FOR_EDIT_PAGE_OBJECT,
  CURRENCY_TYPE_FOR_EDIT_PAGE_OBJECT,
  DATE_TYPE_FOR_EDIT_PAGE_OBJECT,
  FINHURDLE_TYPE_FOR_EDIT_PAGE_OBJECT
];

export const actionClick = 'click';
export const actionDataClick = 'actionDataClick';
export const actionScroll = 'scroll';
export const needWaitLoadThisViews: string[] = [
  VIEWS.pageView,
  VIEWS.dataView,
  VIEWS.recycleBin,
  VIEWS.structureDetails,
  VIEWS.photos,
  VIEWS.plans
];

export const enableDnD = true;
export const scrollCenterParams = {
  behavior: 'auto',
  block: 'center',
  inline: 'start'
};

export const scrollStartParams = {
  behavior: 'auto',
  block: 'start',
  inline: 'start'
};

export const scrollNearestParams = {
  behavior: 'auto',
  block: 'nearest',
  inline: 'nearest'
};

export const ItemTypes = {
  DOCUMENT_PAGE: 'page of document on document view',
  DOCUMENT_PAGE_DDPNS: 'DOCUMENT_PAGE_ON_DETAIL_DOCUMENT_PAGE_NAVIGATE_SECTION'
};

export const handledErrors: string[] = ['IMAGES_NOT_FOUND'];

export const maxLimitForDossierName = 120;

export const DATE_FORMAT = 'DD.MM.YYYY';
export const DATE_FORMAT_2 = 'YYYY-MM-DD';
export const DATE_LOCALE = 'de';

export const GERMAN_LANG = 'de';
export const FRENCH_LANG = 'fr';
export const ITALIAN_LANG = 'it';
export const ENGLISH_LANG = 'en';

export const ACCEPTED_UI_LANGUAGES = [
  GERMAN_LANG,
  FRENCH_LANG,
  ITALIAN_LANG,
  ENGLISH_LANG
];

export const DOSSIER_GERMAN_LANG = 'De';
export const DOSSIER_FRENCH_LANG = 'Fr';
export const DOSSIER_ITALIAN_LANG = 'It';
export const DOSSIER_ENGLISH_LANG = 'En';

export const ACCEPTED_DOSSIER_LANGUAGES = [
  DOSSIER_GERMAN_LANG,
  DOSSIER_FRENCH_LANG,
  DOSSIER_ITALIAN_LANG,
  DOSSIER_ENGLISH_LANG
];

// Keeps track of the accepted frontend theme values,
// to fail hard if any invalid value is found
export const ACCEPTED_FRONTEND_THEME_VALUES = [
  'YELLOW',
  'BLUE',
  'RED',
  'BCGE',
  'FINNOVA',
  'LEGACY',
  'DEFAULT'
];

export const ANT_DESIGN_CONFIG_PROVIDER_CLASS_PREFIX = 'hd-ant';

export const localStorageItems = [
  DEFAULT_APPLIED_ROLES_FILTERS,
  DEFAULT_APPLIED_BC_FILTERS,
  DEFAULT_APPLIED_STATUS_FILTERS,
  DEFAULT_APPLIED_SEARCH_VALUE,
  DEFAULT_FILTER_LAST_UPDATED_VALUE
];

export const DISPLAY_EXPIRY_THRESHOLD = 3000;
