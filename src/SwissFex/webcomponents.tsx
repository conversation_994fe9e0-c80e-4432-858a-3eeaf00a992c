import r2wc from '@r2wc/react-to-web-component';
import { DossierWebComponentWrapper } from './Wrapper/DossierWebComponentWrapper';
import { DetailWebComponentWrapper } from './Wrapper/DetailWebComponentWrapper';

import './../i18n/i18n';
import './../styles/style.css';
import '@fortawesome/fontawesome-svg-core/styles.css';

import 'react-pdf/dist/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Update the TypeScript definition
declare global {
  interface HTMLElementEventMap {
    closedClicked: CustomEvent;
  }

  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace JSX {
    interface IntrinsicElements {
      'swissfex-hypo-dossier': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement> & {
          base_url?: string;
          token: string;
          lang: string;
          view: string;
          closeclicked?: string;
          pageclicked?: string;
          documentactionperformed?: string;
        },
        HTMLElement
      >;
      'swissfex-hypo-detail': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement> & {
          base_url?: string;
          token: string;
          semantic_document_uuid: string;
          lang: string;
          page_number: string;
          closeclicked?: string;
          pagechanged?: string;
          documentnotfound?: string;
          documentactionperformed?: string;
        },
        HTMLElement
      >;
    }
  }
}

customElements.define(
  'swissfex-hypo-dossier',
  r2wc(DossierWebComponentWrapper, {
    props: {
      base_url: 'string',
      token: 'string',
      lang: 'string',
      view: 'string',
      closeclicked: 'function',
      pageclicked: 'function',
      documentactionperformed: 'function'
    }
  })
);

customElements.define(
  'swissfex-hypo-detail',
  r2wc(DetailWebComponentWrapper, {
    props: {
      base_url: 'string',
      token: 'string',
      semantic_document_uuid: 'string',
      lang: 'string',
      page_number: 'string',
      closeclicked: 'function',
      pagechanged: 'function',
      documentnotfound: 'function',
      documentactionperformed: 'function'
    }
  })
);
