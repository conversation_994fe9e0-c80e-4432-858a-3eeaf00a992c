import React from 'react';
import { Provider } from 'react-redux';

import { store } from '../../store';
import { DetailWebComponent } from '../DetailWebComponent';

import { IDetailWebComponentProps } from '../types';
import { ErrorContextProvider } from '../../components/NetworkError/ErrorContextProvider.tsx';
import { StylesRootContextProvider } from '../../providers/StylesRootProvider.tsx';

export const DetailWebComponentWrapper: React.FC<IDetailWebComponentProps> = (
  props
) => {
  // Map the props based on the presence of the 'container' object
  const {
    base_url = props.container?.base_url || props.base_url,
    token = props.container?.token || props.token,
    lang = props.container?.lang || props.lang,
    semantic_document_uuid = props.container?.semantic_document_uuid ||
      props.semantic_document_uuid,
    closeclicked = props.container?.closeclicked || props?.closeclicked,
    page_number = props.container?.page_number || props?.page_number,
    pagechanged = props.container?.pagechanged || props?.pagechanged,
    documentnotfound = props.container?.documentnotfound ||
      props?.documentnotfound,
    documentactionperformed = props.container?.documentactionperformed ||
      props?.documentactionperformed
  } = props;

  return (
    <StylesRootContextProvider>
      <Provider store={store}>
        <ErrorContextProvider>
          <div className="hd-webcomponent">
            <DetailWebComponent
              base_url={base_url}
              token={token}
              semantic_document_uuid={semantic_document_uuid}
              lang={lang}
              closeclicked={closeclicked}
              page_number={page_number}
              pagechanged={pagechanged}
              documentnotfound={documentnotfound}
              documentactionperformed={documentactionperformed}
            />
          </div>
        </ErrorContextProvider>
      </Provider>
    </StylesRootContextProvider>
  );
};
