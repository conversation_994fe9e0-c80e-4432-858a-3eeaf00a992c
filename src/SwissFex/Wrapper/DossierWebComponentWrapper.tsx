import React from 'react';
import { Provider } from 'react-redux';
import { store } from '../../store';
import { DossierWebComponent } from '../DossierWebComponent';

import { IDossierWebComponentProps } from '../types';

import { ErrorContextProvider } from '../../components/NetworkError/ErrorContextProvider.tsx';
import { StylesRootContextProvider } from '../../providers/StylesRootProvider.tsx';

export const DossierWebComponentWrapper: React.FC<IDossierWebComponentProps> = (
  props
) => {
  // Map the props based on the presence of the 'container' object
  const {
    base_url = props.container?.base_url || props.base_url,
    token = props.container?.token || props.token,
    lang = props.container?.lang || props.lang,
    view = props.container?.view || props.view,
    closeclicked = props.container?.closeclicked || props?.closeclicked,
    pageclicked = props.container?.pageclicked || props?.pageclicked,
    documentactionperformed = props.container?.documentactionperformed ||
      props?.documentactionperformed
  } = props;

  return (
    <StylesRootContextProvider>
      <Provider store={store}>
        <ErrorContextProvider>
          <div className="hd-webcomponent">
            <DossierWebComponent
              base_url={base_url}
              token={token}
              lang={lang}
              view={view}
              closeclicked={closeclicked}
              pageclicked={pageclicked}
              documentactionperformed={documentactionperformed}
            />
          </div>
        </ErrorContextProvider>
      </Provider>
    </StylesRootContextProvider>
  );
};
