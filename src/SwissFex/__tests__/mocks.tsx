import { HttpResponse, http } from 'msw';
import { parseJwt } from '@utils/jwtParser';
import {
  account,
  dataV2deleted,
  getDossierByExternalId,
  getDossierDataByExternalId
} from './mockData';
import { createMockJwt } from '@components/Header/SharedHeader/InfoButton/__tests__/stories/mockData.ts';

// https://storybook.js.org/addons/msw-storybook-addon
export const generateDossierHandlers = (_?: string) => [
  http.get('*/api/account', () => {
    return HttpResponse.json(account);
  }),
  http.get('*/api/dossier/external_id/:id', ({ request }) => {
    const authToken = request?.headers?.get('Authorization') || '';
    const payloadFromJWT = authToken ? parseJwt(authToken) : null;
    const externalId = payloadFromJWT?.external_dossier_id;
    console.log('*/api/dossier/external_id/:id, external_dossier_id = ', {
      externalId
    });
    return HttpResponse.json(getDossierByExternalId(externalId));
  }),
  http.get(
    '*/api/dossier/:dossierUuid/data_v2?show_soft_deleted=false',
    async ({ request }) => {
      const queryString = request.url;

      const authToken = request?.headers?.get('Authorization') || '';
      const payloadFromJWT = authToken ? parseJwt(authToken) : null;
      const externalId = payloadFromJWT?.external_dossier_id;
      console.log(
        '*/api/dossier/:dossierUuid/data_v2?show_soft_deleted=false, external_dossier_id = ',
        {
          externalId
        }
      );

      // Split the string by '=' and get the last part
      const showSoftDeletedValue = queryString.split('=')[1];

      const responseData =
        showSoftDeletedValue == 'true'
          ? dataV2deleted
          : getDossierDataByExternalId(externalId);
      return HttpResponse.json(responseData);
    }
  ),

  http.post('*/api/dossier/:dossierUuid/check_updates', async () => {
    // emulate long pending request
    // await delay(2000);
    return HttpResponse.json({ need_update: false });
    // emulate 404 error
    // return HttpResponse.json({ error: 'Not found' }, { status: 404 });
    // 401 error emulation
    // return HttpResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }),
  http.get('*/api/dossier/:dossierUuid/check_access', ({ request }) => {
    const authToken = request?.headers?.get('Authorization') || '';
    const payloadFromJWT = authToken ? parseJwt(authToken) : null;
    const externalId = payloadFromJWT?.external_dossier_id;
    console.log(
      '*/api/dossier/:dossierUuid/check_access, external_dossier_id = ',
      {
        externalId
      }
    );
    return HttpResponse.json({
      dossier_uuid: 'd37277cf-1940-4f1e-acbc-24fb7d6da593',
      requested: '2023-12-29T05:30:31.341Z',
      issued: '2023-12-29T05:30:31.353Z',
      expires: '2023-12-29T05:39:31.353Z'
    });
  }),
  http.post('*/api/accounts/:accountKey/token', () => {
    return HttpResponse.json({
      token: createMockJwt({
        account_key: 'default',
        account_keys: ['default', 'clientis-1-key'],
        external_dossier_id: '123'
      })
    });
  }),
  http.get('*/api/accounts/accessible', () => {
    return HttpResponse.json({
      accounts: {
        default: {
          name: 'Hypodossier Account'
        }
      }
    });
  })
];
