import i18n from 'i18next';
import React, { useEffect, useMemo } from 'react';
import { MemoryRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import {
  setDetailPageCloseEvent,
  setDetailPageNavigationEvent,
  setDocumentNotFoundEvent,
  setLoadingStatus,
  triggerDocumentActionPerformedAction
} from './actions';
import {
  setDocumentTitleStatus,
  setUploadZoneStatus
} from '../components/Dossiers/actions';
import { Pages } from '../enums/pages';
import { Notification } from '../components/Notification';
import { setPageVisited } from '../components/App/actions';
import { PropTokenAuth } from '../components/PropTokenAuth';
import { AxiosBaseURLConfigurator } from '../components/App/AxiosBaseURLConfigurator.tsx';
import { NotificationPlacement } from '../enums/notification';
import { DossierDocumentDetail } from './DossierDocumentDetail';
import { setNotificationPlacement } from '../components/Notification/action';
import { ExternalIdDossierLoader } from '../components/ExternalIdDossierLoader';
import { setStatusDeletedSemanticDocuments } from '../components/Header/actions';
import { setSemanticDocument } from '../components/SemanticDocumentLoader/actions';
import { AccountConfigurationLoader } from './DossierDocumentDetail/AccountConfigurationLoader';

import { RootState } from '../store/redux/types';
import { IDetailWebComponentProps } from './types';
import { useAppDispatch, useAppSelector } from '../store/app/hooks';
import RuntimeConfigContextProvider from '../components/App/RuntimeConfigContextProvider.tsx';
import { AccountContextProvider } from '../components/Account/AccountContextProvider.tsx';
import { api } from '../api';
import { setLoaderStatus } from '../components/Loader/actions';
import { useErrorContext } from '@components/NetworkError/ErrorContextProvider.tsx';
import { ErrorComponent } from '@components/NetworkError/ErrorComponent.tsx';
import MultiTenancyContextProvider from '@components/Auth/MultiTenancyContextProvider.tsx';
import { ControlledThemeConfigurator } from '../providers/ControlledThemeConfigurator.tsx';

const queryClient = new QueryClient();

export const DetailWebComponent: React.FC<IDetailWebComponentProps> = (
  props
) => {
  const dispatch = useAppDispatch();
  const semanticDocumentPageNumberList = useAppSelector(
    (state: RootState) => state.swissFexData.semanticDocumentPageNumberList
  );

  const refreshDossierData = useAppSelector(
    (state: RootState) => state.swissFexData.refreshDossierData
  );

  const documentActionPerformedData = useAppSelector(
    (state: RootState) => state.swissFexData.documentActionPerformedData
  );

  useEffect(() => {
    void i18n.changeLanguage(props.lang);
    dispatch(
      setSemanticDocument(
        props.semantic_document_uuid,
        props.page_number ?? '0'
      )
    );
    dispatch(setLoadingStatus(true));
    dispatch(setDetailPageCloseEvent(props.closeclicked));
    dispatch(setDetailPageNavigationEvent(props.pagechanged));
    dispatch(setDocumentNotFoundEvent(props.documentnotfound));
    dispatch(setDocumentTitleStatus(false));
    dispatch(setUploadZoneStatus(false));
    dispatch(setNotificationPlacement(NotificationPlacement.BOTTOM_RIGHT));
    dispatch(setPageVisited(Pages.DETAIL));
    dispatch(setStatusDeletedSemanticDocuments(false));

    return () => {
      dispatch(setPageVisited(Pages.DOSSIER));
      dispatch(setLoadingStatus(false));
    };
  }, []);

  const pageNumber = useMemo(() => {
    return props.page_number
      ? props.page_number
      : semanticDocumentPageNumberList.find(
          (doc) => doc.semanticDocumentUUID === props.semantic_document_uuid
        )?.pageNumber || '0';
  }, [props.semantic_document_uuid, props.page_number]);

  useEffect(() => {
    dispatch(setSemanticDocument(props.semantic_document_uuid, pageNumber));
  }, [props.semantic_document_uuid, pageNumber]);

  useEffect(() => {
    void i18n.changeLanguage(props.lang);
  }, [props.lang]);

  useEffect(() => {
    dispatch(setDetailPageCloseEvent(props.closeclicked));
  }, [props.closeclicked]);

  useEffect(() => {
    dispatch(setDetailPageNavigationEvent(props.pagechanged));
  }, [props.pagechanged]);

  useEffect(() => {
    dispatch(setDocumentNotFoundEvent(props.documentnotfound));
  }, [props.documentnotfound]);

  useEffect(() => {
    if (props.documentactionperformed && refreshDossierData) {
      dispatch(
        triggerDocumentActionPerformedAction(
          props.documentactionperformed,
          documentActionPerformedData
        )
      );
    }
  }, [refreshDossierData, props.documentactionperformed]);

  const { showErrorComponent, showError } = useErrorContext();

  useEffect(() => {
    api.interceptors.response.use(
      function (response) {
        return response;
      },
      function (error) {
        let isHandledNetworkError = false;

        if (error.response?.status === 403) {
          window.location.reload();
        } else if (error.response && error.response.status === 502) {
          showErrorComponent(); // Trigger the display of the error component
        }

        if (!error.status && !error.response) {
          dispatch(setLoaderStatus(false));
          isHandledNetworkError = true;
        }

        return Promise.reject({ ...error, isHandledNetworkError });
      }
    );
  }, []);

  if (showError) return <ErrorComponent showError={showError} />;

  const ViewComponentContent = (
    <>
      <QueryClientProvider client={queryClient}>
        <RuntimeConfigContextProvider>
          <AxiosBaseURLConfigurator>
            <MemoryRouter>
              <PropTokenAuth token={props.token}>
                <MultiTenancyContextProvider>
                  <AccountContextProvider>
                    <ControlledThemeConfigurator>
                      <AccountConfigurationLoader>
                        <ExternalIdDossierLoader isDetailWebComponent={true}>
                          <DossierDocumentDetail />
                        </ExternalIdDossierLoader>
                      </AccountConfigurationLoader>
                    </ControlledThemeConfigurator>
                  </AccountContextProvider>
                </MultiTenancyContextProvider>
              </PropTokenAuth>
            </MemoryRouter>
            {/* // not styled with our theme */}
            <Notification />
          </AxiosBaseURLConfigurator>
        </RuntimeConfigContextProvider>
      </QueryClientProvider>
    </>
  );

  return <>{ViewComponentContent}</>;
};
