import i18n from 'i18next';
import React, { useEffect } from 'react';
import { MemoryRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { DossierPage } from './DossierPage';
import { PropTokenAuth } from '../components/PropTokenAuth';
import { Notification } from '../components/Notification';
import { AxiosBaseURLConfigurator } from '../components/App/AxiosBaseURLConfigurator.tsx';
import {
  setDossierTitleWithLink,
  setPageVisited
} from '../components/App/actions';
import {
  setDossierPageCloseEvent,
  setDossierPageNavigationEvent,
  setLoadingStatus,
  triggerDocumentActionPerformedAction
} from './actions';
import { ExternalIdDossierLoader } from '../components/ExternalIdDossierLoader';
import { AccountConfigurationLoader } from './DossierDocumentDetail/AccountConfigurationLoader';
import { setDossierPageTabView } from '../components/DossierPageTabViewLoader/actions';
import {
  setDocumentTitleStatus,
  setUploadZoneStatus
} from '../components/Dossiers/actions';
import { VIEWS } from '../constants/cfg';
import { setStatusDeletedSemanticDocuments } from '../components/Header/actions';
import { setNotificationPlacement } from '../components/Notification/action';
import { NotificationPlacement } from '../enums/notification';

import { IDossierWebComponentProps } from './types';
import { Pages } from '../enums/pages';
import { RootState } from '../store/redux/types';
import { useAppDispatch, useAppSelector } from '../store/app/hooks';
import RuntimeConfigContextProvider from '../components/App/RuntimeConfigContextProvider.tsx';
import { AccountContextProvider } from '../components/Account/AccountContextProvider.tsx';
import { api } from '../api';
import { setLoaderStatus } from '../components/Loader/actions';
import { useErrorContext } from '@components/NetworkError/ErrorContextProvider.tsx';
import { ErrorComponent } from '@components/NetworkError/ErrorComponent.tsx';
import { loadDossiersToRedux } from '../components/Dossiers/actions';
import type { DossierDataStructure } from '../components/Dossiers/types';
import MultiTenancyContextProvider from '@components/Auth/MultiTenancyContextProvider.tsx';
import { ControlledThemeConfigurator } from '../providers/ControlledThemeConfigurator.tsx';

const queryClient = new QueryClient();

export const DossierWebComponent: React.FC<IDossierWebComponentProps> = (
  props
) => {
  const dispatch = useAppDispatch();

  const refreshDossierData = useAppSelector(
    (state: RootState) => state.swissFexData.refreshDossierData
  );

  const documentActionPerformedData = useAppSelector(
    (state: RootState) => state.swissFexData.documentActionPerformedData
  );

  const networkStatus = useAppSelector(
    (state: RootState) => state.networkError.status
  );

  useEffect(() => {
    void i18n.changeLanguage(props.lang);
    dispatch(setDossierPageTabView(props.view));
    dispatch(setLoadingStatus(true));
    dispatch(setDossierPageCloseEvent(props.closeclicked));
    dispatch(setDossierPageNavigationEvent(props.pageclicked));
    dispatch(setDocumentTitleStatus(false));
    dispatch(setUploadZoneStatus(false));
    dispatch(setNotificationPlacement(NotificationPlacement.BOTTOM_RIGHT));
    dispatch(setPageVisited(Pages.DOSSIER));
    dispatch(setDossierTitleWithLink(false));

    return () => {
      dispatch(setLoadingStatus(false));
      // clear dossier data to prevent showing old data
      dispatch(loadDossiersToRedux({} as DossierDataStructure));
    };
  }, []);

  useEffect(() => {
    dispatch(setDossierPageTabView(props.view));

    dispatch(
      setStatusDeletedSemanticDocuments(props.view === VIEWS.recycleBin)
    );
  }, [props.view]);

  useEffect(() => {
    void i18n.changeLanguage(props.lang);
  }, [props.lang]);

  useEffect(() => {
    dispatch(setDossierPageCloseEvent(props.closeclicked));
  }, [props.closeclicked]);

  useEffect(() => {
    dispatch(setDossierPageNavigationEvent(props.pageclicked));
  }, [props.pageclicked]);

  useEffect(() => {
    if (props.documentactionperformed && refreshDossierData) {
      dispatch(
        triggerDocumentActionPerformedAction(
          props.documentactionperformed,
          documentActionPerformedData
        )
      );
    }
  }, [refreshDossierData, props.documentactionperformed]);

  const { showErrorComponent, showError } = useErrorContext();

  useEffect(() => {
    api.interceptors.response.use(
      function (response) {
        return response;
      },
      function (error) {
        let isHandledNetworkError = false;

        if (error.response?.status === 403) {
          window.location.reload();
        } else if (error.response && error.response.status === 502) {
          showErrorComponent(); // Trigger the display of the error component
        }

        if (!error.status && !error.response) {
          dispatch(setLoaderStatus(false));
          isHandledNetworkError = true;
        }

        return Promise.reject({ ...error, isHandledNetworkError });
      }
    );
  }, []);

  if (showError) return <ErrorComponent showError={showError} />;

  return (
    <>
      <QueryClientProvider client={queryClient}>
        <RuntimeConfigContextProvider>
          <AxiosBaseURLConfigurator>
            <MemoryRouter>
              <PropTokenAuth token={props.token}>
                <MultiTenancyContextProvider>
                  <AccountContextProvider>
                    <ControlledThemeConfigurator>
                      <AccountConfigurationLoader>
                        <ExternalIdDossierLoader isDetailWebComponent={false}>
                          <DossierPage />
                        </ExternalIdDossierLoader>
                      </AccountConfigurationLoader>
                    </ControlledThemeConfigurator>
                  </AccountContextProvider>
                </MultiTenancyContextProvider>
              </PropTokenAuth>
            </MemoryRouter>
          </AxiosBaseURLConfigurator>
          {/* // not styled with our theme */}
          <Notification />
        </RuntimeConfigContextProvider>
      </QueryClientProvider>
    </>
  );
};
