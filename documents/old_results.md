## Previous Results

### V0

Test set with config v0.1v (vision only) - time to predict the test set: 86 seconds in total (< 6.25ms per page)
While it is super fast, there's a loading time that takes a couple seconds. So expect more something like 10sec on a single CPU to process a batch. Then the GPU process the entire batch in around 250ms. 

| Scores 		                   || Val 		   || Test 		   ||
|-----------------------|-----------|-------|-------|-------|-------|
|				        | thresh    | F1	| Acc %	| F1	| Acc %	|
| v0.1v		            |    .5     | 		| 		| 0.927 | 96.83 |
| v0.1v sc0 	        |    .5     | 0.929	| 96.57	| 0.932	| 97.07	|
| v0.1v sc0 	        |    .8     | 0.937	| 97.08	| 0.941	| 97.55	|
| v0.1v sc0 + k=5       |    .5     | 0.913	| 95.66	| 0.907	| 95.82	|
| v0.1v sc0 + k=5       |    .95    | 0.931	| 96.73	| 0.933	| 97.12	|

Note on sc0: I believe there's a small issue with how the current TABME batch can split any document. As we simply shuffle doc IDs, we often cut documents in between two batch. Therefore, we might not predict a cut for the 1st page of the document, and this is something that will never happen with a user as the first page is always intially set to be a 'cut', so I created a set_cut0 (sc0) argument that force the prediction value of the first page of the batch to be 1. 
In the test set, this corrected 32 batch (32/215->15%) and improves f1 from 0.927 to 0.932 (+0.5%)

### V1
| Scores 		                     || Val 	       || Test 		   ||
|-------------------------|-----------|---------|-------|-------|-------|
| v1.0v sc0 (Anothresh)   |    .5     | 0.906	| 95.27	| 0.896	| 95.24	|
| v1.0v sc0 (Anothresh)   |    .95    | 0.929	| 96.61	| 0.926	| 96.79	|

Return to main [Readme](../README.md)