/// <reference types="vitest" />
import { defineConfig } from 'vite';
import fs from 'fs-extra';

export default defineConfig({
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    copyPublicDir: true,
  },
  test: {
    globals: true,
    environment: 'happy-dom',
    setupFiles: ['./src/tests/setup.ts'],
    include: ['**/*.{test,spec}.{ts,tsx}'],
    exclude: ['**/*.e2e.{test,spec}.{ts,tsx}', 'src/tests/e2e/**'],
    coverage: {
      reporter: ['text', 'json', 'html'],
    },
  },
  publicDir: 'public',
  plugins: [
    {
      name: 'copy-config',
      closeBundle: async () => {
        try {
          await fs.copy('config', 'dist/config');
          console.log('Config files copied to dist/config successfully');
        } catch (error) {
          console.error('Error copying config files:', error);
        }
      }
    }
  ]
}); 