/**
 * Authentication service for handling OAuth token acquisition and management
 */

interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

interface AuthCredentials {
  username: string;
  password: string;
}

// Custom event for auth state changes
export const AUTH_STATE_CHANGE_EVENT = 'auth-state-change';

export class AuthService {
  private tokens: AuthTokens | null = null;
  private tokenExpiryTime: number = 0;
  private readonly CLIENT_ID = 'swissfex-test-client';
  private readonly SCOPE = 'openid';
  
  constructor(
    private authUrl: string = import.meta.env.VITE_AUTH_URL
  ) {
    // Check for a stored token on initialization
    this.loadTokenFromStorage();
  }

  /**
   * Authenticate user with username and password
   */
  async login(credentials: AuthCredentials): Promise<AuthTokens> {
    if (!credentials.username || !credentials.password) {
      throw new Error('Username and password are required');
    }

    const formData = new URLSearchParams();
    formData.append('grant_type', 'password');
    formData.append('client_id', this.CLIENT_ID);
    formData.append('scope', this.SCOPE);
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);

    try {
      const response = await fetch(this.authUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error_description || 'Authentication failed');
      }

      const tokens: AuthTokens = await response.json();
      this.setTokens(tokens);
      
      // Notify that auth state has changed
      this.notifyAuthStateChange(true);
      
      return tokens;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Get the current access token, refreshing if needed
   */
  async getAccessToken(): Promise<string> {
    // If no tokens, throw error
    if (!this.tokens) {
      throw new Error('Not authenticated');
    }

    // If token is expired or about to expire (within 30 seconds), refresh it
    const currentTime = Date.now();
    if (currentTime >= this.tokenExpiryTime - 30000) {
      await this.refreshToken();
    }

    return this.tokens.access_token;
  }

  /**
   * Refresh the access token using the refresh token
   */
  async refreshToken(): Promise<void> {
    if (!this.tokens?.refresh_token) {
      throw new Error('No refresh token available');
    }

    const formData = new URLSearchParams();
    formData.append('grant_type', 'refresh_token');
    formData.append('client_id', this.CLIENT_ID);
    formData.append('refresh_token', this.tokens.refresh_token);

    try {
      const response = await fetch(this.authUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData,
      });

      if (!response.ok) {
        // If refresh fails, clear tokens and throw error
        this.logout();
        throw new Error('Token refresh failed');
      }

      const tokens: AuthTokens = await response.json();
      this.setTokens(tokens);
    } catch (error) {
      this.logout();
      throw error;
    }
  }

  /**
   * Log out the user by clearing tokens
   */
  logout(): void {
    const wasAuthenticated = this.isAuthenticated();
    
    this.tokens = null;
    this.tokenExpiryTime = 0;
    localStorage.removeItem('auth_tokens');
    
    // Notify that auth state has changed (if it was authenticated before)
    if (wasAuthenticated) {
      this.notifyAuthStateChange(false);
    }
  }

  /**
   * Check if the user is currently authenticated
   */
  isAuthenticated(): boolean {
    return !!this.tokens && Date.now() < this.tokenExpiryTime;
  }

  /**
   * Set tokens and update expiry time
   */
  private setTokens(tokens: AuthTokens): void {
    this.tokens = tokens;
    // Set expiry time (current time + expires_in in seconds - 60 second buffer)
    this.tokenExpiryTime = Date.now() + (tokens.expires_in - 60) * 1000;
    
    // Store tokens in localStorage for persistence
    localStorage.setItem('auth_tokens', JSON.stringify({
      tokens,
      expiryTime: this.tokenExpiryTime
    }));
  }

  /**
   * Load tokens from localStorage if available
   */
  private loadTokenFromStorage(): void {
    const storedAuth = localStorage.getItem('auth_tokens');
    if (storedAuth) {
      try {
        const { tokens, expiryTime } = JSON.parse(storedAuth);
        // Only restore if not expired
        if (Date.now() < expiryTime) {
          this.tokens = tokens;
          this.tokenExpiryTime = expiryTime;
        } else {
          // Clear expired tokens
          localStorage.removeItem('auth_tokens');
        }
      } catch (e) {
        // Clear invalid storage
        localStorage.removeItem('auth_tokens');
      }
    }
  }
  
  /**
   * Notify about auth state changes
   */
  private notifyAuthStateChange(isAuthenticated: boolean): void {
    // Dispatch custom event for auth state change
    window.dispatchEvent(new CustomEvent(AUTH_STATE_CHANGE_EVENT, { 
      detail: { isAuthenticated } 
    }));
    
    // Also trigger storage event for cross-tab sync
    // We use localStorage to communicate between tabs
    localStorage.setItem('auth_state_change', Date.now().toString());
  }
} 