import { AuthService } from '../auth/auth-service';

declare global {
  interface Window {
    handleDetailClose: () => void;
    handleDetailPageChange: (event: any) => void;
    handleDetailLanguageChange: (event: Event) => void;
  }
}

// Define the window handlers
window.handleDetailClose = () => {
  console.log('Detail close event');
  window.location.href = '/dossier';
};

window.handleDetailPageChange = (event: any) => {
  console.log('Detail page change event:', event);
  // Update URL with new page number
  const url = new URL(window.location.href);
  url.searchParams.set('page_number', event.pageNumber.toString());
  window.history.pushState({}, '', url.toString());
  
  // Find the detail component and update it
  const detailComponent = document.querySelector('swissfex-hypo-detail');
  if (detailComponent) {
    detailComponent.setAttribute('page_number', event.pageNumber.toString());
  }
};

window.handleDetailLanguageChange = (event: Event) => {
  const newLang = (event.target as HTMLSelectElement).value;
  console.log('Detail language change event:', newLang);
  
  // Update URL with new language
  const url = new URL(window.location.href);
  url.searchParams.set('lang', newLang);
  window.history.pushState({}, '', url.toString());
  
  // Find the detail component within the shadow root
  const detailPage = document.querySelector('detail-page');
  if (detailPage?.shadowRoot) {
    const detailComponent = detailPage.shadowRoot.querySelector('swissfex-hypo-detail');
    if (detailComponent) {
      detailComponent.setAttribute('lang', newLang);
    }
  }
};

/**
 * Detail page component that displays document details
 * Based on the swissfex detail.html example
 * URL format: /dossier/{documentUuid}/semdoc/{semanticDocumentUuid}?lang={language}&page_number={pageNumber}
 */
export class DetailPage extends HTMLElement {
  private authService: AuthService;
  private baseUrl: string = 'http://localhost:8000';
  private language: string = 'de';
  private semanticDocumentUuid: string = '';
  private pageNumber: number = 0;
  private tokenRefreshInterval: number | null = null;
  private readonly TOKEN_REFRESH_INTERVAL = 3500; // ms

  constructor() {
    super();
    this.authService = new AuthService();
    this.attachShadow({ mode: 'open' });
  }

  private parseUrlParameters() {
    const pathParts = window.location.pathname.split('/');
    // Extract document UUID and semantic document UUID from path
    // Format: /dossier/{documentUuid}/semdoc/{semanticDocumentUuid}
    if (pathParts.length >= 5 && pathParts[1] === 'dossier') {
      this.semanticDocumentUuid = pathParts[4];
    } else {
      console.error('Invalid URL format. Expected: /dossier/{documentUuid}/semdoc/{semanticDocumentUuid}');
      this.showError(new Error('Invalid URL format'));
      return;
    }

    // Parse query parameters
    const urlParams = new URLSearchParams(window.location.search);
    this.language = urlParams.get('lang') || 'de';
    this.pageNumber = parseInt(urlParams.get('page_number') || '0', 10);
  }

  async connectedCallback() {
    if (!this.shadowRoot) return;
    
    this.shadowRoot.innerHTML = `
      <div class="detail-page">
        <h2>Document Detail</h2>
        <div class="loading">Loading documents...</div>
      </div>
    `;

    try {
      this.parseUrlParameters();
      if (this.authService.isAuthenticated()) {
        await this.loadDetailContent();
      } else {
        this.showLoginRequired();
      }
    } catch (error) {
      this.showError(error);
    }
  }

  disconnectedCallback() {
    // Clear the refresh interval when the component is removed
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
    }
  }

  private async loadDetailContent() {
    if (!this.shadowRoot) return;
    
    try {
      const token = await this.authService.getAccessToken();

      this.shadowRoot.innerHTML = `
        <style>
          .hd-application {
            height: 85vh;
          }
        </style>
        <div class="detail-page">          
          <div class="detail-controls">
            <div class="control-group">
              <label for="detail-language">Language:</label>
              <select id="detail-language" class="select-control" onchange="window.handleDetailLanguageChange(event)">
                <option value="de" ${this.language === 'de' ? 'selected' : ''}>de</option>
                <option value="en" ${this.language === 'en' ? 'selected' : ''}>en</option>
                <option value="fr" ${this.language === 'fr' ? 'selected' : ''}>fr</option>
                <option value="it" ${this.language === 'it' ? 'selected' : ''}>it</option>
              </select>
            </div>
          </div>

          <div id="detail-content" class="detail-content-container">
            <!-- Detail content will be rendered here -->
          </div>
        </div>
      `;

      await this.renderDetailComponent(token);

    } catch (error) {
      this.showError(error);
    }
  }

  private async renderDetailComponent(token: string) {
    if (!this.shadowRoot) return;
    
    const detailContent = this.shadowRoot.getElementById('detail-content');
    if (!detailContent) return;

    // Create the detail component
    const detailComponent = document.createElement('swissfex-hypo-detail');
    detailComponent.setAttribute('base_url', this.baseUrl);
    detailComponent.setAttribute('token', token);
    detailComponent.setAttribute('semantic_document_uuid', this.semanticDocumentUuid);
    detailComponent.setAttribute('lang', this.language);
    detailComponent.setAttribute('page_number', this.pageNumber.toString());

    // Set the callbacks as string references to window functions
    (detailComponent as any).closeclicked = window.handleDetailClose;
    (detailComponent as any).pagechanged = window.handleDetailPageChange;

    // Clear and append
    detailContent.innerHTML = '';
    detailContent.appendChild(detailComponent);

    // Set up token refresh interval
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
    }

    this.tokenRefreshInterval = window.setInterval(async () => {
      try {
        // Manually refresh the token
        await this.authService.refreshToken();
        const newToken = await this.authService.getAccessToken();
        detailComponent.setAttribute('token', newToken);
        console.log('Detail token refreshed at', new Date().toISOString());
      } catch (error) {
        console.error('Error refreshing detail token:', error);
      }
    }, this.TOKEN_REFRESH_INTERVAL);

    console.log('Detail token refresh started - interval:', this.TOKEN_REFRESH_INTERVAL, 'ms');
  }

  private showLoginRequired() {
    if (!this.shadowRoot) return;
    
    this.shadowRoot.innerHTML = `
      <div class="detail-page">
        <h2>Document Detail</h2>
        <div class="error-message">
          <p>Please log in to gain access.</p>
        </div>
      </div>
    `;
  }

  private showError(error: any) {
    if (!this.shadowRoot) return;
    
    this.shadowRoot.innerHTML = `
      <div class="detail-page">
        <h2>Document Detail</h2>
        <div class="error-message">
          <p>Error loading: ${error.message || 'Unknown error'}</p>
        </div>
      </div>
    `;
  }
}

// Add the event handlers to the window scop

// Register the custom element
customElements.define('detail-page', DetailPage); 