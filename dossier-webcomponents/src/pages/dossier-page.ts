import { AuthService } from '../auth/auth-service';
import { router } from '../router';

// Add the event handlers to the window scope
declare global {
  interface Window {
    handleDossierEvent: (event: any) => void;
    handlePageClick: (event: any) => void;
    handleDossierLanguageChange: (event: Event) => void;
    handleDossierViewChange: (event: Event) => void;
  }
}

// Define the global page click handler
window.handlePageClick = (event: any) => {
  console.log('Page click event:', event);
  if (event.semanticDocumentUUID) {
    const language = document.querySelector('dossier-page')?.getAttribute('language') || 'de';
    const detailUrl = `/dossier/placeholder-id/semdoc/${event.semanticDocumentUUID}?lang=${language}&page_number=${event.pageNumber || 0}`;
    console.log('Navigating to:', detailUrl);
    router.navigate(detailUrl);
  }
};

// Define the language change handler
window.handleDossierLanguageChange = (event: Event) => {
  const newLang = (event.target as HTMLSelectElement).value;
  console.log('Dossier language change event:', newLang);
  
  // Update URL with new language
  const url = new URL(window.location.href);
  url.searchParams.set('lang', newLang);
  window.history.pushState({}, '', url.toString());
  
  // Find the dossier component within the shadow root
  const dossierPage = document.querySelector('dossier-page');
  if (dossierPage?.shadowRoot) {
    const dossierComponent = dossierPage.shadowRoot.querySelector('swissfex-hypo-dossier');
    if (dossierComponent) {
      dossierComponent.setAttribute('lang', newLang);
    }
  }
};

// Define the view change handler
window.handleDossierViewChange = (event: Event) => {
  const newView = (event.target as HTMLSelectElement).value;
  console.log('Dossier view change event:', newView);
  
  // Update URL with new view
  const url = new URL(window.location.href);
  url.searchParams.set('view', newView);
  window.history.pushState({}, '', url.toString());
  
  // Find the dossier component within the shadow root
  const dossierPage = document.querySelector('dossier-page');
  if (dossierPage?.shadowRoot) {
    const dossierComponent = dossierPage.shadowRoot.querySelector('swissfex-hypo-dossier');
    if (dossierComponent) {
      dossierComponent.setAttribute('view', newView);
    }
  }
};

/**
 * Dossier page component that displays dossier information
 * Based on the swissfex dossier.html example
 */
export class DossierPage extends HTMLElement {
  private authService: AuthService;
  private baseUrl: string = 'http://localhost:1337';
  private language: string = 'de';
  private view: string = 'page';
  private tokenRefreshInterval: number | null = null;
  private readonly TOKEN_REFRESH_INTERVAL = 3500; // ms

  constructor() {
    super();
    this.authService = new AuthService();
    // Attach shadow root
    this.attachShadow({ mode: 'open' });
  }

  async connectedCallback() {
    if (!this.shadowRoot) return;

    this.shadowRoot.innerHTML = `
      <div class="dossier-page">
        <h2>Dossier Information</h2>
        <div class="loading">Loading dossier data...</div>
      </div>
    `;

    try {
      // Get token and display dossier information
      if (this.authService.isAuthenticated()) {
        await this.loadDossierContent();
      } else {
        this.showLoginRequired();
      }
    } catch (error) {
      this.showError(error);
    }
  }

  disconnectedCallback() {
    // Clear the refresh interval when the component is removed
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
    }
  }

  private async loadDossierContent() {
    if (!this.shadowRoot) return;

    try {
      // Get the access token
      const token = await this.authService.getAccessToken();

      this.shadowRoot.innerHTML = `
        <style>
          .hd-application {
            height: 85vh;
          }
        </style>
        <div class="dossier-page">          
          <div class="dossier-controls">
            <div class="control-group">
              <label for="dossier-language">Language:</label>
              <select id="dossier-language" class="select-control" onchange="window.handleDossierLanguageChange(event)">
                <option value="de" ${this.language === 'de' ? 'selected' : ''}>de</option>
                <option value="en" ${this.language === 'en' ? 'selected' : ''}>en</option>
                <option value="fr" ${this.language === 'fr' ? 'selected' : ''}>fr</option>
                <option value="it" ${this.language === 'it' ? 'selected' : ''}>it</option>
              </select>
            </div>
            
            <div class="control-group">
              <label for="dossier-view">View:</label>
              <select id="dossier-view" class="select-control" onchange="window.handleDossierViewChange(event)">
                <option value="page" ${this.view === 'page' ? 'selected' : ''}>page</option>
                <option value="recycle-bin" ${this.view === 'recycle-bin' ? 'selected' : ''}>recycle-bin</option>
              </select>
            </div>
          </div>
          
          <div id="dossier-content" class="dossier-content-container">
            <!-- Dossier content will be rendered here -->
          </div>
        </div>
      `;

      await this.renderDossierComponent(token);

    } catch (error) {
      this.showError(error);
    }
  }

  private showLoginRequired() {
    if (!this.shadowRoot) return;

    this.shadowRoot.innerHTML = `
      <div class="dossier-page">
        <h2>Dossier Information</h2>
        <div class="error-message">
          <p>Please log in to view your dossiers.</p>
        </div>
      </div>
    `;
  }

  private showError(error: any) {
    if (!this.shadowRoot) return;

    this.shadowRoot.innerHTML = `
      <div class="dossier-page">
        <h2>Dossier Information</h2>
        <div class="error-message">
          <p>Error loading dossier data: ${error.message || 'Unknown error'}</p>
        </div>
      </div>
    `;
  }

  private async renderDossierComponent(token: string) {
    if (!this.shadowRoot) return;

    const dossierContent = this.shadowRoot.getElementById('dossier-content');
    if (!dossierContent) return;

    console.log('Creating dossier component...');

    // Create the dossier component
    const dossierComponent = document.createElement('swissfex-hypo-dossier');
    console.log('Dossier component created:', dossierComponent);

    // Set attributes
    dossierComponent.setAttribute('base_url', this.baseUrl);
    dossierComponent.setAttribute('token', token);
    dossierComponent.setAttribute('lang', this.language);
    dossierComponent.setAttribute('view', this.view);

    // Create a wrapper div
    const wrapper = document.createElement('div');
    wrapper.style.width = '100%';
    wrapper.style.maxHeight = '80vh';
    wrapper.style.overflowY = 'auto';

    // Set the callback to directly use the window function
    console.log('Setting pageclicked callback...');
    (dossierComponent as any).pageclicked = window.handlePageClick;



    // Append the component to the wrapper
    wrapper.appendChild(dossierComponent);
    console.log('Dossier component appended to wrapper');

    // Clear and append
    dossierContent.innerHTML = '';
    dossierContent.appendChild(wrapper);
    console.log('Wrapper appended to dossier content');

    // Set up token refresh interval
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
    }

    this.tokenRefreshInterval = window.setInterval(async () => {
      try {
        await this.authService.refreshToken();
        const newToken = await this.authService.getAccessToken();
        dossierComponent.setAttribute('token', newToken);
        console.log('Dossier token refreshed at', new Date().toISOString());
      } catch (error) {
        console.error('Error refreshing dossier token:', error);
      }
    }, this.TOKEN_REFRESH_INTERVAL);

    console.log('Dossier token refresh started - interval:', this.TOKEN_REFRESH_INTERVAL, 'ms');
  }
}

// Register the custom element
customElements.define('dossier-page', DossierPage); 