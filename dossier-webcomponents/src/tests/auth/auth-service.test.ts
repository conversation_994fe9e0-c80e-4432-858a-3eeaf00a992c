import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AuthService, AUTH_STATE_CHANGE_EVENT } from '../../auth/auth-service';

describe('AuthService', () => {
  let authService: AuthService;
  const mockAuthUrl = 'http://mock-auth-url.com';
  
  // Use the real username as both username and password
  const validCredentials = {
    username: '<EMAIL>',
    password: '<EMAIL>'
  };
  
  // Mock token response
  const mockTokenResponse = {
    access_token: 'mock-access-token',
    refresh_token: 'mock-refresh-token',
    token_type: 'Bearer',
    expires_in: 300,
    scope: 'openid'
  };
  
  beforeEach(() => {
    // Clear localStorage
    localStorage.clear();
    
    // Reset fetch mock
    vi.resetAllMocks();
    
    // Initialize service with mock URL
    authService = new AuthService(mockAuthUrl);
    
    // Mock localStorage methods properly
    Object.defineProperty(window, 'localStorage', {
      value: {
        setItem: vi.fn(),
        getItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn()
      },
      writable: true
    });
    
    // Mock Date.now to return a consistent timestamp
    vi.spyOn(Date, 'now').mockReturnValue(1000);
    
    // Mock window.dispatchEvent
    Object.defineProperty(window, 'dispatchEvent', {
      value: vi.fn().mockReturnValue(true),
      writable: true
    });
  });
  
  afterEach(() => {
    vi.clearAllMocks();
  });
  

  
  describe('login', () => {
    it('should throw error with empty credentials', async () => {
      await expect(authService.login({ username: '', password: '' }))
        .rejects.toThrow('Username and password are required');
    });
    
    it('should call fetch with correct parameters', async () => {
      // Setup fetch to return mock token
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockTokenResponse)
      });
      
      await authService.login(validCredentials);
      
      // Check that fetch was called with the correct URL and data
      expect(global.fetch).toHaveBeenCalledWith(
        mockAuthUrl,
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/x-www-form-urlencoded'
          }),
          body: expect.any(URLSearchParams)
        })
      );
      
      // Check that URLSearchParams contains expected values
      const fetchCall = (global.fetch as ReturnType<typeof vi.fn>).mock.calls[0];
      const bodyParams = new URLSearchParams(fetchCall[1].body);
      
      expect(bodyParams.get('grant_type')).toBe('password');
      expect(bodyParams.get('username')).toBe(validCredentials.username);
      expect(bodyParams.get('password')).toBe(validCredentials.password);
    });
    
    it('should store tokens in localStorage on successful login', async () => {
      // Setup fetch to return mock token
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockTokenResponse)
      });
      
      await authService.login(validCredentials);
      
      // Check that tokens were saved
      expect(localStorage.setItem).toHaveBeenCalledWith(
        'auth_tokens', 
        expect.any(String)
      );
      
      // Get the saved token string and parse it
      const savedTokenCalls = (localStorage.setItem as any).mock.calls.find(
        (call: any) => call[0] === 'auth_tokens'
      );
      
      if (savedTokenCalls) {
        const storedAuth = JSON.parse(savedTokenCalls[1] as string);
        expect(storedAuth.tokens).toEqual(mockTokenResponse);
        expect(storedAuth.expiryTime).toBe(1000 + (mockTokenResponse.expires_in - 60) * 1000);
      }
    });
    
    it('should dispatch auth state change event on login', async () => {
      // Setup fetch to return mock token
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockTokenResponse)
      });
      
      await authService.login(validCredentials);
      
      // Check that event was dispatched
      expect(window.dispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          type: AUTH_STATE_CHANGE_EVENT,
          detail: expect.objectContaining({ 
            isAuthenticated: true 
          })
        })
      );
    });
    
    it('should throw an error when authentication fails', async () => {
      // Setup fetch to return error
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({
          error_description: 'Invalid credentials'
        })
      });
      
      await expect(authService.login(validCredentials))
        .rejects.toThrow('Invalid credentials');
    });
  });
  
  describe('getAccessToken', () => {
    it('should throw an error when not authenticated', async () => {
      await expect(authService.getAccessToken())
        .rejects.toThrow('Not authenticated');
    });
    
    it('should return the access token when authenticated', async () => {
      // Setup fetch to return mock token
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockTokenResponse)
      });
      
      await authService.login(validCredentials);
      
      // Get access token
      const token = await authService.getAccessToken();
      expect(token).toBe(mockTokenResponse.access_token);
    });
    
    it('should refresh token when it is about to expire', async () => {
      // Setup fetch for initial login
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockTokenResponse)
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ...mockTokenResponse,
            access_token: 'new-access-token'
          })
        });
      
      await authService.login(validCredentials);
      
      // Simulate token is about to expire
      (Date.now as ReturnType<typeof vi.fn>).mockReturnValue(1000 + (mockTokenResponse.expires_in - 29) * 1000);
      
      // Get access token - should trigger refresh
      const token = await authService.getAccessToken();
      
      // Should have refreshed and returned new token
      expect(token).toBe('new-access-token');
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });
  
  describe('refreshToken', () => {
    it('should throw an error when no refresh token exists', async () => {
      await expect(authService.refreshToken())
        .rejects.toThrow('No refresh token available');
    });
    
    it('should call fetch with refresh token parameters', async () => {
      // Setup fetch for initial login and refresh
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockTokenResponse)
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockTokenResponse)
        });
      
      await authService.login(validCredentials);
      
      // Reset fetch mock call history
      (global.fetch as ReturnType<typeof vi.fn>).mockClear();
      
      // Refresh token
      await authService.refreshToken();
      
      // Check that fetch was called with the correct parameters
      expect(global.fetch).toHaveBeenCalledWith(
        mockAuthUrl,
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/x-www-form-urlencoded'
          }),
          body: expect.any(URLSearchParams)
        })
      );
      
      // Check that URLSearchParams contains expected values
      const fetchCall = (global.fetch as ReturnType<typeof vi.fn>).mock.calls[0];
      const bodyParams = new URLSearchParams(fetchCall[1].body);
      
      expect(bodyParams.get('grant_type')).toBe('refresh_token');
      expect(bodyParams.get('refresh_token')).toBe(mockTokenResponse.refresh_token);
    });
    
    it('should update stored tokens on successful refresh', async () => {
      // Setup fetch for initial login and refresh
      const newToken = { 
        ...mockTokenResponse, 
        access_token: 'new-access-token' 
      };
      
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockTokenResponse)
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(newToken)
        });
      
      await authService.login(validCredentials);
      
      // Clear localStorage spy history
      (localStorage.setItem as any).mockClear();
      
      // Refresh token
      await authService.refreshToken();
      
      // Check that tokens were updated
      expect(localStorage.setItem).toHaveBeenCalledWith(
        'auth_tokens', 
        expect.any(String)
      );
      
      // Get the saved token string and parse it
      const savedTokenCalls = (localStorage.setItem as any).mock.calls.find(
        (call: any) => call[0] === 'auth_tokens'
      );
      
      if (savedTokenCalls) {
        const storedAuth = JSON.parse(savedTokenCalls[1] as string);
        expect(storedAuth.tokens).toEqual(newToken);
      }
    });
    
    it('should logout when refresh fails', async () => {
      // Setup fetch for initial login and failed refresh
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockTokenResponse)
        })
        .mockResolvedValueOnce({
          ok: false,
          json: () => Promise.resolve({ error: 'invalid_grant' })
        });
      
      await authService.login(validCredentials);
      
      // Spy on logout method
      vi.spyOn(authService, 'logout');
      
      // Attempt to refresh token and catch error
      await expect(authService.refreshToken()).rejects.toThrow('Token refresh failed');
      
      // Should have called logout
      expect(authService.logout).toHaveBeenCalled();
      
      // Should no longer be authenticated
      expect(authService.isAuthenticated()).toBe(false);
    });
  });
  
  describe('logout', () => {
    it('should clear tokens and localStorage', async () => {
      // Setup fetch for login
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockTokenResponse)
      });
      
      await authService.login(validCredentials);
      
      // Reset localStorage spy
      (localStorage.removeItem as any).mockClear();
      
      // Logout
      authService.logout();
      
      // Should have removed tokens from localStorage
      expect(localStorage.removeItem).toHaveBeenCalledWith('auth_tokens');
      
      // Should no longer be authenticated
      expect(authService.isAuthenticated()).toBe(false);
    });
    
    it('should dispatch auth state change event on logout', async () => {
      // Setup fetch for login
      global.fetch = vi.fn().mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockTokenResponse)
      });
      
      await authService.login(validCredentials);
      
      // Reset dispatchEvent spy
      (window.dispatchEvent as any).mockClear();
      
      // Logout
      authService.logout();
      
      // Check that event was dispatched
      expect(window.dispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          type: AUTH_STATE_CHANGE_EVENT,
          detail: expect.objectContaining({ 
            isAuthenticated: false 
          })
        })
      );
    });
    
    it('should not dispatch event if already logged out', () => {
      // Reset dispatchEvent spy
      (window.dispatchEvent as any).mockClear();
      
      // Logout when already logged out
      authService.logout();
      
      // Should not have dispatched event
      expect(window.dispatchEvent).not.toHaveBeenCalled();
    });
  });
}); 