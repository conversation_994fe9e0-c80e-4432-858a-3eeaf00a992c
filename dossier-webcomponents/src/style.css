:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-width: 320px;
  min-height: 100vh;
  width: 100%;
}

#app {
  width: 100%;
  height: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  width: 100%;
}

main {
  width: 100%;
  padding: 1rem 0;
}

#home-page {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.login-container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
}

login-form {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  display: block;
}

h1 {
  font-size: 2.2em;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  text-align: center;
}

h2 {
  font-size: 1.8em;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.4em;
  margin-bottom: 0.8rem;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}

button:hover {
  border-color: #646cff;
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.primary-button {
  background-color: #646cff;
  color: white;
}

.primary-button:hover {
  background-color: #4e55cc;
}

/* Card styling */
.card {
  background-color: #1a1a1a;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Navigation bar */
.nav-bar {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #333;
  width: 100%;
  gap: 1rem;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.nav-brand .brand {
  font-size: 1.5rem;
  font-weight: bold;
  color: #646cff;
  text-decoration: none;
  white-space: nowrap;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 1.5rem;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
}

.nav-item {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  transition: color 0.3s;
  white-space: nowrap;
}

.nav-item:hover {
  color: rgba(255, 255, 255, 0.9);
}

.nav-item.active {
  color: #646cff;
  font-weight: 500;
}

.nav-auth {
  display: flex;
  align-items: center;
}

/* Profile page */
.profile-page {
  padding: 1rem 0;
  max-width: 900px;
  margin: 0 auto;
}

.profile-data {
  margin-bottom: 1.5rem;
}

.token-info {
  padding-top: 1rem;
  border-top: 1px solid #333;
}

.token-preview {
  margin-top: 1rem;
  font-size: 0.9rem;
}

code {
  background-color: #2a2a2a;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: monospace;
}

/* Dossier and Detail pages */
.dossier-page,
.detail-page {
  padding: 1rem 0;
  max-width: 900px;
  margin: 0 auto;
}

.dossier-controls,
.detail-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  background-color: #1a1a1a;
  padding: 1rem;
  border-radius: 8px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.select-control {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #333;
  background-color: #2a2a2a;
  color: rgba(255, 255, 255, 0.87);
  font-size: 0.9rem;
  min-width: 150px;
}

.dossier-content-container,
.detail-content-container {
  width: 100%;
  border: 1px solid #333;
  border-radius: 8px;
  overflow: auto;
  margin-top: 1rem;
  background-color: #1a1a1a;
}

.user-info {
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
}

.dossier-table {
  width: 100%;
  border-collapse: collapse;
}

.dossier-table th,
.dossier-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #333;
}

.dossier-table th {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.dossier-actions {
  margin: 1.5rem 0;
  display: flex;
  gap: 1rem;
}

/* Status styles */
.status-completed {
  color: #4CAF50;
}

.status-in-progress {
  color: #2196F3;
}

.status-pending {
  color: #FFC107;
}

/* Success and error messages */
.success-message {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4CAF50;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.error-message {
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: #F44336;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.loading {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

#authenticated-welcome {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .nav-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .nav-links {
    margin: 1rem 0;
    width: 100%;
    justify-content: flex-start;
  }

  .nav-auth {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }

  button {
    background-color: #f9f9f9;
  }

  .card {
    background-color: #f9f9f9;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }

  .dossier-controls,
  .detail-controls {
    background-color: #f0f0f0;
  }

  .select-control {
    border: 1px solid #ccc;
    background-color: #fff;
    color: #213547;
  }

  .dossier-content-container,
  .detail-content-container {
    border: 1px solid #e0e0e0;
    background-color: #f9f9f9;
  }

  .nav-brand .brand {
    color: #4e55cc;
  }

  .nav-item {
    color: rgba(0, 0, 0, 0.6);
  }

  .nav-item:hover {
    color: rgba(0, 0, 0, 0.9);
  }

  .nav-item.active {
    color: #4e55cc;
  }

  .nav-bar {
    border-bottom: 1px solid #e5e5e5;
  }

  .token-info {
    border-top: 1px solid #e5e5e5;
  }

  code {
    background-color: #f1f1f1;
  }

  .dossier-table th,
  .dossier-table td {
    border-bottom: 1px solid #e5e5e5;
  }
}