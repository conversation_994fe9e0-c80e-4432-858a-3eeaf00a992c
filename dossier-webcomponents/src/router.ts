/**
 * Simple router for handling navigation between pages
 */

type RouteCallback = () => void;
type RoutePattern = {
  pattern: RegExp;
  callback: RouteCallback;
};

export class Router {
  private routes: Map<string, RouteCallback> = new Map();
  private patternRoutes: RoutePattern[] = [];
  private currentPath: string = '';
  
  constructor() {
    // Initialize router
    window.addEventListener('popstate', () => this.handleRouteChange());
  }
  
  /**
   * Register a route handler for exact path matches
   */
  register(path: string, callback: RouteCallback): void {
    this.routes.set(path, callback);
  }

  /**
   * Register a route handler for pattern matches
   */
  registerPattern(pattern: RegExp, callback: RouteCallback): void {
    this.patternRoutes.push({ pattern, callback });
  }
  
  /**
   * Navigate to a specific path
   */
  navigate(path: string): void {
    window.history.pushState(null, '', path);
    this.handleRouteChange();
  }
  
  /**
   * Get the current path
   */
  getPath(): string {
    return this.currentPath;
  }
  
  /**
   * Handle route changes
   * This is public to allow manual triggering during initialization
   */
  handleRouteChange(): void {
    // Get current path, defaulting to '/' if on the base URL
    const path = window.location.pathname || '/';
    this.currentPath = path;
    
    // First try exact match
    const exactRoute = this.routes.get(path);
    if (exactRoute) {
      exactRoute();
      return;
    }

    // Then try pattern matches
    for (const { pattern, callback } of this.patternRoutes) {
      if (pattern.test(path)) {
        callback();
        return;
      }
    }

    // If no matches found, fall back to home route
    const homeRoute = this.routes.get('/');
    if (homeRoute) {
      homeRoute();
    }
  }
}

// Create and export a singleton router instance
export const router = new Router(); 