---
description: HypoDossier Best-Practices for writing tests
globs:
alwaysApply: true
---

  - Always use V<PERSON><PERSON> (not Je<PERSON>) for all test files and examples in this project.

  - Use this pattern to mock existing modules:
 @tests-actual-module-import-template.ts

  - use this snapshot test with component rendering when doing refactoring to guarantee the same output after refactoring:
  @tests-snapshot-component-template.ts

  - do not mock all the imported modules

  - if component uses useAccountContext wrap test or Storybook story with AccountProvider

  - for internationalisation use 'react-i18next' library that is used in the project

  - to import 'react-query' use `import { QueryClient, QueryClientProvider } from ‘@tanstack/react-query';`

  - for Storybook tests by default use stories title that starts from 'Dmf\Components'

  - for Storybook tests use this template with decorators and network mock handlers: 
  @tests-storybook-template.tsx
  @tests-storybook-decorators-template.ts
  @tests-storybook-handlers-template.ts




