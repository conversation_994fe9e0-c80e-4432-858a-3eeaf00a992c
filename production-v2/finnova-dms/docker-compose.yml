version: '3.8'
services:
  dec:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.157.1

    command: python manage.py dossier_event_consumer_v2
    secrets:
      - source: DMS_ENV_V16
        target: /app/.env
      - source: PSQL_CA
        target: /root/.postgresql/root.crt
    deploy:
      replicas: 1
      placement:
        constraints: [ node.labels.application == true ]

  diew:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.157.1

    command: python manage.py image_exporter_worker
    secrets:
      - source: DMS_ENV_V16
        target: /app/.env
      - source: PSQL_CA
        target: /root/.postgresql/root.crt
    deploy:
      replicas: 1
      placement:
        constraints: [ node.labels.application == true ]

  worker:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.157.1

    command: python manage.py worker
    secrets:
      - source: DMS_ENV_V16
        target: /app/.env
      - source: PSQL_CA
        target: /root/.postgresql/root.crt
    deploy:
      replicas: 1
      placement:
        constraints: [ node.labels.application == true ]

  s3proxy:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.157.1

    command: sanic s3proxy.proxy -H 0.0.0.0 --debug
    deploy:
      replicas: 4
      placement:
        constraints: [ node.labels.application == true ]
        max_replicas_per_node: 1
    networks:
      caddy-finnova:
    secrets:
      - source: DMS_ENV_V16
        target: /app/.env

  dms:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.157.1

    networks:
      caddy-finnova:
    deploy:
      replicas: 4
      placement:
        constraints: [ node.labels.application == true ]
        max_replicas_per_node: 2
    secrets:
      - source: DMS_ENV_V16
        target: /app/.env
      - source: PSQL_CA
        target: /root/.postgresql/root.crt
    healthcheck:
      test: curl --fail http://127.0.0.1:8000/api/docs || exit 1
      start_period: 30s
      interval: 5s
      timeout: 5s
      retries: 20

secrets:
  DMS_ENV_V16:
    file: ./DMS_ENV.env
  PSQL_CA:
    file: ./root.crt
networks:
  default:
  caddy-finnova:
    external: true