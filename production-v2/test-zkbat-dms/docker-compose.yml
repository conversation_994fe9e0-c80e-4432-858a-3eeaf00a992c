version: '3.8'
services:
  dec:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.145.3
    command: python manage.py dossier_event_consumer_v2
    secrets:
      - source: DMS_ENV_V26
        target: /app/.env
      - source: PSQL_CA
        target: /root/.postgresql/root.crt
    deploy:
      replicas: 1
      placement:
        constraints: [ node.labels.application == true ]

  diew:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.145.3
    command: python manage.py image_exporter_worker
    secrets:
      - source: DMS_ENV_V26
        target: /app/.env
      - source: PSQL_CA
        target: /root/.postgresql/root.crt
    deploy:
      replicas: 1
      placement:
        constraints: [ node.labels.application == true ]

  worker:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.145.3
    command: python manage.py worker
    secrets:
      - source: DMS_ENV_V26
        target: /app/.env
      - source: PSQL_CA
        target: /root/.postgresql/root.crt
    deploy:
      replicas: 1
      placement:
        constraints: [ node.labels.application == true ]

  s3proxy:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.145.3
    command: sanic s3proxy.proxy -H 0.0.0.0
    deploy:
      replicas: 3
      placement:
        constraints: [ node.labels.application == true ]
        max_replicas_per_node: 1
    networks:
      caddy:
    secrets:
      - source: DMS_ENV_V26
        target: /app/.env

  dms:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:v1.145.3
    networks:
      caddy:
      proxy_access:
    # this part is needed to register the service at the reverse proxy
    deploy:
      replicas: 3
      placement:
        constraints: [ node.labels.application == true ]
        max_replicas_per_node: 1
    secrets:
      - source: DMS_ENV_V26
        target: /app/.env
      - source: PSQL_CA
        target: /root/.postgresql/root.crt
      - source: CLIENT_CERT_V9
        target: /app/certs/client.crt
      - source: CLIENT_KEY_v1
        target: /app/certs/client.key
    healthcheck:
      test: curl --fail http://127.0.0.1:8000/api/docs || exit 1
      start_period: 30s
      interval: 5s
      timeout: 5s
      retries: 20

secrets:
  DMS_ENV_V26:
    file: ./DMS_ENV.env
  PSQL_CA:
    file: ./root.crt
  CLIENT_CERT_V9:
    file: ./c2.crt
  CLIENT_KEY_v1:
    file: ./c2.key
networks:
  default:
  caddy:
    external: true
  proxy_access:
    external: true