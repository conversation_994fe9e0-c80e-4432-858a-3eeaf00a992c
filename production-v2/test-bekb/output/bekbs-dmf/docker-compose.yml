version: '3.8'
services:
  dossier-frontend:
    image: registry.gitlab.com/hypodossier/dossier-manager-frontend:v1.117.1
    configs:
    - source: KEYCLOAK_CONFIG_V3
      target: /srv/build/keycloak.json
    - source: MANAGER_CONFIG_V7
      target: /srv/build/config/config.json
    networks:
      caddy: null
    deploy:
      replicas: 1
      placement:
        constraints:
        - node.labels.application == true
        max_replicas_per_node: 1
      labels:
        caddy: bekbs.test.hypodossier.ch
        caddy.@vpn.remote_ip: forwarded **************/32
        caddy.0_reverse_proxy: '@vpn {{upstreams 80}}'
        caddy.@bekb.remote_ip: forwarded *************/32 *************/32 **************/32
          *************/32 *************/32
        caddy.1_reverse_proxy: '@bekb {{upstreams 80}}'
        caddy.import: tls
    healthcheck:
      test: wget -O /dev/null -q 127.0.0.1:2019/config
      start_period: 5s
      interval: 5s
      timeout: 5s
      retries: 20
  dossier-frontend-fipla:
    image: registry.gitlab.com/hypodossier/dossier-manager-frontend:v1.117.1
    configs:
    - source: KEYCLOAK_CONFIG_FIPLA_V1
      target: /srv/build/keycloak.json
    - source: MANAGER_CONFIG_V7
      target: /srv/build/config/config.json
    networks:
      caddy: null
    deploy:
      replicas: 1
      placement:
        constraints:
        - node.labels.application == true
        max_replicas_per_node: 1
      labels:
        caddy: fipla.bekbs.test.hypodossier.ch
        caddy.@vpn.remote_ip: forwarded **************/32
        caddy.0_reverse_proxy: '@vpn {{upstreams 80}}'
        caddy.@bekb.remote_ip: forwarded *************/32 *************/32 **************/32
          *************/32 *************/32
        caddy.1_reverse_proxy: '@bekb {{upstreams 80}}'
        caddy.import: tls
    healthcheck:
      test: wget -O /dev/null -q 127.0.0.1:2019/config
      start_period: 5s
      interval: 5s
      timeout: 5s
      retries: 20
networks:
  caddy:
    external: true
configs:
  KEYCLOAK_CONFIG_V3:
    file: keycloak.json
  KEYCLOAK_CONFIG_FIPLA_V1:
    file: keycloak_fipla.json
  MANAGER_CONFIG_V7:
    file: config.json
