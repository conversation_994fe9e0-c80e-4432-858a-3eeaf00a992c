#%% md
# Analyse cuts of test set
#%%
import pandas as pd
import numpy as np

import matplotlib.pyplot as plt

from sklearn.metrics import f1_score, accuracy_score
#%%
file_name = "../output/dainty-sound-79/predictions/prediction_dainty-sound-79_test.csv"
data_df = pd.read_csv(file_name)
#%%
data_df.head()
#%%
labels = [[int(char) for char in row['labels'].replace('[', '').replace(']', '').split()] for index, row in data_df.iterrows()]
labels = np.array([item for sublist in labels for item in sublist])
print("shape labels", np.shape(labels))
#%%
preds = [[float(char) for char in row['preds'].replace('[', '').replace(']', '').split()] for index, row in data_df.iterrows()]
preds = np.array([item for sublist in preds for item in sublist])
print("shape preds", np.shape(preds))
#%%
def count_cuts(predictions, labels, threshold=.5, verbose=True):
    n_entry = len(predictions)
    n_label_cuts = np.sum(labels)
    y_preds = np.array([1 if pred >= threshold else 0 for pred in predictions])
    n_pred_cuts = np.sum(y_preds)

    # compute scores
    f1 = f1_score(labels, y_preds, average='binary')
    acc = accuracy_score(labels, y_preds)
    
    # count extra cuts
    extra_cuts = [1 if pred == 1 and label == 0 else 0 for pred, label in zip(y_preds, labels)]
    n_extra_cuts = np.sum(extra_cuts)

    # count missed cuts
    missed_cuts = [1 if pred == 0 and label == 1 else 0 for pred, label in zip(y_preds, labels)]
    n_missed_cuts = np.sum(missed_cuts)
    
    if verbose:
        print(f"[count_cuts] threshold: {threshold}")
        print(f"[count_cuts] n_true_cuts {n_label_cuts} ({(n_label_cuts/n_entry*100):.2f}% of total)")
        print(f"[count_cuts] n_pred_cuts {n_pred_cuts} ({(n_pred_cuts/n_entry*100):.2f}% of total)")
        print(f"[count_cuts] f1 {f1:.3f}")
        print(f"[count_cuts] acc {acc*100:.2f}%")
        print(f"[count_cuts] n_extra_cuts {n_extra_cuts} ({(n_extra_cuts/n_pred_cuts*100):.2f}% of all cuts)")
        print(f"[count_cuts] n_missed_cuts {n_missed_cuts} ({(n_missed_cuts/n_pred_cuts*100):.2f}% of all cuts)")

    return f1, acc, n_pred_cuts, n_extra_cuts, n_missed_cuts
#%%
count_cuts(preds, labels)
#%%
count_cuts(preds, labels, threshold=0.8)
#%%
thresholds = np.linspace(0, 1, num=21)

results = []
for thresh in thresholds:
    f1, acc, n_pred_cuts, n_extra_cuts, n_missed_cuts = count_cuts(preds, labels, threshold=thresh, verbose=False)
    results.append({'thresh': thresh, 'f1': f1, 'n_cuts': n_pred_cuts, 'n_extra_cuts': n_extra_cuts})

results_df = pd.DataFrame(results)

# create plot
f, ax1 = plt.subplots()
ax2 = ax1.twinx()

results_df.plot(x='thresh', y='f1', ax=ax1)
results_df.plot(x='thresh', y='n_cuts', ylim=[0, 5000], ax=ax2, color='orange')
results_df.plot(x='thresh', y='n_extra_cuts', ax=ax2, color='green')

n_true_cuts = np.sum(labels)
print('n_true_cuts', n_true_cuts)
ax2.plot(thresholds, [n_true_cuts]*len(thresholds), '--k')
ax2.legend(loc='lower left')

#%%
best_f1 = np.max(results_df['f1'])
best_thresh = np.amax(results_df['f1'])
print("best_f1", best_f1)
print("best_thresh", best_thresh)