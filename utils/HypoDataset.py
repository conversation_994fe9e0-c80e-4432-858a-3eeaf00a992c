import pandas as pd
import numpy as np

from PIL import Image
import torch
from torch.utils.data import Dataset
from torchvision import transforms
from transformers import LayoutLMTokenizer
from pathlib import Path

from utils.PDFFeatureExtractor import apply_pdfplumber

SUPPORTED_IMG_FORMATS = ["png", "jpg", "jpeg"]


class HypoDataset(Dataset):
    def __init__(self, config, df):
        self.metadata_df = pd.DataFrame(df).reset_index(drop=True)
        self.config = config
        self.data_path = (
            Path(config["data_path"]) if "data_path" in config else Path("")
        )
        self.max_seq_length = config["max_seq_length"]

        if config["ablation"] != "language":
            # declare tokenizer
            self.tokenizer = LayoutLMTokenizer.from_pretrained(
                "microsoft/layoutlm-base-uncased"
            )

    def __getitem__(self, idx):
        file_path = self.data_path / self.metadata_df.iloc[idx]["pdf_path"]
        extension = file_path.as_posix().split(".")[-1]

        # read pdf file
        if extension == "pdf":
            img, words, normalized_boxes = apply_pdfplumber(file_path, page_number=0)
        # if we use only vision we can work directly with png
        elif (
            extension in SUPPORTED_IMG_FORMATS and self.config["ablation"] == "language"
        ):
            img = Image.open(file_path)

        # transform image
        img = self._preprocess_image(img)

        # vision and language
        if self.config["ablation"] != "language":
            # tokenize words
            words, token_boxes = self._extend_box(words, normalized_boxes)
            # Add bounding boxes of cls + sep tokens
            token_boxes = [[0, 0, 0, 0]] + token_boxes + [[1000, 1000, 1000, 1000]]
            encoding = self.tokenizer(
                " ".join(words),
                return_tensors="pt",
                padding=True,
                pad_to_multiple_of=self.max_seq_length,
            )
            input_ids = torch.squeeze(encoding["input_ids"], 0)
            attention_mask = torch.squeeze(encoding["attention_mask"], 0)
            token_type_ids = torch.squeeze(encoding["token_type_ids"], 0)

            # Pad/cut token_boxes with [0, 0, 0, 0] or cut the elements beyond {max_seq_length}
            pad_box = [0, 0, 0, 0]
            if len(token_boxes) <= self.max_seq_length:
                token_boxes = torch.tensor(
                    np.array(
                        token_boxes
                        + (self.max_seq_length - len(token_boxes)) * [pad_box]
                    )
                )
            else:
                token_boxes = torch.tensor(
                    np.array(
                        token_boxes[: self.max_seq_length - 1]
                        + [[1000, 1000, 1000, 1000]]
                    )
                )
                input_ids = input_ids[: self.max_seq_length]
                input_ids[-1] = 102
                attention_mask = attention_mask[: self.max_seq_length]
                token_type_ids = token_type_ids[: self.max_seq_length]
            bbox = token_boxes

            return {
                "input_ids": input_ids,
                "bbox": bbox,
                "attention_mask": attention_mask,
                "token_type_ids": token_type_ids,
                "label": torch.tensor(self.metadata_df.iloc[idx]["label"]),
                "image": img,
                "pdf_path": file_path,
            }

        # only vision
        else:
            return {
                "label": torch.tensor(self.metadata_df.iloc[idx]["label"]),
                "image": img,
                "pdf_path": file_path,
            }

    def __len__(self):
        return len(self.metadata_df.index)

    def _preprocess_image(self, image):
        preprocess = transforms.Compose(
            [
                transforms.Resize(self.config["input_img_size"]),
                transforms.Grayscale(num_output_channels=3),
                transforms.ToTensor(),
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]
                ),
            ]
        )
        return preprocess(image)

    def _extend_box(self, words, boxes):
        token_boxes = []
        tokenizable_words = words
        j = 0  # index for tokenizable words
        for i in range(len(words)):
            word, box = words[i], boxes[i]
            try:
                word_tokens = self.tokenizer.tokenize(word)
                token_boxes.extend([box] * len(word_tokens))
                j += 1
            except:
                tokenizable_words = np.delete(tokenizable_words, j)

        return tokenizable_words, token_boxes
